<p align=center>
<a href="https://superagi.co"><img src=https://superagi.co/wp-content/uploads/2023/05/SuperAGI_icon.png></a>
</p>

# SuperAGI Web Scraper Tool

The SuperAGI Webscraper Tool lets users perform web scraping, extracting URLs and retrieving the textual content from websites.

## ⚙️ Installation

### 🛠 **Setting Up of SuperAGI**
Set up the SuperAGI by following the instructions given (https://github.com/TransformerOptimus/SuperAGI/blob/main/README.MD)

You'll be able to use the Web Scraper Tool on the fly once you have setup SuperAGI.

## Running SuperAGI Web Scraper Tool

You can simply ask your agent to read or go through a certain website or URL, and it'll be able to retrieve it's textual information from there.