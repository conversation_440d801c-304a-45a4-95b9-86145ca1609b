#  ⚡ Contributing to SuperAGI
<p align="center">
  <a href="https://superagi.com//#gh-light-mode-only">
    <img src="https://superagi.com/wp-content/uploads/2023/05/Logo-dark.svg" width="318px" alt="SuperAGI logo" />
  </a>
  <a href="https://superagi.com//#gh-dark-mode-only">
    <img src="https://superagi.com/wp-content/uploads/2023/05/Logo-light.svg" width="318px" alt="SuperAGI logo" />
  </a>
</p>

First of all, thank you for taking the time to contribute to this project. We truly appreciate your contributions, whether it's bug reports, feature suggestions, or pull requests. Your time and effort are highly valued in this project. 🚀

This document provides guidelines and best practices to help you to contribute effectively. These are meant to serve as guidelines, not strict rules. We encourage you to use your best judgment and feel comfortable proposing changes to this document through a pull request.

For all contributions, a CLA (Contributor License Agreement) needs to be signed
[here](https://cla-assistant.io/TransformerOptimus/SuperAGI) before (or after) the pull request has been submitted.

**********************************Table of Content:********************************** 
1. [Code of conduct](https://github.com/TransformerOptimus/SuperAGI/blob/CONTRIBUTING.md#code-of-conduct) 
2. [Quick Start](https://github.com/TransformerOptimus/SuperAGI/blob/CONTRIBUTING.md#quick-start)
3. [Contributing Guidelines](https://github.com/TransformerOptimus/SuperAGI/blob/CONTRIBUTING.md#contributing-guidelines)
    1. [Reporting Bugs](https://github.com/TransformerOptimus/SuperAGI/blob/CONTRIBUTING.md#reporting-bugs)
    2. [New Feature or Suggesting Enhancements](https://github.com/TransformerOptimus/SuperAGI/blob/CONTRIBUTING.md#new-feature-or-suggesting-enhancements)
4. [Testing](https://github.com/TransformerOptimus/SuperAGI/blob/CONTRIBUTING.md#testing-changes)
5. [Pull Requests](https://github.com/TransformerOptimus/SuperAGI/blob/CONTRIBUTING.md#pull-requests)

## ✔️ Code of Conduct:

Please read our [Code of Conduct](https://github.com/TransformerOptimus/SuperAGI/blob/main/CODE_OF_CONDUCT.md) to understand the expectations we have for all contributors participating in this project. By participating, you agree to abide by our Code of Conduct.

## 🚀 Quick Start

You can quickly get started with contributing by searching for issues with the labels **"Good First Issue"** or **"Help Needed"** in the [Issues Section](https://github.com/TransformerOptimus/SuperAGI/Issues). If you think you can contribute, comment on the issue and we will assign it to you.  

To set up your development environment, please follow the steps mentioned below : 

1. Fork the repository and create a clone of the fork
2. Create a branch for a feature or a bug you are working on in your fork
3. Once you've created your branch, follow the instructions in the [README.MD](https://github.com/TransformerOptimus/SuperAGI/README.MD)

## Contributing Guidelines 
 
### 🔍 Reporting Bugs

You can start working on an existing bug that has been reported and labeled as **"Bug"** in the Issues Section, and you can report your bugs in the following manner :

1. Title describing the issue clearly and concisely with relevant labels
2. Provide a detailed description of the problem and the necessary steps to reproduce the issue.
3. Include any relevant logs, screenshots, or other helpful information supporting the issue.

### :bulb: New Feature or Suggesting Enhancements

This section guides you through working on an enhancement **Including a completely New Feature** & **Enhancements to an existing functionality**. 

Before getting started, Perform a search on Issues to see if the enhancement or feature has already been suggested and picked up. If the feature or enhancement is suggested and not picked up, comment on the issue and assign yourself to it. 

If the feature or enhancement is not in the issues, find out whether your idea fits with the scope and aims of the project by looking at the [Roadmap](https://github.com/users/TransformerOptimus/projects/5/). If yes, raise an issue with the label **"Feature Request"** in the following manner: 

1. Title describing the feature or enhancement in a clear and concise manner
2. Clearly describe the proposed enhancement, highlighting its benefits and potential drawbacks.
3. Provide examples and supporting information.

Once you have raised the issue and have gotten yourself assigned, you can start working on the feature or enhancement. Please make sure the feature or enhancement you're working on is placed on the Roadmap.

## Testing your Changes

Each method or the function of the code should have a unit test with the maximum coverage possible and on each Pull Request, we have GitHub Actions triggered, which
runs all the unit tests where all the tests should pass for merging the Pull Request. 

## Pull Request

Now that you have worked on your code and tested it thoroughly, you can now go ahead and raise the pull request. Please make sure that the Pull Request adheres to the following guidelines: 

1.  The pull request is atomic and focuses on a single change.
2.  You have read the contributing guide and your code conforms to the guidelines.
3.  You have documented your changes clearly and comprehensively.
4.  You have added the required tests.


