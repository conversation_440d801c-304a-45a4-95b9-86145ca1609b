"""
Advanced Market Data Crawler
Collects real-time and historical market data from multiple sources
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
import aiohttp
import ccxt.async_support as ccxt

from ..core.config import BotConfig
from ..database.connection import DatabaseManager


class MarketDataCrawler:
    """
    Advanced market data crawler that collects:
    - Real-time price data
    - Order book data
    - Trading volume
    - Market sentiment indicators
    - Cross-exchange arbitrage opportunities
    """
    
    def __init__(self, config: BotConfig, db_manager: DatabaseManager):
        self.config = config
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self.running = False
        
        # Initialize exchanges
        self.exchanges = self._initialize_exchanges()
        self.crawl_tasks = []
        
    def _initialize_exchanges(self) -> Dict[str, ccxt.Exchange]:
        """Initialize multiple exchanges for data collection"""
        exchanges = {}
        
        # Bybit
        bybit_config = self.config.get_api_key("bybit")
        exchanges['bybit'] = ccxt.bybit({
            'apiKey': bybit_config.get('api_key'),
            'secret': bybit_config.get('api_secret'),
            'sandbox': bybit_config.get('testnet', True),
            'enableRateLimit': True,
        })
        
        # Add other exchanges for comparison
        exchanges['binance'] = ccxt.binance({'enableRateLimit': True})
        exchanges['okx'] = ccxt.okx({'enableRateLimit': True})
        exchanges['coinbase'] = ccxt.coinbasepro({'enableRateLimit': True})
        
        return exchanges
    
    async def start(self):
        """Start the data crawler"""
        if self.running:
            return
            
        self.running = True
        self.logger.info("🔄 Starting advanced market data crawler...")
        
        # Start crawling tasks
        self.crawl_tasks = [
            asyncio.create_task(self._crawl_real_time_data()),
            asyncio.create_task(self._crawl_order_book_data()),
            asyncio.create_task(self._crawl_volume_data()),
            asyncio.create_task(self._crawl_cross_exchange_data()),
            asyncio.create_task(self._calculate_market_indicators()),
        ]
        
        await asyncio.gather(*self.crawl_tasks, return_exceptions=True)
    
    async def stop(self):
        """Stop the data crawler"""
        self.running = False
        
        # Cancel all tasks
        for task in self.crawl_tasks:
            task.cancel()
        
        # Close exchange connections
        for exchange in self.exchanges.values():
            await exchange.close()
            
        self.logger.info("🛑 Market data crawler stopped")
    
    async def _crawl_real_time_data(self):
        """Crawl real-time price data"""
        while self.running:
            try:
                for symbol in self.config.get_trading_pairs():
                    # Get data from primary exchange (Bybit)
                    ticker = await self.exchanges['bybit'].fetch_ticker(symbol)
                    
                    market_data = {
                        'timestamp': datetime.utcnow(),
                        'symbol': symbol,
                        'exchange': 'bybit',
                        'price': ticker['last'],
                        'bid': ticker['bid'],
                        'ask': ticker['ask'],
                        'volume': ticker['baseVolume'],
                        'volume_quote': ticker['quoteVolume'],
                        'high_24h': ticker['high'],
                        'low_24h': ticker['low'],
                        'change_24h': ticker['change'],
                        'change_percent_24h': ticker['percentage'],
                    }
                    
                    # Store in database
                    await self._store_market_data(market_data)
                    
                    # Calculate additional indicators
                    await self._calculate_real_time_indicators(symbol, market_data)
                
                await asyncio.sleep(self.config.data_crawler.market_data_interval)
                
            except Exception as e:
                self.logger.error(f"Error in real-time data crawler: {e}")
                await asyncio.sleep(10)
    
    async def _crawl_order_book_data(self):
        """Crawl order book data for market depth analysis"""
        while self.running:
            try:
                for symbol in self.config.get_trading_pairs():
                    # Get order book
                    order_book = await self.exchanges['bybit'].fetch_order_book(symbol, limit=50)
                    
                    # Calculate order book metrics
                    order_book_data = self._analyze_order_book(symbol, order_book)
                    
                    # Store order book data
                    await self._store_order_book_data(order_book_data)
                
                await asyncio.sleep(30)  # Order book every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in order book crawler: {e}")
                await asyncio.sleep(15)
    
    async def _crawl_volume_data(self):
        """Crawl volume data and calculate volume profiles"""
        while self.running:
            try:
                for symbol in self.config.get_trading_pairs():
                    # Get recent trades
                    trades = await self.exchanges['bybit'].fetch_trades(symbol, limit=100)
                    
                    # Calculate volume profile
                    volume_profile = self._calculate_volume_profile(symbol, trades)
                    
                    # Store volume data
                    await self._store_volume_data(volume_profile)
                
                await asyncio.sleep(60)  # Volume data every minute
                
            except Exception as e:
                self.logger.error(f"Error in volume crawler: {e}")
                await asyncio.sleep(30)
    
    async def _crawl_cross_exchange_data(self):
        """Crawl data across multiple exchanges for arbitrage opportunities"""
        while self.running:
            try:
                arbitrage_opportunities = []
                
                for symbol in self.config.get_trading_pairs():
                    exchange_prices = {}
                    
                    # Get prices from all exchanges
                    for exchange_name, exchange in self.exchanges.items():
                        try:
                            ticker = await exchange.fetch_ticker(symbol)
                            exchange_prices[exchange_name] = {
                                'bid': ticker['bid'],
                                'ask': ticker['ask'],
                                'last': ticker['last'],
                                'volume': ticker['baseVolume']
                            }
                        except Exception:
                            continue
                    
                    # Calculate arbitrage opportunities
                    if len(exchange_prices) >= 2:
                        arb_data = self._calculate_arbitrage_opportunities(symbol, exchange_prices)
                        if arb_data:
                            arbitrage_opportunities.extend(arb_data)
                
                # Store arbitrage data
                if arbitrage_opportunities:
                    await self._store_arbitrage_data(arbitrage_opportunities)
                
                await asyncio.sleep(120)  # Cross-exchange data every 2 minutes
                
            except Exception as e:
                self.logger.error(f"Error in cross-exchange crawler: {e}")
                await asyncio.sleep(60)
    
    async def _calculate_market_indicators(self):
        """Calculate advanced market indicators"""
        while self.running:
            try:
                for symbol in self.config.get_trading_pairs():
                    # Get historical data for calculations
                    ohlcv = await self.exchanges['bybit'].fetch_ohlcv(
                        symbol, '1m', limit=200
                    )
                    
                    if len(ohlcv) < 50:
                        continue
                    
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    
                    # Calculate technical indicators
                    indicators = self._calculate_technical_indicators(df)
                    
                    # Calculate market microstructure indicators
                    microstructure = await self._calculate_microstructure_indicators(symbol)
                    
                    # Combine all indicators
                    market_indicators = {
                        'timestamp': datetime.utcnow(),
                        'symbol': symbol,
                        **indicators,
                        **microstructure
                    }
                    
                    # Store indicators
                    await self._store_market_indicators(market_indicators)
                
                await asyncio.sleep(300)  # Indicators every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error calculating market indicators: {e}")
                await asyncio.sleep(60)
    
    def _analyze_order_book(self, symbol: str, order_book: Dict) -> Dict:
        """Analyze order book for market depth and imbalance"""
        bids = np.array(order_book['bids'])
        asks = np.array(order_book['asks'])
        
        if len(bids) == 0 or len(asks) == 0:
            return {}
        
        # Calculate metrics
        bid_volume = np.sum(bids[:, 1])
        ask_volume = np.sum(asks[:, 1])
        
        # Order book imbalance
        imbalance = (bid_volume - ask_volume) / (bid_volume + ask_volume)
        
        # Weighted mid price
        weighted_bid = np.sum(bids[:, 0] * bids[:, 1]) / bid_volume
        weighted_ask = np.sum(asks[:, 0] * asks[:, 1]) / ask_volume
        weighted_mid = (weighted_bid + weighted_ask) / 2
        
        # Spread metrics
        best_bid = bids[0, 0]
        best_ask = asks[0, 0]
        spread = best_ask - best_bid
        spread_percent = spread / weighted_mid * 100
        
        return {
            'timestamp': datetime.utcnow(),
            'symbol': symbol,
            'bid_volume': bid_volume,
            'ask_volume': ask_volume,
            'imbalance': imbalance,
            'weighted_mid_price': weighted_mid,
            'spread': spread,
            'spread_percent': spread_percent,
            'depth_5_bid': np.sum(bids[:5, 1]),
            'depth_5_ask': np.sum(asks[:5, 1]),
            'depth_10_bid': np.sum(bids[:10, 1]),
            'depth_10_ask': np.sum(asks[:10, 1]),
        }
    
    def _calculate_volume_profile(self, symbol: str, trades: List) -> Dict:
        """Calculate volume profile from recent trades"""
        if not trades:
            return {}
        
        df = pd.DataFrame(trades)
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df['price'] = df['price'].astype(float)
        df['amount'] = df['amount'].astype(float)
        
        # Group by price levels
        price_bins = 50
        df['price_bin'] = pd.cut(df['price'], bins=price_bins)
        
        volume_profile = df.groupby('price_bin').agg({
            'amount': 'sum',
            'price': 'mean'
        }).reset_index()
        
        # Calculate volume-weighted average price (VWAP)
        total_volume = df['amount'].sum()
        total_value = (df['price'] * df['amount']).sum()
        vwap = total_value / total_volume if total_volume > 0 else 0
        
        # Calculate volume distribution metrics
        volume_std = df['amount'].std()
        volume_skew = df['amount'].skew()
        
        return {
            'timestamp': datetime.utcnow(),
            'symbol': symbol,
            'total_volume': total_volume,
            'vwap': vwap,
            'volume_std': volume_std,
            'volume_skew': volume_skew,
            'trade_count': len(trades),
            'avg_trade_size': total_volume / len(trades) if trades else 0,
        }
    
    def _calculate_arbitrage_opportunities(self, symbol: str, exchange_prices: Dict) -> List[Dict]:
        """Calculate arbitrage opportunities between exchanges"""
        opportunities = []
        
        exchanges = list(exchange_prices.keys())
        
        for i, exchange1 in enumerate(exchanges):
            for exchange2 in exchanges[i+1:]:
                price1 = exchange_prices[exchange1]
                price2 = exchange_prices[exchange2]
                
                # Buy on exchange1, sell on exchange2
                if price1['ask'] and price2['bid']:
                    profit_1_to_2 = (price2['bid'] - price1['ask']) / price1['ask'] * 100
                    
                    if profit_1_to_2 > 0.1:  # More than 0.1% profit
                        opportunities.append({
                            'timestamp': datetime.utcnow(),
                            'symbol': symbol,
                            'buy_exchange': exchange1,
                            'sell_exchange': exchange2,
                            'buy_price': price1['ask'],
                            'sell_price': price2['bid'],
                            'profit_percent': profit_1_to_2,
                            'min_volume': min(price1.get('volume', 0), price2.get('volume', 0))
                        })
                
                # Buy on exchange2, sell on exchange1
                if price2['ask'] and price1['bid']:
                    profit_2_to_1 = (price1['bid'] - price2['ask']) / price2['ask'] * 100
                    
                    if profit_2_to_1 > 0.1:  # More than 0.1% profit
                        opportunities.append({
                            'timestamp': datetime.utcnow(),
                            'symbol': symbol,
                            'buy_exchange': exchange2,
                            'sell_exchange': exchange1,
                            'buy_price': price2['ask'],
                            'sell_price': price1['bid'],
                            'profit_percent': profit_2_to_1,
                            'min_volume': min(price1.get('volume', 0), price2.get('volume', 0))
                        })
        
        return opportunities
    
    def _calculate_technical_indicators(self, df: pd.DataFrame) -> Dict:
        """Calculate technical indicators"""
        try:
            import ta
            
            # Moving averages
            df['sma_20'] = ta.trend.sma_indicator(df['close'], window=20)
            df['ema_12'] = ta.trend.ema_indicator(df['close'], window=12)
            df['ema_26'] = ta.trend.ema_indicator(df['close'], window=26)
            
            # Momentum indicators
            df['rsi'] = ta.momentum.rsi(df['close'], window=14)
            df['stoch'] = ta.momentum.stoch(df['high'], df['low'], df['close'])
            
            # Volatility indicators
            bb = ta.volatility.BollingerBands(df['close'])
            df['bb_upper'] = bb.bollinger_hband()
            df['bb_lower'] = bb.bollinger_lband()
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['close'] * 100
            
            # Volume indicators
            df['obv'] = ta.volume.on_balance_volume(df['close'], df['volume'])
            df['volume_sma'] = ta.trend.sma_indicator(df['volume'], window=20)
            
            # MACD
            macd = ta.trend.MACD(df['close'])
            df['macd'] = macd.macd()
            df['macd_signal'] = macd.macd_signal()
            df['macd_histogram'] = macd.macd_diff()
            
            # Get latest values
            latest = df.iloc[-1]
            
            return {
                'sma_20': latest['sma_20'],
                'ema_12': latest['ema_12'],
                'ema_26': latest['ema_26'],
                'rsi': latest['rsi'],
                'stoch': latest['stoch'],
                'bb_upper': latest['bb_upper'],
                'bb_lower': latest['bb_lower'],
                'bb_width': latest['bb_width'],
                'obv': latest['obv'],
                'volume_ratio': latest['volume'] / latest['volume_sma'] if latest['volume_sma'] > 0 else 1,
                'macd': latest['macd'],
                'macd_signal': latest['macd_signal'],
                'macd_histogram': latest['macd_histogram'],
                'price_vs_sma20': (latest['close'] - latest['sma_20']) / latest['sma_20'] * 100 if latest['sma_20'] > 0 else 0,
                'price_vs_bb_upper': (latest['close'] - latest['bb_upper']) / latest['bb_upper'] * 100 if latest['bb_upper'] > 0 else 0,
                'price_vs_bb_lower': (latest['close'] - latest['bb_lower']) / latest['bb_lower'] * 100 if latest['bb_lower'] > 0 else 0,
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {e}")
            return {}
    
    async def _calculate_microstructure_indicators(self, symbol: str) -> Dict:
        """Calculate market microstructure indicators"""
        try:
            # Get recent trades
            trades = await self.exchanges['bybit'].fetch_trades(symbol, limit=100)
            
            if not trades:
                return {}
            
            df = pd.DataFrame(trades)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['price'] = df['price'].astype(float)
            df['amount'] = df['amount'].astype(float)
            
            # Calculate trade direction (simplified)
            df['price_change'] = df['price'].diff()
            df['trade_direction'] = np.where(df['price_change'] > 0, 1, 
                                   np.where(df['price_change'] < 0, -1, 0))
            
            # Buy/sell imbalance
            buy_volume = df[df['trade_direction'] == 1]['amount'].sum()
            sell_volume = df[df['trade_direction'] == -1]['amount'].sum()
            total_volume = buy_volume + sell_volume
            
            buy_sell_imbalance = (buy_volume - sell_volume) / total_volume if total_volume > 0 else 0
            
            # Trade intensity
            time_diff = (df['timestamp'].max() - df['timestamp'].min()).total_seconds()
            trade_intensity = len(df) / time_diff if time_diff > 0 else 0
            
            # Price impact
            price_impact = df['price'].std() / df['price'].mean() * 100 if df['price'].mean() > 0 else 0
            
            return {
                'buy_sell_imbalance': buy_sell_imbalance,
                'trade_intensity': trade_intensity,
                'price_impact': price_impact,
                'avg_trade_size': df['amount'].mean(),
                'trade_size_std': df['amount'].std(),
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating microstructure indicators: {e}")
            return {}
    
    async def _calculate_real_time_indicators(self, symbol: str, market_data: Dict):
        """Calculate real-time indicators from market data"""
        try:
            # Get recent price history from database
            query = """
            SELECT * FROM market_data 
            WHERE symbol = $1 AND timestamp > $2 
            ORDER BY timestamp DESC LIMIT 100
            """
            
            cutoff_time = datetime.utcnow() - timedelta(hours=2)
            recent_data = await self.db_manager.fetch_all(query, symbol, cutoff_time)
            
            if len(recent_data) < 10:
                return
            
            # Convert to DataFrame
            df = pd.DataFrame(recent_data)
            
            # Calculate momentum
            price_change_1m = (market_data['price'] - df.iloc[-2]['price']) / df.iloc[-2]['price'] * 100 if len(df) >= 2 else 0
            price_change_5m = (market_data['price'] - df.iloc[-6]['price']) / df.iloc[-6]['price'] * 100 if len(df) >= 6 else 0
            price_change_15m = (market_data['price'] - df.iloc[-16]['price']) / df.iloc[-16]['price'] * 100 if len(df) >= 16 else 0
            
            # Calculate volatility
            returns = df['price'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(60)  # Annualized volatility
            
            # Store real-time indicators
            rt_indicators = {
                'timestamp': market_data['timestamp'],
                'symbol': symbol,
                'price_change_1m': price_change_1m,
                'price_change_5m': price_change_5m,
                'price_change_15m': price_change_15m,
                'volatility': volatility,
                'volume_spike': market_data['volume'] / df['volume'].mean() if df['volume'].mean() > 0 else 1,
            }
            
            await self._store_realtime_indicators(rt_indicators)
            
        except Exception as e:
            self.logger.error(f"Error calculating real-time indicators: {e}")
    
    async def _store_market_data(self, data: Dict):
        """Store market data in database"""
        query = """
        INSERT INTO market_data (timestamp, symbol, exchange, price, bid, ask, volume, volume_quote, 
                               high_24h, low_24h, change_24h, change_percent_24h)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        """
        
        await self.db_manager.execute(
            query,
            data['timestamp'], data['symbol'], data['exchange'], data['price'],
            data['bid'], data['ask'], data['volume'], data['volume_quote'],
            data['high_24h'], data['low_24h'], data['change_24h'], data['change_percent_24h']
        )
    
    async def _store_order_book_data(self, data: Dict):
        """Store order book data in database"""
        if not data:
            return
            
        query = """
        INSERT INTO order_book_data (timestamp, symbol, bid_volume, ask_volume, imbalance,
                                   weighted_mid_price, spread, spread_percent, depth_5_bid,
                                   depth_5_ask, depth_10_bid, depth_10_ask)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        """
        
        await self.db_manager.execute(
            query,
            data['timestamp'], data['symbol'], data['bid_volume'], data['ask_volume'],
            data['imbalance'], data['weighted_mid_price'], data['spread'], data['spread_percent'],
            data['depth_5_bid'], data['depth_5_ask'], data['depth_10_bid'], data['depth_10_ask']
        )
    
    async def _store_volume_data(self, data: Dict):
        """Store volume data in database"""
        if not data:
            return
            
        query = """
        INSERT INTO volume_data (timestamp, symbol, total_volume, vwap, volume_std,
                               volume_skew, trade_count, avg_trade_size)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        """
        
        await self.db_manager.execute(
            query,
            data['timestamp'], data['symbol'], data['total_volume'], data['vwap'],
            data['volume_std'], data['volume_skew'], data['trade_count'], data['avg_trade_size']
        )
    
    async def _store_arbitrage_data(self, opportunities: List[Dict]):
        """Store arbitrage opportunities in database"""
        query = """
        INSERT INTO arbitrage_opportunities (timestamp, symbol, buy_exchange, sell_exchange,
                                           buy_price, sell_price, profit_percent, min_volume)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        """
        
        for opp in opportunities:
            await self.db_manager.execute(
                query,
                opp['timestamp'], opp['symbol'], opp['buy_exchange'], opp['sell_exchange'],
                opp['buy_price'], opp['sell_price'], opp['profit_percent'], opp['min_volume']
            )
    
    async def _store_market_indicators(self, data: Dict):
        """Store market indicators in database"""
        query = """
        INSERT INTO market_indicators (timestamp, symbol, sma_20, ema_12, ema_26, rsi, stoch,
                                     bb_upper, bb_lower, bb_width, obv, volume_ratio, macd,
                                     macd_signal, macd_histogram, price_vs_sma20, price_vs_bb_upper,
                                     price_vs_bb_lower, buy_sell_imbalance, trade_intensity,
                                     price_impact, avg_trade_size, trade_size_std)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23)
        """
        
        await self.db_manager.execute(
            query,
            data['timestamp'], data['symbol'], data.get('sma_20'), data.get('ema_12'),
            data.get('ema_26'), data.get('rsi'), data.get('stoch'), data.get('bb_upper'),
            data.get('bb_lower'), data.get('bb_width'), data.get('obv'), data.get('volume_ratio'),
            data.get('macd'), data.get('macd_signal'), data.get('macd_histogram'),
            data.get('price_vs_sma20'), data.get('price_vs_bb_upper'), data.get('price_vs_bb_lower'),
            data.get('buy_sell_imbalance'), data.get('trade_intensity'), data.get('price_impact'),
            data.get('avg_trade_size'), data.get('trade_size_std')
        )
    
    async def _store_realtime_indicators(self, data: Dict):
        """Store real-time indicators in database"""
        query = """
        INSERT INTO realtime_indicators (timestamp, symbol, price_change_1m, price_change_5m,
                                       price_change_15m, volatility, volume_spike)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        """
        
        await self.db_manager.execute(
            query,
            data['timestamp'], data['symbol'], data['price_change_1m'], data['price_change_5m'],
            data['price_change_15m'], data['volatility'], data['volume_spike']
        )
    
    async def get_latest_market_data(self, symbol: str) -> Optional[Dict]:
        """Get the latest market data for a symbol"""
        query = """
        SELECT * FROM market_data 
        WHERE symbol = $1 
        ORDER BY timestamp DESC 
        LIMIT 1
        """
        
        result = await self.db_manager.fetch_one(query, symbol)
        return dict(result) if result else None
    
    async def get_market_indicators(self, symbol: str, limit: int = 100) -> List[Dict]:
        """Get market indicators for a symbol"""
        query = """
        SELECT * FROM market_indicators 
        WHERE symbol = $1 
        ORDER BY timestamp DESC 
        LIMIT $2
        """
        
        results = await self.db_manager.fetch_all(query, symbol, limit)
        return [dict(row) for row in results]
    
    async def get_arbitrage_opportunities(self, min_profit: float = 0.5) -> List[Dict]:
        """Get current arbitrage opportunities"""
        query = """
        SELECT * FROM arbitrage_opportunities 
        WHERE timestamp > $1 AND profit_percent > $2
        ORDER BY profit_percent DESC
        """
        
        cutoff_time = datetime.utcnow() - timedelta(minutes=5)
        results = await self.db_manager.fetch_all(query, cutoff_time, min_profit)
        return [dict(row) for row in results]
