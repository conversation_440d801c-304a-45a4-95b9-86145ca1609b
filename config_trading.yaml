# AUTONOMOUS BYBIT TRADING BOT - PRODUCTION CONFIGURATION
# REAL TRADING ONLY - NO SIMULATIONS

# ================================
# DATABASE CONFIGURATION (REAL DATA ONLY)
# ================================
database:
  host: 'localhost'
  port: 5432
  name: 'bybit_trading_bot'
  user: 'postgres'
  password: 'postgres'  # Standard postgres password
  ssl_mode: 'prefer'
  echo: false

# ================================
# BYBIT API CONFIGURATION (REAL TRADING)
# ================================
api_keys:
  bybit:
    api_key: 'YOUR_REAL_BYBIT_API_KEY'
    api_secret: 'YOUR_REAL_BYBIT_SECRET'
    testnet: false  # PRODUCTION MODE ONLY
    base_url: 'https://api.bybit.com'
    websocket_url: 'wss://stream.bybit.com/v5/public/linear'

# ================================
# TRADING CONFIGURATION (REAL MONEY)
# ================================
trading:
  enabled: true  # REAL TRADING ENABLED
  environment: 'production'  # NO TESTNET
  paper_trading: false  # NO SIMULATION
  debug_mode: false  # PRODUCTION MODE
  
  # Real money management
  max_position_size: 0.25  # 25% of account
  max_daily_loss: 0.10     # 10% max daily loss
  risk_per_trade: 0.02     # 2% risk per trade
  leverage_range: [1, 10]  # Real leverage
  
  # Trading pairs (real markets)
  symbols:
    - "BTCUSDT"
    - "ETHUSDT"
    - "SOLUSDT"
    - "ADAUSDT"
    - "DOTUSDT"
  
  # Execution settings
  order_timeout: 30
  slippage_tolerance: 0.005
  trading_cycle_interval: 10  # 10 seconds for real-time

# ================================
# AI CONFIGURATION (REAL LEARNING)
# ================================
ai:
  learning_enabled: true
  self_healing_enabled: true
  meta_learning_enabled: true
  model_selection_enabled: true
  autonomy_enabled: true
  
  # Real-time AI settings
  retrain_interval: 3600  # 1 hour
  prediction_horizon: 300  # 5 minutes
  feature_lookback: 1000   # Real data points
  
  models:
    primary: 'neural_network'
    secondary: 'random_forest'
    ensemble_enabled: true

# ================================
# RISK MANAGEMENT (REAL PROTECTION)
# ================================
risk:
  max_drawdown: 0.20        # 20% max drawdown
  stop_loss_percentage: 0.05 # 5% stop loss
  take_profit_percentage: 0.15 # 15% take profit
  emergency_stop_loss: 0.25  # 25% emergency stop
  var_confidence: 0.95       # 95% VaR
  max_correlation: 0.8       # Position correlation limit

# ================================
# DATA CRAWLER (REAL DATA SOURCES)
# ================================
data_crawler:
  market_data_interval: 1    # 1 second real-time
  news_interval: 60          # 1 minute news updates
  social_sentiment_interval: 30 # 30 seconds social data
  economic_data_interval: 300   # 5 minutes economic data
  
  # Real data sources
  news_sources:
    - "cryptonews"
    - "coindesk" 
    - "cointelegraph"
    - "bloomberg"
  
  social_keywords:
    - "bitcoin"
    - "ethereum"
    - "crypto"
    - "bybit"
    - "trading"

# ================================
# PERFORMANCE TRACKING (REAL METRICS)
# ================================
performance:
  track_real_pnl: true
  save_all_trades: true
  calculate_real_metrics: true
  benchmark_against_hodl: true
  
  metrics:
    - "total_pnl"
    - "win_rate"
    - "sharpe_ratio"
    - "max_drawdown"
    - "profit_factor"
    - "calmar_ratio"

# ================================
# HARDWARE MONITORING (REAL SYSTEM)
# ================================
hardware:
  check_interval: 30  # 30 seconds
  cpu_temp_threshold: 80
  memory_threshold: 85
  disk_threshold: 90
  auto_shutdown_on_critical: true

# ================================
# LOGGING (PRODUCTION LOGGING)
# ================================
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/trading_bot.log"
  max_file_size: "100MB"
  backup_count: 10
  log_trades: true
  log_errors: true
  log_performance: true

# ================================
# API SERVER (REAL MONITORING)
# ================================
api:
  host: "0.0.0.0"
  port: 8000
  debug: false  # Production mode
  workers: 4

# ================================
# SECURITY (PRODUCTION SECURITY)
# ================================
security:
  jwt_secret: "CHANGE_THIS_IN_PRODUCTION_12345678901234567890"
  jwt_expiry_hours: 24
  rate_limit_per_minute: 120
  enable_api_auth: true

# ================================
# NOTIFICATIONS (REAL ALERTS)
# ================================
notifications:
  enabled: true
  channels:
    email:
      enabled: true
      smtp_host: "smtp.gmail.com"
      smtp_port: 587
    slack:
      enabled: false
      webhook_url: ""
    discord:
      enabled: false
      webhook_url: ""
  
  triggers:
    large_profit: 1000    # Alert on $1000+ profit
    large_loss: -500      # Alert on $500+ loss
    system_error: true    # Alert on all errors
    daily_summary: true   # Daily P&L summary

# ================================
# DEVELOPMENT SETTINGS (DISABLED)
# ================================
development:
  paper_trading: false      # NO SIMULATION
  debug_mode: false         # NO DEBUG
  save_predictions: true    # Save real predictions
  backtest_mode: false      # NO BACKTESTING
  simulation_mode: false    # NO SIMULATION
