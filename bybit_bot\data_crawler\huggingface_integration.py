"""
HuggingFace Data Integration for Super GPT Bybit Bot
Fetches and processes relevant financial and trading datasets from HuggingFace Hub
"""

import asyncio
import logging
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from datasets import load_dataset, DatasetDict
from huggingface_hub import Hf<PERSON><PERSON>, list_datasets
import numpy as np
from pathlib import Path

from bybit_bot.database.connection import DatabaseManager

logger = logging.getLogger("bybit_trading_bot.huggingface_integration")


class HuggingFaceDataIntegrator:
    """
    Advanced HuggingFace data integration for trading bot enhancement
    """
    
    def __init__(self, database_manager: DatabaseManager):
        self.db = database_manager
        self.api = HfApi()
        self.local_cache_dir = Path("workspace/huggingface_cache")
        self.local_cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Target categories for financial/trading data
        self.target_categories = [
            "finance", "economics", "time-series", "trading", "crypto", 
            "stocks", "forex", "market-data", "sentiment", "news"
        ]
        
        # Quality thresholds
        self.min_download_count = 100
        self.relevance_threshold = 0.6
        
        logger.info("🤖 HuggingFace Data Integrator initialized")
    
    async def discover_relevant_datasets(self) -> List[Dict[str, Any]]:
        """Discover relevant financial and trading datasets"""
        try:
            logger.info("🔍 Discovering relevant datasets from HuggingFace Hub...")
            
            discovered_datasets = []
            
            # Search for datasets by category
            for category in self.target_categories:
                try:
                    datasets = list_datasets(
                        filter=category,
                        sort="downloads",
                        direction=-1,
                        limit=50
                    )
                    
                    for dataset in datasets:
                        if dataset.downloads and dataset.downloads >= self.min_download_count:
                            relevance_score = self._calculate_relevance_score(dataset)
                            
                            if relevance_score >= self.relevance_threshold:
                                dataset_info = {
                                    'dataset_name': dataset.id.split('/')[-1],
                                    'dataset_id': dataset.id,
                                    'description': getattr(dataset, 'description', ''),
                                    'categories': getattr(dataset, 'tags', []),
                                    'tags': getattr(dataset, 'tags', []),
                                    'download_count': dataset.downloads,
                                    'last_modified': getattr(dataset, 'lastModified', None),
                                    'relevance_score': relevance_score,
                                    'quality_score': self._calculate_quality_score(dataset),
                                    'usability_score': self._calculate_usability_score(dataset)
                                }
                                
                                discovered_datasets.append(dataset_info)
                                
                except Exception as e:
                    logger.warning(f"Error searching category {category}: {e}")
                    continue
            
            # Remove duplicates and sort by relevance
            unique_datasets = {d['dataset_id']: d for d in discovered_datasets}.values()
            sorted_datasets = sorted(unique_datasets, key=lambda x: x['relevance_score'], reverse=True)
            
            logger.info(f"📊 Discovered {len(sorted_datasets)} relevant datasets")
            
            # Store in database
            await self._store_discovered_datasets(sorted_datasets)
            
            return sorted_datasets
            
        except Exception as e:
            logger.error(f"Error discovering datasets: {e}")
            return []
    
    async def download_priority_datasets(self, max_datasets: int = 10) -> List[Dict[str, Any]]:
        """Download and process priority datasets"""
        try:
            logger.info(f"📥 Downloading top {max_datasets} priority datasets...")
            
            # Get top datasets from database
            query = """
                SELECT * FROM huggingface_datasets 
                WHERE status = 'discovered' 
                ORDER BY relevance_score DESC, quality_score DESC, usability_score DESC
                LIMIT $1
            """
            datasets = await self.db.fetch_all(query, max_datasets)
            
            downloaded_datasets = []
            
            for dataset_row in datasets:
                try:
                    dataset_result = await self._download_and_process_dataset(dataset_row)
                    if dataset_result:
                        downloaded_datasets.append(dataset_result)
                        
                except Exception as e:
                    logger.error(f"Error downloading dataset {dataset_row['dataset_id']}: {e}")
                    # Update status to show error
                    await self.db.execute(
                        "UPDATE huggingface_datasets SET status = 'error', error_messages = $1 WHERE id = $2",
                        str(e), dataset_row['id']
                    )
                    continue
            
            logger.info(f"✅ Successfully downloaded {len(downloaded_datasets)} datasets")
            return downloaded_datasets
            
        except Exception as e:
            logger.error(f"Error downloading priority datasets: {e}")
            return []
    
    async def _download_and_process_dataset(self, dataset_row: Dict) -> Optional[Dict[str, Any]]:
        """Download and process a single dataset"""
        try:
            dataset_id = dataset_row['dataset_id']
            logger.info(f"📥 Processing dataset: {dataset_id}")
            
            # Download dataset
            dataset = load_dataset(dataset_id, cache_dir=str(self.local_cache_dir))
            
            # Calculate dataset size
            dataset_size_gb = self._calculate_dataset_size(dataset)
            
            # Update database with download status
            await self.db.execute("""
                UPDATE huggingface_datasets 
                SET status = 'downloaded', 
                    dataset_size_gb = $1,
                    local_path = $2,
                    updated_at = NOW()
                WHERE id = $3
            """, dataset_size_gb, str(self.local_cache_dir / dataset_id), dataset_row['id'])
            
            # Process the dataset for trading insights
            processed_data = await self._process_dataset_for_trading(dataset, dataset_row)
            
            # Update status to processed
            await self.db.execute("""
                UPDATE huggingface_datasets 
                SET status = 'processed',
                    integration_metadata = $1,
                    updated_at = NOW()
                WHERE id = $2
            """, json.dumps(processed_data['metadata']), dataset_row['id'])
            
            return {
                'dataset_info': dataset_row,
                'processed_data': processed_data,
                'dataset_size_gb': dataset_size_gb
            }
            
        except Exception as e:
            logger.error(f"Error processing dataset {dataset_row['dataset_id']}: {e}")
            raise
    
    async def _process_dataset_for_trading(self, dataset: DatasetDict, dataset_info: Dict) -> Dict[str, Any]:
        """Process dataset to extract trading-relevant information"""
        try:
            processed_data = {
                'metadata': {
                    'processing_timestamp': datetime.utcnow().isoformat(),
                    'splits_processed': list(dataset.keys()),
                    'total_rows': 0,
                    'columns_found': [],
                    'trading_relevance': {}
                },
                'extracted_data': []
            }
            
            # Process each split in the dataset
            for split_name, split_data in dataset.items():
                if len(split_data) == 0:
                    continue
                    
                processed_data['metadata']['total_rows'] += len(split_data)
                
                # Get column information
                if hasattr(split_data, 'column_names'):
                    columns = split_data.column_names
                    processed_data['metadata']['columns_found'] = columns
                    
                    # Analyze trading relevance of columns
                    trading_columns = self._identify_trading_columns(columns)
                    processed_data['metadata']['trading_relevance'] = trading_columns
                    
                    # Extract sample data for analysis
                    sample_size = min(1000, len(split_data))
                    sample_data = split_data.select(range(sample_size))
                    
                    # Convert to pandas for easier processing
                    df = sample_data.to_pandas()
                    
                    # Extract different types of data
                    extracted = await self._extract_trading_insights(df, trading_columns, dataset_info)
                    processed_data['extracted_data'].extend(extracted)
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Error processing dataset for trading: {e}")
            return {'metadata': {'error': str(e)}, 'extracted_data': []}
    
    async def _extract_trading_insights(self, df: pd.DataFrame, trading_columns: Dict, dataset_info: Dict) -> List[Dict]:
        """Extract specific trading insights from the dataset"""
        insights = []
        
        try:
            # Price data extraction
            if trading_columns.get('price_columns'):
                price_insights = self._extract_price_insights(df, trading_columns['price_columns'])
                insights.extend(price_insights)
            
            # Sentiment data extraction
            if trading_columns.get('sentiment_columns'):
                sentiment_insights = self._extract_sentiment_insights(df, trading_columns['sentiment_columns'])
                insights.extend(sentiment_insights)
            
            # Volume data extraction
            if trading_columns.get('volume_columns'):
                volume_insights = self._extract_volume_insights(df, trading_columns['volume_columns'])
                insights.extend(volume_insights)
            
            # News/text data extraction
            if trading_columns.get('text_columns'):
                text_insights = self._extract_text_insights(df, trading_columns['text_columns'])
                insights.extend(text_insights)
            
            # Store extracted insights in database
            for insight in insights:
                await self._store_processed_insight(insight, dataset_info)
                
        except Exception as e:
            logger.error(f"Error extracting trading insights: {e}")
        
        return insights
    
    def _extract_price_insights(self, df: pd.DataFrame, price_columns: List[str]) -> List[Dict]:
        """Extract price-related insights"""
        insights = []
        
        for col in price_columns:
            if col in df.columns:
                try:
                    prices = pd.to_numeric(df[col], errors='coerce').dropna()
                    if len(prices) > 0:
                        insight = {
                            'data_type': 'price',
                            'symbol': self._infer_symbol_from_data(df),
                            'confidence_score': 0.8,
                            'data_content': {
                                'column_name': col,
                                'mean_price': float(prices.mean()),
                                'std_price': float(prices.std()),
                                'min_price': float(prices.min()),
                                'max_price': float(prices.max()),
                                'price_range': float(prices.max() - prices.min()),
                                'volatility': float(prices.std() / prices.mean()) if prices.mean() != 0 else 0,
                                'sample_size': len(prices)
                            }
                        }
                        insights.append(insight)
                except Exception as e:
                    logger.warning(f"Error processing price column {col}: {e}")
        
        return insights
    
    def _extract_sentiment_insights(self, df: pd.DataFrame, sentiment_columns: List[str]) -> List[Dict]:
        """Extract sentiment-related insights"""
        insights = []
        
        for col in sentiment_columns:
            if col in df.columns:
                try:
                    sentiments = pd.to_numeric(df[col], errors='coerce').dropna()
                    if len(sentiments) > 0:
                        insight = {
                            'data_type': 'sentiment',
                            'symbol': self._infer_symbol_from_data(df),
                            'confidence_score': 0.7,
                            'data_content': {
                                'column_name': col,
                                'mean_sentiment': float(sentiments.mean()),
                                'sentiment_std': float(sentiments.std()),
                                'positive_ratio': float((sentiments > 0).sum() / len(sentiments)),
                                'negative_ratio': float((sentiments < 0).sum() / len(sentiments)),
                                'neutral_ratio': float((sentiments == 0).sum() / len(sentiments)),
                                'sample_size': len(sentiments)
                            }
                        }
                        insights.append(insight)
                except Exception as e:
                    logger.warning(f"Error processing sentiment column {col}: {e}")
        
        return insights
    
    def _extract_volume_insights(self, df: pd.DataFrame, volume_columns: List[str]) -> List[Dict]:
        """Extract volume-related insights"""
        insights = []
        
        for col in volume_columns:
            if col in df.columns:
                try:
                    volumes = pd.to_numeric(df[col], errors='coerce').dropna()
                    if len(volumes) > 0:
                        insight = {
                            'data_type': 'volume',
                            'symbol': self._infer_symbol_from_data(df),
                            'confidence_score': 0.8,
                            'data_content': {
                                'column_name': col,
                                'mean_volume': float(volumes.mean()),
                                'volume_std': float(volumes.std()),
                                'max_volume': float(volumes.max()),
                                'volume_distribution': {
                                    'q25': float(volumes.quantile(0.25)),
                                    'q50': float(volumes.quantile(0.50)),
                                    'q75': float(volumes.quantile(0.75)),
                                    'q95': float(volumes.quantile(0.95))
                                },
                                'sample_size': len(volumes)
                            }
                        }
                        insights.append(insight)
                except Exception as e:
                    logger.warning(f"Error processing volume column {col}: {e}")
        
        return insights
    
    def _extract_text_insights(self, df: pd.DataFrame, text_columns: List[str]) -> List[Dict]:
        """Extract text/news-related insights"""
        insights = []
        
        for col in text_columns:
            if col in df.columns:
                try:
                    texts = df[col].dropna().astype(str)
                    if len(texts) > 0:
                        # Basic text analysis
                        avg_length = texts.str.len().mean()
                        max_length = texts.str.len().max()
                        
                        # Keywords extraction (simple approach)
                        all_text = ' '.join(texts.head(100)).lower()
                        trading_keywords = self._extract_trading_keywords(all_text)
                        
                        insight = {
                            'data_type': 'news',
                            'symbol': self._infer_symbol_from_data(df),
                            'confidence_score': 0.6,
                            'data_content': {
                                'column_name': col,
                                'avg_text_length': float(avg_length),
                                'max_text_length': int(max_length),
                                'trading_keywords': trading_keywords,
                                'sample_size': len(texts)
                            }
                        }
                        insights.append(insight)
                except Exception as e:
                    logger.warning(f"Error processing text column {col}: {e}")
        
        return insights
    
    def _identify_trading_columns(self, columns: List[str]) -> Dict[str, List[str]]:
        """Identify columns relevant to trading"""
        trading_columns = {
            'price_columns': [],
            'volume_columns': [],
            'sentiment_columns': [],
            'text_columns': [],
            'time_columns': []
        }
        
        price_keywords = ['price', 'close', 'open', 'high', 'low', 'value', 'quote', 'usd', 'btc', 'eth']
        volume_keywords = ['volume', 'amount', 'size', 'quantity', 'trade_count']
        sentiment_keywords = ['sentiment', 'score', 'polarity', 'compound', 'positive', 'negative']
        text_keywords = ['text', 'content', 'message', 'title', 'description', 'news', 'tweet']
        time_keywords = ['time', 'date', 'timestamp', 'created', 'updated']
        
        for col in columns:
            col_lower = col.lower()
            
            if any(keyword in col_lower for keyword in price_keywords):
                trading_columns['price_columns'].append(col)
            elif any(keyword in col_lower for keyword in volume_keywords):
                trading_columns['volume_columns'].append(col)
            elif any(keyword in col_lower for keyword in sentiment_keywords):
                trading_columns['sentiment_columns'].append(col)
            elif any(keyword in col_lower for keyword in text_keywords):
                trading_columns['text_columns'].append(col)
            elif any(keyword in col_lower for keyword in time_keywords):
                trading_columns['time_columns'].append(col)
        
        return trading_columns
    
    def _extract_trading_keywords(self, text: str) -> List[str]:
        """Extract trading-relevant keywords from text"""
        trading_terms = [
            'bitcoin', 'btc', 'ethereum', 'eth', 'crypto', 'cryptocurrency',
            'bull', 'bear', 'bullish', 'bearish', 'pump', 'dump',
            'buy', 'sell', 'hold', 'long', 'short', 'margin',
            'profit', 'loss', 'gain', 'trade', 'trading', 'exchange',
            'market', 'price', 'volatility', 'support', 'resistance',
            'trend', 'analysis', 'technical', 'fundamental'
        ]
        
        found_keywords = []
        for term in trading_terms:
            if term in text:
                found_keywords.append(term)
        
        return found_keywords[:10]  # Limit to top 10
    
    def _infer_symbol_from_data(self, df: pd.DataFrame) -> Optional[str]:
        """Try to infer trading symbol from the data"""
        common_symbols = ['BTCUSDT', 'ETHUSDT', 'BTC', 'ETH', 'ADAUSDT', 'XRPUSDT']
        
        # Check column names first
        for col in df.columns:
            col_upper = col.upper()
            for symbol in common_symbols:
                if symbol in col_upper:
                    return symbol
        
        # Check data content (if string columns exist)
        for col in df.select_dtypes(include=['object']).columns[:3]:  # Check first 3 string columns
            try:
                sample_values = df[col].astype(str).str.upper().head(10)
                for symbol in common_symbols:
                    if any(symbol in val for val in sample_values):
                        return symbol
            except:
                continue
        
        return None
    
    async def _store_discovered_datasets(self, datasets: List[Dict[str, Any]]):
        """Store discovered datasets in the database"""
        for dataset in datasets:
            try:
                await self.db.execute("""
                    INSERT INTO huggingface_datasets 
                    (dataset_name, dataset_id, description, categories, tags, download_count, 
                     last_modified, relevance_score, quality_score, usability_score)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    ON CONFLICT (dataset_id) DO UPDATE SET
                        download_count = EXCLUDED.download_count,
                        relevance_score = EXCLUDED.relevance_score,
                        quality_score = EXCLUDED.quality_score,
                        usability_score = EXCLUDED.usability_score,
                        updated_at = NOW()
                """, 
                    dataset['dataset_name'],
                    dataset['dataset_id'],
                    dataset['description'],
                    dataset['categories'],
                    dataset['tags'],
                    dataset['download_count'],
                    dataset['last_modified'],
                    dataset['relevance_score'],
                    dataset['quality_score'],
                    dataset['usability_score']
                )
            except Exception as e:
                logger.error(f"Error storing dataset {dataset['dataset_id']}: {e}")
    
    async def _store_processed_insight(self, insight: Dict, dataset_info: Dict):
        """Store processed insight in the database"""
        try:
            await self.db.execute("""
                INSERT INTO huggingface_processed_data 
                (dataset_id, symbol, data_type, data_content, confidence_score, processing_method)
                SELECT id, $1, $2, $3, $4, $5
                FROM huggingface_datasets 
                WHERE dataset_id = $6
            """,
                insight.get('symbol'),
                insight['data_type'],
                json.dumps(insight['data_content']),
                insight['confidence_score'],
                'automated_extraction',
                dataset_info['dataset_id']
            )
        except Exception as e:
            logger.error(f"Error storing processed insight: {e}")
    
    def _calculate_relevance_score(self, dataset) -> float:
        """Calculate relevance score for trading use case"""
        score = 0.0
        
        # Check dataset ID for trading keywords
        dataset_id_lower = dataset.id.lower()
        trading_keywords = ['finance', 'trading', 'crypto', 'stock', 'market', 'price', 'sentiment']
        
        for keyword in trading_keywords:
            if keyword in dataset_id_lower:
                score += 0.2
        
        # Check tags
        if hasattr(dataset, 'tags') and dataset.tags:
            relevant_tags = ['finance', 'time-series', 'tabular', 'economics', 'crypto']
            tag_score = sum(1 for tag in dataset.tags if any(rt in tag.lower() for rt in relevant_tags))
            score += min(tag_score * 0.1, 0.3)
        
        # Download count indicates quality/usefulness
        if hasattr(dataset, 'downloads') and dataset.downloads:
            if dataset.downloads > 10000:
                score += 0.3
            elif dataset.downloads > 1000:
                score += 0.2
            elif dataset.downloads > 100:
                score += 0.1
        
        return min(score, 1.0)
    
    def _calculate_quality_score(self, dataset) -> float:
        """Calculate quality score based on available metadata"""
        score = 0.0
        
        # Has description
        if hasattr(dataset, 'description') and dataset.description:
            score += 0.3
        
        # Has reasonable download count
        if hasattr(dataset, 'downloads') and dataset.downloads and dataset.downloads > 50:
            score += 0.4
        
        # Recent updates
        if hasattr(dataset, 'lastModified') and dataset.lastModified:
            days_since_update = (datetime.now() - dataset.lastModified).days
            if days_since_update < 365:  # Updated within a year
                score += 0.3
        
        return min(score, 1.0)
    
    def _calculate_usability_score(self, dataset) -> float:
        """Calculate usability score"""
        score = 0.5  # Base score
        
        # Prefer datasets with clear structure indicators
        if hasattr(dataset, 'tags') and dataset.tags:
            if 'tabular' in dataset.tags:
                score += 0.3
            if 'csv' in ' '.join(dataset.tags).lower():
                score += 0.2
        
        return min(score, 1.0)
    
    def _calculate_dataset_size(self, dataset: DatasetDict) -> float:
        """Estimate dataset size in GB"""
        try:
            total_rows = sum(len(split) for split in dataset.values())
            # Rough estimate: 1KB per row average
            estimated_size_gb = (total_rows * 1024) / (1024 * 1024 * 1024)
            return round(estimated_size_gb, 2)
        except:
            return 0.0
    
    async def get_integration_status(self) -> Dict[str, Any]:
        """Get status of HuggingFace data integration"""
        try:
            stats = await self.db.fetch_one("""
                SELECT 
                    COUNT(*) as total_datasets,
                    COUNT(*) FILTER (WHERE status = 'discovered') as discovered,
                    COUNT(*) FILTER (WHERE status = 'downloaded') as downloaded,
                    COUNT(*) FILTER (WHERE status = 'processed') as processed,
                    COUNT(*) FILTER (WHERE status = 'integrated') as integrated,
                    COUNT(*) FILTER (WHERE status = 'error') as errors,
                    AVG(relevance_score) as avg_relevance,
                    AVG(quality_score) as avg_quality,
                    SUM(dataset_size_gb) as total_size_gb
                FROM huggingface_datasets
            """)
            
            processed_stats = await self.db.fetch_one("""
                SELECT 
                    COUNT(*) as total_insights,
                    COUNT(DISTINCT symbol) as unique_symbols,
                    COUNT(*) FILTER (WHERE data_type = 'price') as price_insights,
                    COUNT(*) FILTER (WHERE data_type = 'sentiment') as sentiment_insights,
                    COUNT(*) FILTER (WHERE data_type = 'volume') as volume_insights,
                    COUNT(*) FILTER (WHERE data_type = 'news') as news_insights,
                    AVG(confidence_score) as avg_confidence
                FROM huggingface_processed_data
            """)
            
            return {
                'datasets': dict(stats),
                'processed_insights': dict(processed_stats),
                'cache_directory': str(self.local_cache_dir),
                'last_updated': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting integration status: {e}")
            return {'error': str(e)}


# Integration function for the main bot
async def integrate_huggingface_data(database_manager: DatabaseManager) -> Dict[str, Any]:
    """Main function to integrate HuggingFace data"""
    integrator = HuggingFaceDataIntegrator(database_manager)
    
    # Discover datasets
    datasets = await integrator.discover_relevant_datasets()
    logger.info(f"📊 Discovered {len(datasets)} relevant datasets")
    
    # Download and process top datasets
    processed = await integrator.download_priority_datasets(max_datasets=5)
    logger.info(f"✅ Processed {len(processed)} priority datasets")
    
    # Get status
    status = await integrator.get_integration_status()
    
    return {
        'discovered_datasets': len(datasets),
        'processed_datasets': len(processed),
        'integration_status': status,
        'success': True
    }
