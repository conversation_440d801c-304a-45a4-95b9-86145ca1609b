---
applyTo: 'bybit_bot/**/*.py'
description: "Trading system specific development instructions"
priority: highest
---

# TRADING SYSTEM DEVELOPMENT RULES

## API INTEGRATION REQUIREMENTS
- **Primary Exchange**: Bybit (spot, futures, options)
- **Real-time Data**: WebSocket feeds for live market data
- **Authentication**: Secure API key management
- **Rate Limits**: Respect exchange rate limits
- **Error Recovery**: Automatic reconnection and error handling

## TRADING LOGIC REQUIREMENTS
- **Pattern Recognition**: Implement autonomous pattern detection
- **Risk Management**: Dynamic position sizing and stop-losses
- **Multi-timeframe**: Support multiple timeframe analysis
- **Backtesting**: Comprehensive strategy validation
- **Performance Tracking**: Real-time performance metrics

## AUTONOMOUS OPERATION
All trading functions must operate autonomously:
- Market analysis and opportunity detection
- Position sizing based on risk parameters
- Order execution and management
- Portfolio rebalancing
- Risk monitoring and adjustment

## DATA REQUIREMENTS
- **Live Market Data**: Real-time price, volume, orderbook
- **Historical Data**: For backtesting and analysis
- **Economic Data**: News, sentiment, economic indicators
- **System Metrics**: Performance, latency, error rates

## SAFETY REQUIREMENTS
- **Risk Limits**: Maximum position sizes and drawdown limits
- **Circuit Breakers**: Automatic stops for unusual conditions
- **Monitoring**: Continuous system health monitoring
- **Logging**: Comprehensive audit trail

## SUPER GPT FEATURES
Implement all advanced AI capabilities:
- Natural language processing for market sentiment
- Advanced pattern recognition and prediction
- Autonomous decision-making algorithms
- Self-healing and error recovery systems
- Continuous learning and adaptation

## MANDATORY FUNCTIONS TO IMPLEMENT
Every module must include:
- Error handling and recovery
- Performance monitoring
- Logging and audit trails
- Real-time data processing
- Autonomous operation capabilities
