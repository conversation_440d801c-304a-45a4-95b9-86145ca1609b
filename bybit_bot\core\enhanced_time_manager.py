"""
Enhanced Time Management System with Market Session Awareness
Implements precise timing, calendar awareness, and market activity optimization
"""

import asyncio
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from zoneinfo import ZoneInfo
import holidays
import calendar

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager


class MarketRegion(Enum):
    """Global market regions"""
    ASIA_PACIFIC = "asia_pacific"
    EUROPE = "europe" 
    NORTH_AMERICA = "north_america"
    GLOBAL = "global"


class TradingDay(Enum):
    """Trading day classifications"""
    WEEKDAY = "weekday"
    WEEKEND = "weekend"
    HOLIDAY = "holiday"
    HALF_DAY = "half_day"
    SPECIAL_EVENT = "special_event"


@dataclass
class PreciseTimeMetrics:
    """Ultra-precise time metrics for trading decisions"""
    utc_timestamp: float  # Unix timestamp with microsecond precision
    utc_datetime: datetime
    local_datetime: datetime
    market_session: str
    trading_day_type: TradingDay
    milliseconds_in_session: int
    microseconds_precision: int
    time_zone_offset: float
    daylight_saving: bool
    market_open_countdown: Optional[timedelta]
    market_close_countdown: Optional[timedelta]
    optimal_trading_window: bool
    volatility_expected: float
    volume_expected: float


@dataclass
class MarketCalendar:
    """Market calendar with events and schedules"""
    date: datetime
    is_trading_day: bool
    market_open: Optional[datetime]
    market_close: Optional[datetime]
    half_day: bool
    holiday_name: Optional[str]
    special_events: List[str]
    expected_volatility: float
    expected_volume: float


class EnhancedTimeManager:
    """
    Ultra-precise time management system providing:
    - Microsecond precision timing
    - Global market session coordination
    - Holiday and event calendar integration
    - Optimal trading window detection
    - Time-based strategy optimization
    - Market rhythm analysis
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db = database_manager
        self.logger = TradingBotLogger(config)
        
        # Time zones for major markets
        self.time_zones = {
            'UTC': timezone.utc,
            'Tokyo': ZoneInfo("Asia/Tokyo"),
            'Sydney': ZoneInfo("Australia/Sydney"),
            'London': ZoneInfo("Europe/London"),
            'Frankfurt': ZoneInfo("Europe/Berlin"),
            'New_York': ZoneInfo("America/New_York"),
            'Chicago': ZoneInfo("America/Chicago"),
            'Los_Angeles': ZoneInfo("America/Los_Angeles")
        }
        
        # Market sessions with precise timing
        self.market_sessions = {
            'Asian_Early': {'start': 21, 'end': 2, 'peak': 23},  # UTC hours
            'Asian_Main': {'start': 0, 'end': 9, 'peak': 4},
            'European_Pre': {'start': 6, 'end': 8, 'peak': 7},
            'European_Main': {'start': 7, 'end': 16, 'peak': 10},
            'US_Pre': {'start': 12, 'end': 14, 'peak': 13},
            'US_Main': {'start': 13, 'end': 22, 'peak': 16},
            'Overlap_Asia_Europe': {'start': 7, 'end': 9, 'peak': 8},
            'Overlap_Europe_US': {'start': 13, 'end': 16, 'peak': 14}
        }
        
        # Holiday calendars for major markets
        self.holiday_calendars = {
            'US': holidays.UnitedStates(),
            'UK': holidays.UnitedKingdom(),
            'Japan': holidays.Japan(),
            'Germany': holidays.Germany(),
            'Australia': holidays.Australia(),
            'China': holidays.China(),
            'Hong_Kong': holidays.HongKong(),
            'Singapore': holidays.Singapore()
        }
        
        # Market activity profiles by time
        self.activity_profiles = {}
        self._initialize_activity_profiles()
        
        # Position timing tracking
        self.position_timings = {}
        self.session_statistics = {}
        
        # Performance by time periods
        self.time_performance = {}
        
    async def initialize(self):
        """Initialize the enhanced time manager"""
        try:
            self.logger.info("🕰️ Initializing Enhanced Time Manager with microsecond precision...")
            
            # Load historical time-based performance data
            await self._load_historical_time_performance()
            
            # Initialize market calendar
            await self._build_market_calendar()
            
            # Load session statistics
            await self._load_session_statistics()
            
            self.logger.info("✅ Enhanced Time Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Enhanced Time Manager: {e}")
            raise
    
    def get_precise_time_metrics(self) -> PreciseTimeMetrics:
        """Get ultra-precise time metrics for trading decisions"""
        try:
            # Get current time with maximum precision
            utc_timestamp = time.time()
            utc_datetime = datetime.fromtimestamp(utc_timestamp, tz=timezone.utc)
            
            # Calculate microseconds
            microseconds = int((utc_timestamp % 1) * 1_000_000)
            
            # Determine current market session
            current_session = self._get_current_market_session(utc_datetime)
            
            # Calculate time in session
            session_start = self._get_session_start_time(current_session, utc_datetime)
            milliseconds_in_session = int((utc_datetime - session_start).total_seconds() * 1000)
            
            # Get trading day classification
            trading_day_type = self._get_trading_day_type(utc_datetime)
            
            # Calculate market timing
            market_open_countdown = self._get_time_to_market_open(utc_datetime)
            market_close_countdown = self._get_time_to_market_close(utc_datetime)
            
            # Determine if in optimal trading window
            optimal_window = self._is_optimal_trading_window(utc_datetime, current_session)
            
            # Get expected market activity
            volatility_expected = self._get_expected_volatility(utc_datetime, current_session)
            volume_expected = self._get_expected_volume(utc_datetime, current_session)
            
            return PreciseTimeMetrics(
                utc_timestamp=utc_timestamp,
                utc_datetime=utc_datetime,
                local_datetime=utc_datetime.astimezone(),
                market_session=current_session,
                trading_day_type=trading_day_type,
                milliseconds_in_session=milliseconds_in_session,
                microseconds_precision=microseconds,
                time_zone_offset=utc_datetime.astimezone().utcoffset().total_seconds() / 3600,
                daylight_saving=time.daylight > 0,
                market_open_countdown=market_open_countdown,
                market_close_countdown=market_close_countdown,
                optimal_trading_window=optimal_window,
                volatility_expected=volatility_expected,
                volume_expected=volume_expected
            )
            
        except Exception as e:
            self.logger.error(f"Error getting precise time metrics: {e}")
            raise
    
    def get_market_rhythm_analysis(self, lookback_days: int = 30) -> Dict[str, Any]:
        """Analyze market rhythm patterns over time"""
        try:
            now = datetime.utcnow()
            analysis = {
                'timestamp': now,
                'lookback_days': lookback_days,
                'hourly_patterns': {},
                'daily_patterns': {},
                'weekly_patterns': {},
                'monthly_patterns': {},
                'optimal_windows': [],
                'high_volatility_periods': [],
                'low_activity_periods': []
            }
            
            # Analyze hourly patterns (UTC)
            for hour in range(24):
                analysis['hourly_patterns'][hour] = {
                    'avg_volatility': self._get_historical_avg_volatility(hour),
                    'avg_volume': self._get_historical_avg_volume(hour),
                    'success_rate': self._get_historical_success_rate(hour),
                    'optimal_for_trading': hour in self._get_optimal_hours()
                }
            
            # Analyze daily patterns
            for day in range(7):  # 0=Monday, 6=Sunday
                day_name = calendar.day_name[day]
                analysis['daily_patterns'][day_name] = {
                    'avg_volatility': self._get_daily_avg_volatility(day),
                    'avg_volume': self._get_daily_avg_volume(day),
                    'market_open': day < 5,  # Weekdays
                    'recommended_strategy': self._get_recommended_daily_strategy(day)
                }
            
            # Find optimal trading windows
            analysis['optimal_windows'] = self._identify_optimal_windows()
            
            # Identify high volatility periods
            analysis['high_volatility_periods'] = self._identify_high_volatility_periods()
            
            # Identify low activity periods to avoid
            analysis['low_activity_periods'] = self._identify_low_activity_periods()
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing market rhythm: {e}")
            return {}
    
    def calculate_position_timing_score(self, 
                                      entry_time: datetime, 
                                      exit_time: Optional[datetime] = None) -> Dict[str, Any]:
        """Calculate timing score for position entry/exit"""
        try:
            if exit_time is None:
                exit_time = datetime.utcnow()
            
            # Calculate holding duration
            duration = exit_time - entry_time
            duration_hours = duration.total_seconds() / 3600
            
            # Get session information for entry and exit
            entry_session = self._get_current_market_session(entry_time)
            exit_session = self._get_current_market_session(exit_time)
            
            # Calculate timing scores
            entry_score = self._calculate_timing_score(entry_time, entry_session)
            exit_score = self._calculate_timing_score(exit_time, exit_session)
            
            # Overall timing assessment
            overall_score = (entry_score + exit_score) / 2
            
            return {
                'entry_time': entry_time,
                'exit_time': exit_time,
                'duration_hours': duration_hours,
                'entry_session': entry_session,
                'exit_session': exit_session,
                'entry_score': entry_score,
                'exit_score': exit_score,
                'overall_score': overall_score,
                'timing_grade': self._get_timing_grade(overall_score),
                'recommendations': self._get_timing_recommendations(overall_score)
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating position timing score: {e}")
            return {}
    
    def get_next_optimal_entry_window(self, hours_ahead: int = 24) -> List[Dict[str, Any]]:
        """Get next optimal entry windows within specified hours"""
        try:
            now = datetime.utcnow()
            end_time = now + timedelta(hours=hours_ahead)
            optimal_windows = []
            
            # Check each hour for optimal conditions
            current = now
            while current < end_time:
                hour = current.hour
                day_of_week = current.weekday()
                
                # Check if this hour is historically optimal
                if self._is_historically_optimal_hour(hour, day_of_week):
                    session = self._get_current_market_session(current)
                    
                    window = {
                        'start_time': current,
                        'end_time': current + timedelta(hours=1),
                        'session': session,
                        'expected_volatility': self._get_expected_volatility(current, session),
                        'expected_volume': self._get_expected_volume(current, session),
                        'confidence_score': self._calculate_window_confidence(current),
                        'recommended_strategies': self._get_recommended_strategies_for_time(current)
                    }
                    optimal_windows.append(window)
                
                current += timedelta(hours=1)
            
            # Sort by confidence score
            optimal_windows.sort(key=lambda x: x['confidence_score'], reverse=True)
            
            return optimal_windows[:10]  # Top 10 windows
            
        except Exception as e:
            self.logger.error(f"Error getting optimal entry windows: {e}")
            return []
    
    def should_trade_now(self, strategy_type: str = 'general') -> Dict[str, Any]:
        """Comprehensive analysis of whether to trade now"""
        try:
            time_metrics = self.get_precise_time_metrics()
            
            # Base decision factors
            factors = {
                'market_open': time_metrics.trading_day_type == TradingDay.WEEKDAY,
                'optimal_window': time_metrics.optimal_trading_window,
                'sufficient_volatility': time_metrics.volatility_expected > 0.5,
                'sufficient_volume': time_metrics.volume_expected > 0.5,
                'not_holiday': time_metrics.trading_day_type != TradingDay.HOLIDAY,
                'session_active': time_metrics.market_session != 'Quiet'
            }
            
            # Calculate overall score
            positive_factors = sum(1 for factor in factors.values() if factor)
            total_factors = len(factors)
            confidence_score = positive_factors / total_factors
            
            # Strategy-specific adjustments
            if strategy_type == 'scalping':
                # Scalping needs high volatility and volume
                if time_metrics.volatility_expected < 0.7 or time_metrics.volume_expected < 0.7:
                    confidence_score *= 0.5
            elif strategy_type == 'swing':
                # Swing trading is less time-sensitive
                confidence_score += 0.1
            elif strategy_type == 'news':
                # News trading needs to consider event timing
                if self._is_near_major_news_time(time_metrics.utc_datetime):
                    confidence_score += 0.2
            
            # Final recommendation
            should_trade = confidence_score > 0.6
            
            return {
                'should_trade': should_trade,
                'confidence_score': confidence_score,
                'factors': factors,
                'time_metrics': time_metrics,
                'recommendation': 'TRADE' if should_trade else 'WAIT',
                'next_optimal_window': self.get_next_optimal_entry_window(6)[0] if not should_trade else None
            }
            
        except Exception as e:
            self.logger.error(f"Error determining if should trade now: {e}")
            return {'should_trade': False, 'confidence_score': 0.0}
    
    # Private helper methods
    def _initialize_activity_profiles(self):
        """Initialize market activity profiles"""
        # This would be populated with historical data
        # For now, using default patterns
        self.activity_profiles = {
            'Asian_Early': {'volatility': 0.6, 'volume': 0.4, 'spread': 1.3},
            'Asian_Main': {'volatility': 0.8, 'volume': 0.7, 'spread': 1.1},
            'European_Pre': {'volatility': 0.7, 'volume': 0.5, 'spread': 1.2},
            'European_Main': {'volatility': 1.0, 'volume': 1.0, 'spread': 1.0},
            'US_Pre': {'volatility': 0.9, 'volume': 0.8, 'spread': 1.1},
            'US_Main': {'volatility': 1.2, 'volume': 1.3, 'spread': 0.9},
            'Overlap_Asia_Europe': {'volatility': 1.1, 'volume': 1.1, 'spread': 0.95},
            'Overlap_Europe_US': {'volatility': 1.4, 'volume': 1.5, 'spread': 0.8}
        }
    
    def _get_current_market_session(self, dt: datetime) -> str:
        """Determine current market session"""
        hour = dt.hour
        
        # Check for overlap sessions first (highest priority)
        if 7 <= hour < 9:
            return 'Overlap_Asia_Europe'
        elif 13 <= hour < 16:
            return 'Overlap_Europe_US'
        
        # Individual sessions
        elif 21 <= hour or hour < 2:
            return 'Asian_Early'
        elif 0 <= hour < 9:
            return 'Asian_Main'
        elif 6 <= hour < 8:
            return 'European_Pre'
        elif 7 <= hour < 16:
            return 'European_Main'
        elif 12 <= hour < 14:
            return 'US_Pre'
        elif 13 <= hour < 22:
            return 'US_Main'
        else:
            return 'Quiet'
    
    def _get_trading_day_type(self, dt: datetime) -> TradingDay:
        """Classify the type of trading day"""
        # Check if weekend
        if dt.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return TradingDay.WEEKEND
        
        # Check for holidays
        date = dt.date()
        for country, holiday_calendar in self.holiday_calendars.items():
            if date in holiday_calendar:
                return TradingDay.HOLIDAY
        
        return TradingDay.WEEKDAY
    
    def _get_session_start_time(self, session: str, dt: datetime) -> datetime:
        """Get the start time of a session"""
        if session in self.market_sessions:
            start_hour = self.market_sessions[session]['start']
            return dt.replace(hour=start_hour, minute=0, second=0, microsecond=0)
        return dt
    
    def _get_time_to_market_open(self, dt: datetime) -> Optional[timedelta]:
        """Get time until next market open"""
        if dt.weekday() >= 5:  # Weekend
            # Next market open is Monday
            days_until_monday = (7 - dt.weekday()) % 7
            if days_until_monday == 0:
                days_until_monday = 1
            next_open = dt.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=days_until_monday)
            return next_open - dt
        
        # During weekday, find next session start
        current_hour = dt.hour
        next_session_hour = None
        
        for session, times in self.market_sessions.items():
            start_hour = times['start']
            if start_hour > current_hour:
                next_session_hour = start_hour
                break
        
        if next_session_hour:
            next_open = dt.replace(hour=next_session_hour, minute=0, second=0, microsecond=0)
            return next_open - dt
        
        return None
    
    def _get_time_to_market_close(self, dt: datetime) -> Optional[timedelta]:
        """Get time until current session closes"""
        current_session = self._get_current_market_session(dt)
        if current_session in self.market_sessions:
            end_hour = self.market_sessions[current_session]['end']
            end_time = dt.replace(hour=end_hour, minute=0, second=0, microsecond=0)
            if end_time < dt:
                end_time += timedelta(days=1)
            return end_time - dt
        return None
    
    def _is_optimal_trading_window(self, dt: datetime, session: str) -> bool:
        """Check if current time is in optimal trading window"""
        if session == 'Quiet':
            return False
        
        hour = dt.hour
        minute = dt.minute
        
        # Peak hours for each session
        peak_hours = {
            'Asian_Main': [3, 4, 5],
            'European_Main': [9, 10, 11, 14, 15],
            'US_Main': [15, 16, 17, 20, 21],
            'Overlap_Asia_Europe': [7, 8],
            'Overlap_Europe_US': [13, 14, 15]
        }
        
        if session in peak_hours:
            return hour in peak_hours[session]
        
        return False
    
    def _get_expected_volatility(self, dt: datetime, session: str) -> float:
        """Get expected volatility for time period"""
        base_volatility = self.activity_profiles.get(session, {}).get('volatility', 0.5)
        
        # Adjust for specific conditions
        if dt.weekday() >= 5:  # Weekend
            base_volatility *= 0.3
        elif self._get_trading_day_type(dt) == TradingDay.HOLIDAY:
            base_volatility *= 0.2
        
        return min(1.0, base_volatility)
    
    def _get_expected_volume(self, dt: datetime, session: str) -> float:
        """Get expected volume for time period"""
        base_volume = self.activity_profiles.get(session, {}).get('volume', 0.5)
        
        # Adjust for specific conditions
        if dt.weekday() >= 5:  # Weekend
            base_volume *= 0.2
        elif self._get_trading_day_type(dt) == TradingDay.HOLIDAY:
            base_volume *= 0.1
        
        return min(1.0, base_volume)
    
    async def _load_historical_time_performance(self):
        """Load historical performance by time periods"""
        try:
            # This would load from database
            # For now, using placeholder
            self.time_performance = {}
        except Exception as e:
            self.logger.error(f"Error loading historical time performance: {e}")
    
    async def _build_market_calendar(self):
        """Build comprehensive market calendar"""
        try:
            # This would build a detailed calendar
            # For now, basic implementation
            pass
        except Exception as e:
            self.logger.error(f"Error building market calendar: {e}")
    
    async def _load_session_statistics(self):
        """Load session-based statistics"""
        try:
            # Load from database
            self.session_statistics = {}
        except Exception as e:
            self.logger.error(f"Error loading session statistics: {e}")
    
    def _get_historical_avg_volatility(self, hour: int) -> float:
        """Get historical average volatility for hour"""
        # Placeholder - would use real historical data
        hour_volatility_map = {
            0: 0.4, 1: 0.3, 2: 0.3, 3: 0.5, 4: 0.6, 5: 0.5,
            6: 0.4, 7: 0.7, 8: 0.8, 9: 0.9, 10: 0.8, 11: 0.7,
            12: 0.6, 13: 0.9, 14: 1.0, 15: 1.0, 16: 0.9, 17: 0.8,
            18: 0.6, 19: 0.5, 20: 0.7, 21: 0.6, 22: 0.5, 23: 0.4
        }
        return hour_volatility_map.get(hour, 0.5)
    
    def _get_historical_avg_volume(self, hour: int) -> float:
        """Get historical average volume for hour"""
        # Placeholder - would use real historical data
        return self._get_historical_avg_volatility(hour) * 0.9
    
    def _get_historical_success_rate(self, hour: int) -> float:
        """Get historical success rate for hour"""
        # Placeholder - would use real historical data
        optimal_hours = [3, 4, 8, 9, 10, 14, 15, 16, 20, 21]
        return 0.7 if hour in optimal_hours else 0.5
    
    def _get_optimal_hours(self) -> List[int]:
        """Get list of optimal trading hours"""
        return [3, 4, 8, 9, 10, 14, 15, 16, 20, 21]
    
    def _get_daily_avg_volatility(self, day: int) -> float:
        """Get average volatility for day of week"""
        day_volatility = {0: 0.8, 1: 0.9, 2: 1.0, 3: 0.9, 4: 0.7, 5: 0.3, 6: 0.2}
        return day_volatility.get(day, 0.5)
    
    def _get_daily_avg_volume(self, day: int) -> float:
        """Get average volume for day of week"""
        return self._get_daily_avg_volatility(day) * 0.9
    
    def _get_recommended_daily_strategy(self, day: int) -> str:
        """Get recommended strategy for day of week"""
        if day < 5:  # Weekdays
            return 'active_trading'
        else:  # Weekend
            return 'position_management'
    
    def _identify_optimal_windows(self) -> List[Dict[str, Any]]:
        """Identify optimal trading windows"""
        return [
            {'start_hour': 8, 'end_hour': 10, 'session': 'European_Main'},
            {'start_hour': 14, 'end_hour': 16, 'session': 'Overlap_Europe_US'},
            {'start_hour': 20, 'end_hour': 22, 'session': 'US_Main'}
        ]
    
    def _identify_high_volatility_periods(self) -> List[Dict[str, Any]]:
        """Identify high volatility periods"""
        return [
            {'start_hour': 13, 'end_hour': 16, 'reason': 'Europe-US overlap'},
            {'start_hour': 20, 'end_hour': 22, 'reason': 'US market peak'}
        ]
    
    def _identify_low_activity_periods(self) -> List[Dict[str, Any]]:
        """Identify periods to avoid trading"""
        return [
            {'start_hour': 22, 'end_hour': 2, 'reason': 'Market quiet'},
            {'start_hour': 5, 'end_hour': 7, 'reason': 'Pre-European low volume'}
        ]
    
    def _calculate_timing_score(self, dt: datetime, session: str) -> float:
        """Calculate timing score for specific time"""
        base_score = 0.5
        
        # Session bonus
        if session in ['Overlap_Europe_US', 'European_Main', 'US_Main']:
            base_score += 0.2
        elif session == 'Overlap_Asia_Europe':
            base_score += 0.1
        
        # Hour bonus
        if self._is_optimal_trading_window(dt, session):
            base_score += 0.2
        
        # Day penalty
        if dt.weekday() >= 5:
            base_score -= 0.3
        
        return max(0.0, min(1.0, base_score))
    
    def _get_timing_grade(self, score: float) -> str:
        """Convert timing score to grade"""
        if score >= 0.8:
            return 'A'
        elif score >= 0.6:
            return 'B'
        elif score >= 0.4:
            return 'C'
        elif score >= 0.2:
            return 'D'
        else:
            return 'F'
    
    def _get_timing_recommendations(self, score: float) -> List[str]:
        """Get recommendations based on timing score"""
        if score >= 0.8:
            return ["Excellent timing", "Continue with current approach"]
        elif score >= 0.6:
            return ["Good timing", "Minor optimization possible"]
        else:
            return ["Poor timing", "Consider waiting for better windows", "Review market session schedule"]
    
    def _is_historically_optimal_hour(self, hour: int, day_of_week: int) -> bool:
        """Check if hour is historically optimal"""
        optimal_hours = self._get_optimal_hours()
        return hour in optimal_hours and day_of_week < 5
    
    def _calculate_window_confidence(self, dt: datetime) -> float:
        """Calculate confidence score for trading window"""
        session = self._get_current_market_session(dt)
        base_score = 0.5
        
        if self._is_optimal_trading_window(dt, session):
            base_score += 0.3
        
        if dt.weekday() < 5:  # Weekday
            base_score += 0.1
        
        volatility = self._get_expected_volatility(dt, session)
        base_score += volatility * 0.1
        
        return min(1.0, base_score)
    
    def _get_recommended_strategies_for_time(self, dt: datetime) -> List[str]:
        """Get recommended strategies for specific time"""
        session = self._get_current_market_session(dt)
        volatility = self._get_expected_volatility(dt, session)
        
        if volatility > 0.8:
            return ['momentum', 'trend_following']
        elif volatility > 0.5:
            return ['momentum', 'mean_reversion']
        else:
            return ['mean_reversion', 'range_trading']
    
    def _is_near_major_news_time(self, dt: datetime) -> bool:
        """Check if near major news release times"""
        # Common news release times (UTC)
        news_hours = [8, 12, 13, 14, 15]  # ECB, Fed, major economic data
        return dt.hour in news_hours
