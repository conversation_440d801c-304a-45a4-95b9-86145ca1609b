"""create call logs table

Revision ID: be1d922bf2ad
Revises: 2fbd6472112c
Create Date: 2023-08-08 08:42:37.148178

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'be1d922bf2ad'
down_revision = '520aa6776347'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('call_logs',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('agent_execution_name', sa.String(), nullable=False),
    sa.<PERSON>umn('agent_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('tokens_consumed', sa.Integer(), nullable=False),
    sa.Column('tool_used', sa.String(), nullable=False),
    sa.Column('model', sa.String(), nullable=True),
    sa.Column('org_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.<PERSON>umn('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('call_logs')
    # ### end Alembic commands ###
