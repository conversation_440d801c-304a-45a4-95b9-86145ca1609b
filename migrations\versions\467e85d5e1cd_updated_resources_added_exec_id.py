"""updated_resources_added_exec_id

Revision ID: 467e85d5e1cd
Revises: ba60b12ae109
Create Date: 2023-07-10 08:54:46.702652

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '467e85d5e1cd'
down_revision = 'ba60b12ae109'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('resources', sa.Column('agent_execution_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('resources', 'agent_execution_id')
    # ### end Alembic commands ###
