-- Complete Database Schema for Super GPT Bybit Bot
-- This script creates all required tables for the enhanced trading bot

-- ============================================================================
-- MEMORY MANAGEMENT TABLES
-- ============================================================================

-- Trading memories for persistent learning
CREATE TABLE IF NOT EXISTS trading_memories (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    pattern_data JSONB NOT NULL,
    pattern_hash VARCHAR(64) NOT NULL,
    outcome JSONB NOT NULL,
    success BOOLEAN NOT NULL,
    profit_loss DECIMAL(15,8) DEFAULT 0,
    strategy VARCHAR(50),
    confidence DECIMAL(5,4) DEFAULT 0,
    market_conditions JSONB,
    technical_indicators JSONB,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_trading_memories_symbol_timestamp ON trading_memories(symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_trading_memories_pattern_hash ON trading_memories(pattern_hash);
CREATE INDEX IF NOT EXISTS idx_trading_memories_success ON trading_memories(success);
CREATE INDEX IF NOT EXISTS idx_trading_memories_strategy ON trading_memories(strategy);

-- Strategy memories for adaptive learning
CREATE TABLE IF NOT EXISTS strategy_memories (
    id SERIAL PRIMARY KEY,
    strategy_name VARCHAR(50) NOT NULL,
    memory_data JSONB NOT NULL,
    performance_score DECIMAL(10,6) DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    total_pnl DECIMAL(15,8) DEFAULT 0,
    max_drawdown DECIMAL(10,6) DEFAULT 0,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE UNIQUE INDEX IF NOT EXISTS idx_strategy_memories_name ON strategy_memories(strategy_name);
CREATE INDEX IF NOT EXISTS idx_strategy_memories_updated ON strategy_memories(updated_at);
CREATE INDEX IF NOT EXISTS idx_strategy_memories_performance ON strategy_memories(performance_score);

-- ============================================================================
-- FIRECRAWL DATA TABLES
-- ============================================================================

-- Raw firecrawl data storage
CREATE TABLE IF NOT EXISTS firecrawl_data (
    id SERIAL PRIMARY KEY,
    url VARCHAR(2048) NOT NULL,
    source_type VARCHAR(50) NOT NULL, -- 'news', 'analysis', 'social', 'regulatory'
    title VARCHAR(500),
    content TEXT,
    extracted_data JSONB,
    metadata JSONB DEFAULT '{}',
    crawled_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    processed BOOLEAN DEFAULT FALSE,
    processing_errors TEXT,
    sentiment_score DECIMAL(5,4),
    relevance_score DECIMAL(5,4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_firecrawl_data_url ON firecrawl_data(url);
CREATE INDEX IF NOT EXISTS idx_firecrawl_data_source_type ON firecrawl_data(source_type);
CREATE INDEX IF NOT EXISTS idx_firecrawl_data_crawled_at ON firecrawl_data(crawled_at);
CREATE INDEX IF NOT EXISTS idx_firecrawl_data_processed ON firecrawl_data(processed);
CREATE INDEX IF NOT EXISTS idx_firecrawl_data_sentiment ON firecrawl_data(sentiment_score);

-- Processed market intelligence from firecrawl
CREATE TABLE IF NOT EXISTS market_intelligence (
    id SERIAL PRIMARY KEY,
    source_url VARCHAR(2048),
    source_type VARCHAR(50) NOT NULL,
    intelligence_type VARCHAR(50) NOT NULL, -- 'price_prediction', 'trend_analysis', 'sentiment', 'news_impact'
    symbol VARCHAR(20),
    content TEXT NOT NULL,
    extracted_insights JSONB,
    confidence_score DECIMAL(5,4) DEFAULT 0,
    impact_score DECIMAL(5,4) DEFAULT 0,
    sentiment_score DECIMAL(5,4),
    relevance_score DECIMAL(5,4) DEFAULT 0,
    processing_metadata JSONB DEFAULT '{}',
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_market_intelligence_symbol ON market_intelligence(symbol);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_type ON market_intelligence(intelligence_type);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_source_type ON market_intelligence(source_type);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_impact ON market_intelligence(impact_score);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_created ON market_intelligence(created_at);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_expires ON market_intelligence(expires_at);

-- ============================================================================
-- ENHANCED TIME MANAGEMENT TABLES
-- ============================================================================

-- Time-based performance tracking
CREATE TABLE IF NOT EXISTS time_performance_tracking (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    time_category VARCHAR(50) NOT NULL, -- 'market_hours', 'pre_market', 'post_market', 'weekend'
    market_session VARCHAR(50), -- 'asian', 'european', 'american', 'overlap'
    hour_of_day INTEGER NOT NULL CHECK (hour_of_day >= 0 AND hour_of_day <= 23),
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
    total_trades INTEGER DEFAULT 0,
    successful_trades INTEGER DEFAULT 0,
    total_pnl DECIMAL(15,8) DEFAULT 0,
    avg_trade_duration_minutes INTEGER,
    volatility DECIMAL(10,6),
    volume_profile JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_time_performance_timestamp ON time_performance_tracking(timestamp);
CREATE INDEX IF NOT EXISTS idx_time_performance_category ON time_performance_tracking(time_category);
CREATE INDEX IF NOT EXISTS idx_time_performance_session ON time_performance_tracking(market_session);
CREATE INDEX IF NOT EXISTS idx_time_performance_hour ON time_performance_tracking(hour_of_day);
CREATE INDEX IF NOT EXISTS idx_time_performance_dow ON time_performance_tracking(day_of_week);

-- Optimal trading windows
CREATE TABLE IF NOT EXISTS optimal_trading_windows (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    strategy VARCHAR(50) NOT NULL,
    start_hour INTEGER NOT NULL,
    end_hour INTEGER NOT NULL,
    days_of_week INTEGER[] NOT NULL, -- Array of days (0=Sunday, 6=Saturday)
    timezone VARCHAR(50) NOT NULL DEFAULT 'UTC',
    win_rate DECIMAL(5,4) DEFAULT 0,
    avg_profit DECIMAL(15,8) DEFAULT 0,
    trade_count INTEGER DEFAULT 0,
    volatility_score DECIMAL(5,4) DEFAULT 0,
    confidence_score DECIMAL(5,4) DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_optimal_windows_symbol ON optimal_trading_windows(symbol);
CREATE INDEX IF NOT EXISTS idx_optimal_windows_strategy ON optimal_trading_windows(strategy);
CREATE INDEX IF NOT EXISTS idx_optimal_windows_hours ON optimal_trading_windows(start_hour, end_hour);
CREATE INDEX IF NOT EXISTS idx_optimal_windows_performance ON optimal_trading_windows(win_rate, avg_profit);

-- ============================================================================
-- ADVANCED RISK MANAGEMENT TABLES
-- ============================================================================

-- Risk scenario analysis
CREATE TABLE IF NOT EXISTS risk_scenarios (
    id SERIAL PRIMARY KEY,
    scenario_name VARCHAR(100) NOT NULL,
    scenario_type VARCHAR(50) NOT NULL, -- 'stress_test', 'monte_carlo', 'historical'
    parameters JSONB NOT NULL,
    results JSONB NOT NULL,
    max_loss DECIMAL(15,8),
    probability DECIMAL(5,4),
    var_95 DECIMAL(15,8), -- Value at Risk 95%
    var_99 DECIMAL(15,8), -- Value at Risk 99%
    expected_shortfall DECIMAL(15,8),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_risk_scenarios_name ON risk_scenarios(scenario_name);
CREATE INDEX IF NOT EXISTS idx_risk_scenarios_type ON risk_scenarios(scenario_type);
CREATE INDEX IF NOT EXISTS idx_risk_scenarios_max_loss ON risk_scenarios(max_loss);

-- Correlation matrices for risk management
CREATE TABLE IF NOT EXISTS correlation_matrices (
    id SERIAL PRIMARY KEY,
    calculation_date DATE NOT NULL,
    timeframe VARCHAR(20) NOT NULL, -- '1d', '7d', '30d', etc.
    correlation_data JSONB NOT NULL, -- Full correlation matrix
    eigenvalues DECIMAL(10,6)[], -- For PCA analysis
    max_correlation DECIMAL(5,4),
    avg_correlation DECIMAL(5,4),
    diversification_ratio DECIMAL(5,4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_correlation_matrices_date ON correlation_matrices(calculation_date);
CREATE INDEX IF NOT EXISTS idx_correlation_matrices_timeframe ON correlation_matrices(timeframe);

-- ============================================================================
-- ADVANCED ANALYTICS TABLES
-- ============================================================================

-- Strategy performance attribution
CREATE TABLE IF NOT EXISTS strategy_attribution (
    id SERIAL PRIMARY KEY,
    strategy_name VARCHAR(50) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    date DATE NOT NULL,
    pnl_attribution DECIMAL(15,8) NOT NULL,
    risk_attribution DECIMAL(15,8) NOT NULL,
    alpha DECIMAL(10,6),
    beta DECIMAL(10,6),
    tracking_error DECIMAL(10,6),
    information_ratio DECIMAL(10,6),
    factor_exposures JSONB, -- Exposure to various market factors
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_strategy_attribution_strategy ON strategy_attribution(strategy_name);
CREATE INDEX IF NOT EXISTS idx_strategy_attribution_symbol ON strategy_attribution(symbol);
CREATE INDEX IF NOT EXISTS idx_strategy_attribution_date ON strategy_attribution(date);

-- Advanced portfolio metrics
CREATE TABLE IF NOT EXISTS portfolio_metrics (
    id SERIAL PRIMARY KEY,
    calculation_date DATE NOT NULL,
    total_value DECIMAL(15,8) NOT NULL,
    total_pnl DECIMAL(15,8) NOT NULL,
    daily_return DECIMAL(10,6),
    volatility DECIMAL(10,6),
    sharpe_ratio DECIMAL(10,6),
    sortino_ratio DECIMAL(10,6),
    calmar_ratio DECIMAL(10,6),
    max_drawdown DECIMAL(10,6),
    current_drawdown DECIMAL(10,6),
    var_95 DECIMAL(15,8),
    var_99 DECIMAL(15,8),
    expected_shortfall DECIMAL(15,8),
    beta_to_market DECIMAL(10,6),
    correlation_to_market DECIMAL(5,4),
    position_count INTEGER,
    concentration_risk DECIMAL(5,4),
    leverage DECIMAL(10,6),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_portfolio_metrics_date ON portfolio_metrics(calculation_date);
CREATE INDEX IF NOT EXISTS idx_portfolio_metrics_performance ON portfolio_metrics(sharpe_ratio, total_pnl);

-- ============================================================================
-- MACHINE LEARNING ENHANCEMENT TABLES
-- ============================================================================

-- Feature engineering tracking
CREATE TABLE IF NOT EXISTS feature_engineering (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    feature_name VARCHAR(100) NOT NULL,
    feature_type VARCHAR(50) NOT NULL, -- 'technical', 'fundamental', 'sentiment', 'macro'
    calculation_method TEXT NOT NULL,
    importance_score DECIMAL(5,4) DEFAULT 0,
    correlation_with_target DECIMAL(5,4),
    stability_score DECIMAL(5,4),
    last_calculated TIMESTAMP WITH TIME ZONE,
    calculation_errors INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_feature_engineering_symbol ON feature_engineering(symbol);
CREATE INDEX IF NOT EXISTS idx_feature_engineering_name ON feature_engineering(feature_name);
CREATE INDEX IF NOT EXISTS idx_feature_engineering_importance ON feature_engineering(importance_score);

-- Model training history
CREATE TABLE IF NOT EXISTS model_training_history (
    id SERIAL PRIMARY KEY,
    model_name VARCHAR(100) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    training_start TIMESTAMP WITH TIME ZONE NOT NULL,
    training_end TIMESTAMP WITH TIME ZONE,
    model_type VARCHAR(50) NOT NULL, -- 'xgboost', 'lstm', 'transformer', etc.
    hyperparameters JSONB NOT NULL,
    training_samples INTEGER NOT NULL,
    validation_samples INTEGER NOT NULL,
    test_samples INTEGER NOT NULL,
    training_accuracy DECIMAL(5,4),
    validation_accuracy DECIMAL(5,4),
    test_accuracy DECIMAL(5,4),
    feature_importance JSONB,
    model_metrics JSONB,
    model_path VARCHAR(500),
    status VARCHAR(20) DEFAULT 'training', -- 'training', 'completed', 'failed', 'deployed'
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_model_training_name ON model_training_history(model_name);
CREATE INDEX IF NOT EXISTS idx_model_training_symbol ON model_training_history(symbol);
CREATE INDEX IF NOT EXISTS idx_model_training_status ON model_training_history(status);
CREATE INDEX IF NOT EXISTS idx_model_training_accuracy ON model_training_history(test_accuracy);

-- ============================================================================
-- HUGGINGFACE DATA INTEGRATION TABLES
-- ============================================================================

-- HuggingFace datasets tracking
CREATE TABLE IF NOT EXISTS huggingface_datasets (
    id SERIAL PRIMARY KEY,
    dataset_name VARCHAR(200) NOT NULL,
    dataset_id VARCHAR(200) NOT NULL,
    description TEXT,
    categories VARCHAR(100)[],
    tags VARCHAR(50)[],
    download_count BIGINT,
    dataset_size_gb DECIMAL(10,2),
    last_modified TIMESTAMP WITH TIME ZONE,
    relevance_score DECIMAL(5,4) DEFAULT 0,
    quality_score DECIMAL(5,4) DEFAULT 0,
    usability_score DECIMAL(5,4) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'discovered', -- 'discovered', 'downloaded', 'processed', 'integrated'
    local_path VARCHAR(500),
    integration_metadata JSONB DEFAULT '{}',
    error_messages TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_huggingface_datasets_name ON huggingface_datasets(dataset_name);
CREATE INDEX IF NOT EXISTS idx_huggingface_datasets_id ON huggingface_datasets(dataset_id);
CREATE INDEX IF NOT EXISTS idx_huggingface_datasets_relevance ON huggingface_datasets(relevance_score);
CREATE INDEX IF NOT EXISTS idx_huggingface_datasets_status ON huggingface_datasets(status);

-- Processed HuggingFace data
CREATE TABLE IF NOT EXISTS huggingface_processed_data (
    id SERIAL PRIMARY KEY,
    dataset_id INTEGER REFERENCES huggingface_datasets(id),
    symbol VARCHAR(20),
    data_type VARCHAR(50) NOT NULL, -- 'price', 'sentiment', 'news', 'social', 'fundamental'
    data_content JSONB NOT NULL,
    confidence_score DECIMAL(5,4) DEFAULT 0,
    processing_method VARCHAR(100),
    processing_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    valid_from TIMESTAMP WITH TIME ZONE,
    valid_to TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_hf_processed_dataset ON huggingface_processed_data(dataset_id);
CREATE INDEX IF NOT EXISTS idx_hf_processed_symbol ON huggingface_processed_data(symbol);
CREATE INDEX IF NOT EXISTS idx_hf_processed_type ON huggingface_processed_data(data_type);
CREATE INDEX IF NOT EXISTS idx_hf_processed_timestamp ON huggingface_processed_data(processing_timestamp);

-- ============================================================================
-- SYSTEM MONITORING TABLES
-- ============================================================================

-- Enhanced system performance tracking
CREATE TABLE IF NOT EXISTS system_performance_detailed (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    component VARCHAR(50) NOT NULL, -- 'bot_manager', 'ml_engine', 'risk_manager', etc.
    operation VARCHAR(100) NOT NULL,
    execution_time_ms INTEGER NOT NULL,
    memory_usage_mb DECIMAL(10,2),
    cpu_usage_percent DECIMAL(5,2),
    success BOOLEAN NOT NULL,
    error_message TEXT,
    performance_metrics JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_system_perf_timestamp ON system_performance_detailed(timestamp);
CREATE INDEX IF NOT EXISTS idx_system_perf_component ON system_performance_detailed(component);
CREATE INDEX IF NOT EXISTS idx_system_perf_operation ON system_performance_detailed(operation);
CREATE INDEX IF NOT EXISTS idx_system_perf_execution_time ON system_performance_detailed(execution_time_ms);

-- API rate limiting tracking
CREATE TABLE IF NOT EXISTS api_rate_limits (
    id SERIAL PRIMARY KEY,
    api_name VARCHAR(50) NOT NULL, -- 'bybit', 'firecrawl', 'huggingface'
    endpoint VARCHAR(200) NOT NULL,
    request_timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    response_time_ms INTEGER,
    rate_limit_remaining INTEGER,
    rate_limit_reset TIMESTAMP WITH TIME ZONE,
    status_code INTEGER,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    request_metadata JSONB DEFAULT '{}'
);

CREATE INDEX IF NOT EXISTS idx_api_rate_limits_api ON api_rate_limits(api_name);
CREATE INDEX IF NOT EXISTS idx_api_rate_limits_endpoint ON api_rate_limits(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_rate_limits_timestamp ON api_rate_limits(request_timestamp);

-- ============================================================================
-- VIEWS FOR ENHANCED ANALYTICS
-- ============================================================================

-- Memory performance summary view
CREATE OR REPLACE VIEW memory_performance_summary AS
SELECT 
    symbol,
    strategy,
    COUNT(*) as total_memories,
    AVG(CASE WHEN success THEN 1 ELSE 0 END) as success_rate,
    AVG(profit_loss) as avg_pnl,
    SUM(profit_loss) as total_pnl,
    AVG(confidence) as avg_confidence,
    DATE_TRUNC('day', timestamp) as date
FROM trading_memories 
GROUP BY symbol, strategy, DATE_TRUNC('day', timestamp)
ORDER BY date DESC;

-- Time-based performance view
CREATE OR REPLACE VIEW time_based_performance AS
SELECT 
    hour_of_day,
    day_of_week,
    time_category,
    market_session,
    SUM(total_trades) as total_trades,
    AVG(CASE WHEN total_trades > 0 THEN successful_trades::DECIMAL / total_trades ELSE 0 END) as avg_success_rate,
    AVG(total_pnl) as avg_pnl,
    AVG(volatility) as avg_volatility
FROM time_performance_tracking 
GROUP BY hour_of_day, day_of_week, time_category, market_session
ORDER BY hour_of_day, day_of_week;

-- Strategy intelligence summary
CREATE OR REPLACE VIEW strategy_intelligence_summary AS
SELECT 
    sm.strategy_name,
    sm.performance_score,
    sm.total_trades,
    sm.winning_trades,
    sm.total_pnl,
    (sm.winning_trades::DECIMAL / NULLIF(sm.total_trades, 0)) * 100 as win_rate,
    COUNT(DISTINCT tm.symbol) as symbols_traded,
    AVG(tm.confidence) as avg_pattern_confidence,
    sm.updated_at
FROM strategy_memories sm
LEFT JOIN trading_memories tm ON tm.strategy = sm.strategy_name
GROUP BY sm.strategy_name, sm.performance_score, sm.total_trades, 
         sm.winning_trades, sm.total_pnl, sm.updated_at
ORDER BY sm.performance_score DESC;

-- Market intelligence insights
CREATE OR REPLACE VIEW market_intelligence_insights AS
SELECT 
    symbol,
    intelligence_type,
    source_type,
    COUNT(*) as insight_count,
    AVG(confidence_score) as avg_confidence,
    AVG(impact_score) as avg_impact,
    AVG(relevance_score) as avg_relevance,
    DATE_TRUNC('hour', created_at) as hour_bucket
FROM market_intelligence 
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY symbol, intelligence_type, source_type, DATE_TRUNC('hour', created_at)
ORDER BY hour_bucket DESC, avg_impact DESC;

-- ============================================================================
-- FUNCTIONS FOR ADVANCED ANALYTICS
-- ============================================================================

-- Function to calculate portfolio correlation
CREATE OR REPLACE FUNCTION calculate_portfolio_correlation(lookback_days INTEGER DEFAULT 30)
RETURNS TABLE(symbol1 VARCHAR(20), symbol2 VARCHAR(20), correlation DECIMAL(5,4)) AS $$
BEGIN
    RETURN QUERY
    WITH price_changes AS (
        SELECT 
            symbol,
            timestamp,
            close_price,
            LAG(close_price) OVER (PARTITION BY symbol ORDER BY timestamp) as prev_price
        FROM market_data 
        WHERE timestamp >= NOW() - (lookback_days || ' days')::INTERVAL
    ),
    returns AS (
        SELECT 
            symbol,
            timestamp,
            (close_price - prev_price) / prev_price as return_pct
        FROM price_changes 
        WHERE prev_price IS NOT NULL
    )
    SELECT 
        r1.symbol as symbol1,
        r2.symbol as symbol2,
        CORR(r1.return_pct, r2.return_pct)::DECIMAL(5,4) as correlation
    FROM returns r1
    JOIN returns r2 ON r1.timestamp = r2.timestamp AND r1.symbol < r2.symbol
    GROUP BY r1.symbol, r2.symbol
    HAVING COUNT(*) >= 10; -- Minimum data points for meaningful correlation
END;
$$ LANGUAGE plpgsql;

-- Function to update strategy performance
CREATE OR REPLACE FUNCTION update_strategy_performance(
    p_strategy_name VARCHAR(50),
    p_trade_result BOOLEAN,
    p_pnl DECIMAL(15,8)
) RETURNS VOID AS $$
BEGIN
    INSERT INTO strategy_memories (strategy_name, memory_data, performance_score, total_trades, winning_trades, total_pnl)
    VALUES (
        p_strategy_name,
        '{"last_updated": "' || NOW() || '"}',
        CASE WHEN p_trade_result THEN p_pnl ELSE -ABS(p_pnl) END,
        1,
        CASE WHEN p_trade_result THEN 1 ELSE 0 END,
        p_pnl
    )
    ON CONFLICT (strategy_name) DO UPDATE SET
        total_trades = strategy_memories.total_trades + 1,
        winning_trades = strategy_memories.winning_trades + CASE WHEN p_trade_result THEN 1 ELSE 0 END,
        total_pnl = strategy_memories.total_pnl + p_pnl,
        performance_score = (strategy_memories.total_pnl + p_pnl) / (strategy_memories.total_trades + 1),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- INDEXES FOR OPTIMAL PERFORMANCE
-- ============================================================================

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_trading_memories_composite ON trading_memories(symbol, strategy, timestamp, success);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_composite ON market_intelligence(symbol, intelligence_type, created_at, impact_score);
CREATE INDEX IF NOT EXISTS idx_firecrawl_data_composite ON firecrawl_data(source_type, crawled_at, processed, sentiment_score);

-- Partial indexes for active/recent data
CREATE INDEX IF NOT EXISTS idx_trading_memories_recent ON trading_memories(timestamp) WHERE timestamp >= NOW() - INTERVAL '30 days';
CREATE INDEX IF NOT EXISTS idx_market_intelligence_active ON market_intelligence(created_at) WHERE expires_at IS NULL OR expires_at > NOW();

-- ============================================================================
-- TRIGGERS FOR DATA INTEGRITY
-- ============================================================================

-- Update timestamp trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply update triggers to relevant tables
DROP TRIGGER IF EXISTS update_strategy_memories_updated_at ON strategy_memories;
CREATE TRIGGER update_strategy_memories_updated_at 
    BEFORE UPDATE ON strategy_memories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_market_intelligence_updated_at ON market_intelligence;
CREATE TRIGGER update_market_intelligence_updated_at 
    BEFORE UPDATE ON market_intelligence 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_huggingface_datasets_updated_at ON huggingface_datasets;
CREATE TRIGGER update_huggingface_datasets_updated_at 
    BEFORE UPDATE ON huggingface_datasets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- INITIAL DATA SETUP
-- ============================================================================

-- Insert initial strategy memory entries for common strategies
INSERT INTO strategy_memories (strategy_name, memory_data, performance_score) VALUES
    ('momentum', '{"initialized": true, "default_params": {"lookback": 14, "threshold": 0.02}}', 0),
    ('mean_reversion', '{"initialized": true, "default_params": {"lookback": 20, "std_dev": 2.0}}', 0),
    ('trend_following', '{"initialized": true, "default_params": {"fast_ema": 12, "slow_ema": 26}}', 0),
    ('adaptive', '{"initialized": true, "default_params": {"adaptation_rate": 0.1}}', 0)
ON CONFLICT (strategy_name) DO NOTHING;

-- Create default time performance tracking entries
INSERT INTO time_performance_tracking (time_category, market_session, hour_of_day, day_of_week) 
SELECT 
    'market_hours' as time_category,
    'overlap' as market_session,
    h.hour_of_day,
    d.day_of_week
FROM generate_series(0, 23) h(hour_of_day)
CROSS JOIN generate_series(0, 6) d(day_of_week)
ON CONFLICT DO NOTHING;

COMMIT;

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================
SELECT 'Super GPT Bybit Bot Database Schema Created Successfully!' as status,
       'All tables, indexes, views, and functions have been created.' as message,
       'Ready for full bot operation with advanced features.' as ready_status;
