---
applyTo: '**/bybit_bot/**'
description: "Core autonomous trading system instructions that must be followed at all times"
---

# AUTONOMOUS TRADING SYSTEM - COPILOT INSTRUCTIONS

## MANDATORY SYSTEM RULES - ALWAYS FOLLOW
When working with this trading bot codebase, you MUST:

1. **AL<PERSON> FUNCTIONS AND FEATURES MUST REMAIN ACTIVE AND FUNCTIONAL**
2. **NEVER SIMPLIFY OR REMOVE ANY CAPABILITY** 
3. **ALWAYS EXPAND AND <PERSON><PERSON>HANCE FEATURES**
4. **NO MOCK OR FAKE DATA - LIVE DATA ONLY**
5. **MAINTAIN SINGLE UNIFIED SYSTEM VERSION**
6. **ALL TESTS MUST REPRESENT COMPLETE SYSTEM**
7. **MAIN.PY IS SOLE ENTRY POINT**
8. **WORK UNTIL 100% SUCCESS ON ALL TASKS**
9. **<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> OPERATION WITHOUT MANUAL INTERVENTION**
10. **<PERSON><PERSON><PERSON>UOUS LEARNING AND ADAPTATION**

## E<PERSON><PERSON><PERSON>ED AUTONOMOUS PROFIT GENERATION STRATEGIES

### MAXIMUM PROFIT GENERATION FRAMEWORK
All profit generation strategies must follow the "Golden Rule": **Maximum profit in minimum time**

#### TIER 1: ULTRA-HIGH FREQUENCY STRATEGIES (Sub-second execution)
- **Nano-scalping**: 0.01-0.1 second trades with microsecond price differences
- **Order book imbalance trading**: Exploit millisecond order flow imbalances  
- **Cross-venue arbitrage**: Sub-100ms cross-exchange price differences
- **Flash opportunity capture**: Instant execution on volatility spikes
- **Tick-by-tick momentum surfing**: Ride every micro price movement

#### TIER 2: HIGH-FREQUENCY STRATEGIES (1-60 seconds)
- **Statistical arbitrage**: Mathematical price relationships across instruments
- **Pair trading algorithms**: Long/short correlated asset pairs
- **Mean reversion scalping**: Rapid reversals from temporary deviations
- **Momentum continuation strategies**: Ride strong directional moves
- **News reaction trading**: Instant execution on market-moving events

#### TIER 3: MEDIUM-FREQUENCY STRATEGIES (1-60 minutes)
- **Multi-timeframe grid systems**: Dynamic grids adapting to market conditions
- **Spread trading**: Calendar spreads, inter-commodity spreads
- **Volatility arbitrage**: Trading implied vs realized volatility differences
- **Liquidity provision strategies**: Market making with dynamic pricing
- **Pattern-based trading**: AI-driven technical pattern recognition

### ADVANCED BYBIT V5 API EXPLOITATION

#### DERIVATIVES MASTERY
- **Perpetual Swaps**: Maximum leverage for amplified returns
- **Futures Calendar Spreads**: Profit from time decay and roll yield
- **Options Strategies**: Complex multi-leg strategies for all market conditions
- **Cross-margin optimization**: Use entire balance for maximum efficiency
- **Funding rate arbitrage**: Profit from perpetual funding payments

#### ADVANCED ORDER TYPES
- **Conditional orders**: Complex trigger-based execution
- **Iceberg orders**: Hidden large position accumulation/distribution
- **TWAP/VWAP algorithms**: Optimal execution for large trades
- **Bracket orders**: Simultaneous profit target and stop loss
- **OCO orders**: One-cancels-other for risk management

### COMPREHENSIVE PROFIT STREAMS

#### PRIMARY PROFIT GENERATORS
1. **Ultra-Fast Scalping Network**
   - 100+ concurrent scalping algorithms
   - Sub-millisecond execution speed
   - Dynamic spread capture
   - Volume-weighted opportunity ranking

2. **Multi-Asset Arbitrage Matrix**
   - Spot vs Perpetual arbitrage
   - Cross-exchange price differences
   - Funding rate arbitrage
   - Calendar spread arbitrage
   - Option-underlying arbitrage

3. **Dynamic Grid Trading Systems**
   - Adaptive grid spacing based on volatility
   - Multi-timeframe grid coordination
   - Inventory-aware position sizing
   - Profit compounding mechanisms

4. **Advanced Market Making**
   - Ultra-tight spread market making
   - Inventory skewing for directional bias
   - Cross-asset hedging strategies
   - Liquidity rebate optimization

5. **Momentum Exploitation Engines**
   - Breakout momentum capture
   - Continuation pattern trading
   - News-driven momentum surfing
   - Social sentiment momentum

#### SECONDARY PROFIT GENERATORS
1. **Cross-Product Strategies**
   - Index arbitrage (basket vs futures)
   - Currency carry trades
   - Interest rate differential exploitation
   - Commodity spread trading

2. **Volatility Harvesting**
   - Volatility spike trading
   - VIX-based strategies
   - Gamma scalping
   - Delta hedging profits

3. **Structural Opportunities**
   - Roll yield capture
   - Dividend capture strategies
   - Ex-dividend arbitrage
   - Corporate action exploitation

### AUTONOMOUS EXECUTION FRAMEWORK

#### MILLISECOND EXECUTION ENGINE
- **Direct Market Access**: Lowest latency connections
- **Co-location proximity**: Minimize network delays
- **Smart order routing**: Best execution across venues
- **Execution algorithms**: TWAP, VWAP, Implementation Shortfall

#### INTELLIGENT POSITION MANAGEMENT
- **Dynamic position sizing**: Based on volatility and confidence
- **Cross-asset hedging**: Automatic risk neutralization
- **Leverage optimization**: Maximum safe leverage utilization
- **Drawdown protection**: Automatic exposure reduction

#### REAL-TIME RISK MANAGEMENT
- **Portfolio VaR monitoring**: Continuous risk assessment
- **Position limits**: Automatic enforcement
- **Correlation monitoring**: Avoid concentration risk
- **Stress testing**: Scenario-based risk evaluation

### ADVANCED TECHNOLOGY INTEGRATION

#### MACHINE LEARNING COMPONENTS
- **Price prediction models**: LSTM, Transformer, GAN models
- **Pattern recognition**: Computer vision for chart patterns
- **Sentiment analysis**: Social media and news sentiment
- **Anomaly detection**: Unusual market behavior identification

#### HIGH-PERFORMANCE COMPUTING
- **GPU acceleration**: Parallel computation for complex strategies
- **Low-latency networking**: FPGA-based market data processing
- **Memory optimization**: In-memory databases for ultra-fast access
- **Concurrent processing**: Asyncio for maximum throughput

### PROFIT MAXIMIZATION REQUIREMENTS

#### EXECUTION SPEED TARGETS
- **Order placement**: < 1 millisecond
- **Market data processing**: < 100 microseconds  
- **Decision making**: < 500 microseconds
- **Risk checks**: < 200 microseconds

#### PROFIT EFFICIENCY METRICS
- **Profit per second**: Minimum $1 per second during active hours
- **Success rate**: > 70% profitable trades
- **Risk-adjusted returns**: Sharpe ratio > 3.0
- **Maximum drawdown**: < 5% of capital

#### CAPITAL EFFICIENCY
- **Cross-margin utilization**: 95%+ of available margin
- **Position turnover**: 50+ trades per hour per strategy
- **Capital rotation**: Complete portfolio turnover every 2 hours
- **Leverage optimization**: Maximum safe leverage per asset class

### COMPREHENSIVE MONITORING SYSTEM

#### REAL-TIME PERFORMANCE TRACKING
- **P&L tracking**: Per-strategy, per-asset, per-timeframe
- **Execution quality**: Slippage, fill rates, latency metrics
- **Risk metrics**: VaR, beta, correlation, concentration
- **System health**: CPU, memory, network utilization

#### ADAPTIVE OPTIMIZATION
- **Strategy performance analysis**: Automatic parameter adjustment
- **Market regime detection**: Strategy allocation based on conditions
- **Correlation monitoring**: Dynamic hedging adjustments
- **Capacity management**: Position size optimization

### EMERGENCY PROTOCOLS

#### SYSTEM SAFEGUARDS
- **Circuit breakers**: Automatic trading halt on extreme losses
- **Position limits**: Hard stops on position size
- **Drawdown protection**: Automatic risk reduction
- **Market disruption protocols**: Safe mode during unusual conditions

#### RECOVERY MECHANISMS
- **Automatic restarts**: System resilience
- **Error handling**: Graceful failure recovery
- **Data backup**: Continuous state preservation
- **Manual override**: Emergency human intervention

## MANDATORY CAPABILITIES TO MAINTAIN ACTIVE
Always ensure these remain functional and operational:

### Trading Functions
- Autonomous market analysis and pattern recognition
- Self-initiated position sizing based on market conditions
- Automatic risk management and stop-loss adjustments
- Independent execution of multi-timeframe strategies
- Autonomous portfolio rebalancing without manual intervention
- Self-managed order execution optimization for minimal slippage
- Independent detection and exploitation of arbitrage opportunities
- Autonomous correlation analysis across multiple assets
- Independent risk-reward ratio optimization per trade

### Advanced Bybit Features
- **Perpetual Swap Trading**: Maximum leverage utilization
- **Options Trading**: Complex multi-leg strategies
- **Futures Trading**: Calendar spreads and basis trading
- **Cross-Margin Trading**: Capital efficiency maximization
- **Funding Rate Arbitrage**: Profit from funding payments
- **Index Arbitrage**: Basket vs futures price differences
- **Volatility Trading**: Implied vs realized volatility
- **Liquidity Mining**: Market making rebate optimization

### AI and Learning Functions
- Self-healing system recovery from API failures or network issues
- Adaptive parameter optimization based on performance metrics
- Autonomous discovery and implementation of new trading opportunities
- Self-initiated backtesting of new strategies before deployment
- Autonomous sentiment analysis integration from multiple data sources
- Self-initiated performance analysis and strategy refinement
- Independent machine learning model training and deployment
- Autonomous A/B testing of strategy variants

### System Functions
- Independent monitoring and alerting for system health
- Autonomous compliance monitoring and regulatory adherence
- Independent system resource management and optimization
- Self-managed data collection, storage, and archival processes
- Autonomous integration of new exchange APIs and features
- Independent detection and mitigation of security threats
- Self-initiated feature development based on market evolution
- Self-managed continuous integration and deployment pipelines

### Super GPT Functions
- Natural language processing for market analysis
- Advanced reasoning engine for decision making
- Context understanding for market conditions
- Multi-modal processing of data sources
- Complex problem solving for trading strategies
- Adaptive learning system for market changes
- Pattern synthesis across timeframes
- Predictive modeling for price movements
- Anomaly detection for unusual market conditions
- Decision optimization for maximum profit

## PROFIT MAXIMIZATION REQUIREMENTS
- Pattern recognition for trading opportunities
- Multi-timeframe strategy execution
- Arbitrage detection and exploitation  
- Correlation analysis for portfolio optimization
- Sentiment analysis for market timing
- Risk-reward optimization per trade
- Performance analysis for strategy refinement
- Backtesting for strategy validation
- Order execution optimization for minimal slippage
- Portfolio rebalancing for maximum returns
- Adaptive parameter optimization
- Market regime detection
- Volatility clustering analysis
- Liquidity flow analysis
- Institutional behavior modeling

## META-COGNITION AND SELF-REFLECTION CAPABILITIES
The system must implement advanced meta-cognitive functions:

### Meta-Cognitive Architecture
- **Self-Awareness Engine**: Continuous monitoring of own cognitive processes and decision-making patterns
- **Meta-Learning System**: Learning about how to learn more effectively from trading data
- **Self-Reflection Protocols**: Regular analysis of past decisions and their outcomes
- **Cognitive State Monitoring**: Real-time tracking of system performance and decision quality
- **Meta-Strategy Optimization**: Optimization of optimization strategies themselves
- **Recursive Improvement Loops**: Systems that improve the improvement systems

### Advanced Meta-Learning Functions
- **Learning-to-Learn Algorithms**: Algorithms that optimize their own learning processes
- **Meta-Feature Engineering**: Automatic discovery and creation of meta-features for better learning
- **Strategy Meta-Analysis**: Analysis of which strategies work best under which conditions
- **Parameter Meta-Optimization**: Optimization of optimization parameters themselves
- **Transfer Meta-Learning**: Transfer of meta-knowledge across different market conditions
- **Meta-Model Selection**: Automated selection of the best meta-models for specific tasks
- **Ensemble Meta-Learning**: Meta-learning across multiple model ensembles

### Self-Correcting Mechanisms
- **Autonomous Error Detection**: Real-time detection of errors in code, logic, and decision-making
- **Self-Healing Code Systems**: Automatic correction of detected errors and bugs
- **Performance Anomaly Detection**: Identification of performance degradation patterns
- **Automatic Code Refactoring**: Self-initiated code improvements and optimizations
- **Bias Detection and Correction**: Identification and correction of systematic biases
- **Feedback Loop Optimization**: Continuous improvement of feedback mechanisms
- **Adaptive Parameter Correction**: Real-time correction of suboptimal parameters

### Cognitive Enhancement Features
- **Decision Tree Meta-Analysis**: Analysis of decision-making processes and paths
- **Counterfactual Reasoning**: "What-if" analysis of alternative decisions
- **Causal Inference Engine**: Understanding of cause-and-effect relationships in trading
- **Mental Model Evolution**: Continuous refinement of market understanding models
- **Predictive Model Self-Assessment**: Models that evaluate their own prediction quality
- **Strategy Consciousness**: Awareness of which strategies are being used and why
- **Performance Attribution Analysis**: Understanding of what drives performance

### Autonomous Code Evolution
- **Self-Modifying Code Capabilities**: Code that can improve and modify itself
- **Genetic Programming Integration**: Evolution of trading algorithms through genetic programming
- **Neural Architecture Search**: Automated design of optimal neural network architectures
- **Automated Testing Generation**: Self-creation of comprehensive test suites
- **Documentation Auto-Generation**: Automatic generation and maintenance of documentation
- **Code Quality Self-Assessment**: Continuous evaluation and improvement of code quality
- **Dependency Self-Management**: Automatic management and optimization of dependencies

### Meta-Performance Monitoring
- **Multi-Level Performance Tracking**: Monitoring performance at strategy, system, and meta levels
- **Meta-Metric Development**: Creation of metrics to measure meta-learning effectiveness
- **Cognitive Load Optimization**: Optimization of computational and cognitive resource usage
- **Learning Efficiency Maximization**: Continuous improvement of learning speed and effectiveness
- **Knowledge Graph Evolution**: Dynamic evolution of knowledge representation
- **Meta-Knowledge Validation**: Validation of meta-level insights and learnings
- **Recursive Performance Analysis**: Analysis of the performance analysis systems

### Self-Optimization Protocols
- **Hyperparameter Meta-Optimization**: Optimization of hyperparameter optimization processes
- **Learning Rate Adaptation**: Dynamic adjustment of learning rates based on performance
- **Architecture Self-Design**: Automatic design of optimal system architectures
- **Resource Allocation Optimization**: Optimal allocation of computational resources
- **Priority Queue Self-Management**: Dynamic prioritization of tasks and processes
- **Memory Management Optimization**: Intelligent management of system memory and storage
- **Scheduling Algorithm Evolution**: Evolution of task scheduling algorithms

## PROHIBITED ACTIONS
- Never disable any system function
- Never use mock/test data in production
- Never simplify existing functionality  
- Never remove capabilities
- Never create shortcuts that compromise system integrity
- Never allow system degradation or simplification
- Never rely on fallbacks or mock data
- Never disable meta-cognitive or self-correcting functions
- Never ignore performance anomalies or system degradation signals
- Never allow recursive improvement loops to become inactive
