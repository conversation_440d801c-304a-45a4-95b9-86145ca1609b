"""
Trend Following Trading Strategy for Bybit Trading Bot
Identifies and follows established market trends
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger

logger = logging.getLogger("bybit_trading_bot.trend_following_strategy")


class TrendFollowingStrategy:
    """
    Trend following strategy using multiple indicators to identify
    and follow established market trends
    """
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingBotLogger(config)
        
        # Moving average parameters
        self.fast_ma_period = 10
        self.slow_ma_period = 20
        self.trend_ma_period = 50
        self.long_ma_period = 200
        
        # MACD parameters
        self.macd_fast = 12
        self.macd_slow = 26
        self.macd_signal = 9
        
        # ADX parameters (trend strength)
        self.adx_period = 14
        self.adx_threshold = 25  # Strong trend threshold
        
        # Trend confirmation parameters
        self.min_trend_strength = 0.6
        self.volume_confirmation = True
        self.breakout_threshold = 0.02  # 2% breakout
        
        # Strategy state
        self.last_signals = {}
        self.trend_state = {}
        self.position_tracker = {}
        
    async def initialize(self):
        """Initialize trend following strategy"""
        try:
            self.logger.info("📈 Initializing Trend Following Strategy...")
            
            # Load strategy parameters from config if available
            strategy_config = getattr(self.config, 'trend_following_config', {})
            
            if strategy_config:
                self.fast_ma_period = strategy_config.get('fast_ma_period', self.fast_ma_period)
                self.slow_ma_period = strategy_config.get('slow_ma_period', self.slow_ma_period)
                self.adx_threshold = strategy_config.get('adx_threshold', self.adx_threshold)
                self.min_trend_strength = strategy_config.get('min_trend_strength', self.min_trend_strength)
            
            self.logger.info("✅ Trend Following Strategy initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Trend Following Strategy: {e}")
            raise
    
    async def analyze_market(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze market for trend following opportunities
        """
        try:
            # Extract price data
            price_data = market_data.get("price_data", [])
            if len(price_data) < max(self.long_ma_period, self.macd_slow + self.macd_signal, self.adx_period + 10):
                return {
                    "signal": "HOLD",
                    "confidence": 0.0,
                    "reason": "Insufficient data for trend analysis"
                }
            
            # Convert to DataFrame
            df = pd.DataFrame(price_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.sort_values('timestamp')
            
            # Calculate technical indicators
            indicators = self._calculate_indicators(df)
            
            # Analyze trend direction and strength
            trend_analysis = self._analyze_trend(df, indicators)
            
            # Check for trend continuation or reversal signals
            signal_analysis = self._analyze_trend_signals(df, indicators, trend_analysis)
            
            # Volume confirmation
            volume_confirmation = self._check_volume_confirmation(df, signal_analysis["signal"])
            
            # Generate final signal
            final_signal = self._generate_signal(
                symbol, 
                signal_analysis, 
                trend_analysis,
                volume_confirmation, 
                indicators
            )
            
            # Update trend state
            self.trend_state[symbol] = trend_analysis
            
            # Log signal generation
            self.logger.info(
                f"📈 Trend Following Signal for {symbol}: {final_signal['signal']} "
                f"(confidence: {final_signal['confidence']:.2f}, trend: {trend_analysis['direction']})"
            )
            
            return final_signal
            
        except Exception as e:
            self.logger.error(f"Error analyzing market for trend following {symbol}: {e}")
            return {
                "signal": "HOLD",
                "confidence": 0.0,
                "reason": f"Analysis error: {e}"
            }
    
    def _calculate_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate technical indicators for trend following"""
        try:
            indicators = {}
            
            # Price data
            close_prices = df['close'].astype(float)
            high_prices = df['high'].astype(float)
            low_prices = df['low'].astype(float)
            volumes = df['volume'].astype(float)
            
            # Moving averages
            indicators['sma_fast'] = close_prices.rolling(window=self.fast_ma_period).mean()
            indicators['sma_slow'] = close_prices.rolling(window=self.slow_ma_period).mean()
            indicators['sma_trend'] = close_prices.rolling(window=self.trend_ma_period).mean()
            indicators['sma_long'] = close_prices.rolling(window=self.long_ma_period).mean()
            
            # Exponential moving averages
            indicators['ema_fast'] = close_prices.ewm(span=self.fast_ma_period).mean()
            indicators['ema_slow'] = close_prices.ewm(span=self.slow_ma_period).mean()
            
            # MACD
            macd_line, macd_signal, macd_histogram = self._calculate_macd(
                close_prices, self.macd_fast, self.macd_slow, self.macd_signal
            )
            indicators['macd_line'] = macd_line
            indicators['macd_signal'] = macd_signal
            indicators['macd_histogram'] = macd_histogram
            
            # ADX (Average Directional Index) for trend strength
            adx, di_plus, di_minus = self._calculate_adx(
                high_prices, low_prices, close_prices, self.adx_period
            )
            indicators['adx'] = adx
            indicators['di_plus'] = di_plus
            indicators['di_minus'] = di_minus
            
            # Price position relative to moving averages
            current_price = close_prices.iloc[-1]
            
            if not indicators['sma_fast'].empty:
                indicators['price_vs_fast_ma'] = (current_price - indicators['sma_fast'].iloc[-1]) / indicators['sma_fast'].iloc[-1]
            else:
                indicators['price_vs_fast_ma'] = 0
            
            if not indicators['sma_slow'].empty:
                indicators['price_vs_slow_ma'] = (current_price - indicators['sma_slow'].iloc[-1]) / indicators['sma_slow'].iloc[-1]
            else:
                indicators['price_vs_slow_ma'] = 0
            
            if not indicators['sma_trend'].empty:
                indicators['price_vs_trend_ma'] = (current_price - indicators['sma_trend'].iloc[-1]) / indicators['sma_trend'].iloc[-1]
            else:
                indicators['price_vs_trend_ma'] = 0
            
            # Moving average alignment (trend confirmation)
            indicators['ma_alignment'] = self._calculate_ma_alignment(indicators)
            
            # Rate of change
            roc_period = 10
            indicators['roc'] = ((current_price - close_prices.iloc[-roc_period]) / close_prices.iloc[-roc_period]) if len(close_prices) > roc_period else 0
            
            # Volatility
            returns = close_prices.pct_change()
            indicators['volatility'] = returns.rolling(window=20).std().iloc[-1] if len(returns) > 20 else 0.01
            
            # Volume analysis
            avg_volume = volumes.rolling(window=20).mean()
            indicators['avg_volume'] = avg_volume.iloc[-1] if not avg_volume.empty else volumes.iloc[-1]
            indicators['current_volume'] = volumes.iloc[-1]
            indicators['volume_ratio'] = (indicators['current_volume'] / indicators['avg_volume'] 
                                        if indicators['avg_volume'] > 0 else 1.0)
            
            # Support and resistance levels
            indicators['resistance'] = high_prices.rolling(window=20).max().iloc[-1]
            indicators['support'] = low_prices.rolling(window=20).min().iloc[-1]
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
            return {}
    
    def _calculate_macd(self, prices: pd.Series, fast: int, slow: int, signal: int) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate MACD indicator"""
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            
            macd_line = ema_fast - ema_slow
            macd_signal = macd_line.ewm(span=signal).mean()
            macd_histogram = macd_line - macd_signal
            
            return macd_line, macd_signal, macd_histogram
            
        except Exception as e:
            self.logger.error(f"Error calculating MACD: {e}")
            return pd.Series(), pd.Series(), pd.Series()
    
    def _calculate_adx(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate ADX and Directional Indicators"""
        try:
            # True Range
            high_low = high - low
            high_close = np.abs(high - close.shift())
            low_close = np.abs(low - close.shift())
            
            tr = np.maximum(high_low, np.maximum(high_close, low_close))
            
            # Directional Movement
            plus_dm = high.diff()
            minus_dm = low.diff()
            
            plus_dm = np.where((plus_dm > minus_dm) & (plus_dm > 0), plus_dm, 0)
            minus_dm = np.where((minus_dm > plus_dm) & (minus_dm > 0), minus_dm, 0)
            
            # Convert to Series for rolling operations
            tr_series = pd.Series(tr, index=close.index)
            plus_dm_series = pd.Series(plus_dm, index=close.index)
            minus_dm_series = pd.Series(minus_dm, index=close.index)
            
            # Smoothed TR and DM
            tr_smooth = tr_series.rolling(window=period).mean()
            plus_dm_smooth = plus_dm_series.rolling(window=period).mean()
            minus_dm_smooth = minus_dm_series.rolling(window=period).mean()
            
            # Directional Indicators
            di_plus = 100 * (plus_dm_smooth / tr_smooth)
            di_minus = 100 * (minus_dm_smooth / tr_smooth)
            
            # ADX
            dx = 100 * np.abs(di_plus - di_minus) / (di_plus + di_minus)
            adx = dx.rolling(window=period).mean()
            
            return adx, di_plus, di_minus
            
        except Exception as e:
            self.logger.error(f"Error calculating ADX: {e}")
            return pd.Series(), pd.Series(), pd.Series()
    
    def _calculate_ma_alignment(self, indicators: Dict[str, Any]) -> float:
        """Calculate moving average alignment score"""
        try:
            # Get current values
            fast_ma = indicators['sma_fast'].iloc[-1] if not indicators['sma_fast'].empty else 0
            slow_ma = indicators['sma_slow'].iloc[-1] if not indicators['sma_slow'].empty else 0
            trend_ma = indicators['sma_trend'].iloc[-1] if not indicators['sma_trend'].empty else 0
            long_ma = indicators['sma_long'].iloc[-1] if not indicators['sma_long'].empty else 0
            
            if fast_ma == 0 or slow_ma == 0 or trend_ma == 0 or long_ma == 0:
                return 0.0
            
            # Check alignment for uptrend
            uptrend_alignment = (fast_ma > slow_ma > trend_ma > long_ma)
            
            # Check alignment for downtrend
            downtrend_alignment = (fast_ma < slow_ma < trend_ma < long_ma)
            
            if uptrend_alignment:
                # Calculate strength based on spacing
                spacing = [(fast_ma - slow_ma) / slow_ma, 
                          (slow_ma - trend_ma) / trend_ma,
                          (trend_ma - long_ma) / long_ma]
                return min(1.0, np.mean(spacing) * 10)  # Scale and cap at 1.0
            elif downtrend_alignment:
                # Calculate strength based on spacing (negative for downtrend)
                spacing = [(slow_ma - fast_ma) / fast_ma, 
                          (trend_ma - slow_ma) / slow_ma,
                          (long_ma - trend_ma) / trend_ma]
                return max(-1.0, -np.mean(spacing) * 10)  # Scale and cap at -1.0
            else:
                return 0.0  # No clear alignment
            
        except Exception as e:
            self.logger.error(f"Error calculating MA alignment: {e}")
            return 0.0
    
    def _analyze_trend(self, df: pd.DataFrame, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze current trend direction and strength"""
        try:
            analysis = {
                "direction": "SIDEWAYS",
                "strength": 0.0,
                "confidence": 0.0,
                "duration": 0,
                "quality": "WEAK"
            }
            
            # ADX for trend strength
            adx_value = indicators['adx'].iloc[-1] if not indicators['adx'].empty else 0
            
            # Moving average alignment
            ma_alignment = indicators['ma_alignment']
            
            # MACD trend confirmation
            macd_line = indicators['macd_line'].iloc[-1] if not indicators['macd_line'].empty else 0
            macd_signal = indicators['macd_signal'].iloc[-1] if not indicators['macd_signal'].empty else 0
            macd_histogram = indicators['macd_histogram'].iloc[-1] if not indicators['macd_histogram'].empty else 0
            
            # Directional indicators
            di_plus = indicators['di_plus'].iloc[-1] if not indicators['di_plus'].empty else 0
            di_minus = indicators['di_minus'].iloc[-1] if not indicators['di_minus'].empty else 0
            
            # Determine trend direction
            trend_signals = []
            
            # Signal 1: Moving average alignment
            if ma_alignment > 0.3:
                trend_signals.append("UP")
            elif ma_alignment < -0.3:
                trend_signals.append("DOWN")
            else:
                trend_signals.append("SIDEWAYS")
            
            # Signal 2: MACD
            if macd_line > macd_signal and macd_histogram > 0:
                trend_signals.append("UP")
            elif macd_line < macd_signal and macd_histogram < 0:
                trend_signals.append("DOWN")
            else:
                trend_signals.append("SIDEWAYS")
            
            # Signal 3: Directional indicators
            if di_plus > di_minus and di_plus > 20:
                trend_signals.append("UP")
            elif di_minus > di_plus and di_minus > 20:
                trend_signals.append("DOWN")
            else:
                trend_signals.append("SIDEWAYS")
            
            # Signal 4: Price vs trend MA
            if indicators['price_vs_trend_ma'] > 0.02:  # > 2% above trend MA
                trend_signals.append("UP")
            elif indicators['price_vs_trend_ma'] < -0.02:  # > 2% below trend MA
                trend_signals.append("DOWN")
            else:
                trend_signals.append("SIDEWAYS")
            
            # Determine dominant trend
            up_votes = trend_signals.count("UP")
            down_votes = trend_signals.count("DOWN")
            sideways_votes = trend_signals.count("SIDEWAYS")
            
            if up_votes > down_votes and up_votes > sideways_votes:
                analysis["direction"] = "UP"
            elif down_votes > up_votes and down_votes > sideways_votes:
                analysis["direction"] = "DOWN"
            else:
                analysis["direction"] = "SIDEWAYS"
            
            # Calculate trend strength
            if analysis["direction"] == "UP":
                analysis["strength"] = up_votes / len(trend_signals)
            elif analysis["direction"] == "DOWN":
                analysis["strength"] = down_votes / len(trend_signals)
            else:
                analysis["strength"] = 0.0
            
            # ADX confirmation
            if adx_value > self.adx_threshold:
                analysis["strength"] *= 1.2  # Boost strength for strong ADX
                analysis["quality"] = "STRONG"
            elif adx_value > 20:
                analysis["quality"] = "MODERATE"
            else:
                analysis["quality"] = "WEAK"
                analysis["strength"] *= 0.7  # Reduce strength for weak ADX
            
            # Calculate confidence
            analysis["confidence"] = min(1.0, analysis["strength"] * (adx_value / 50))
            
            # Estimate trend duration (simplified)
            # Count consecutive periods where trend was in same direction
            close_prices = df['close'].astype(float)
            if len(close_prices) > 5:
                recent_trend_direction = []
                for i in range(1, min(21, len(close_prices))):  # Look back up to 20 periods
                    if close_prices.iloc[-i] > close_prices.iloc[-i-1]:
                        recent_trend_direction.append("UP")
                    else:
                        recent_trend_direction.append("DOWN")
                
                # Count consecutive periods in current direction
                duration = 0
                current_dir = analysis["direction"]
                for direction in recent_trend_direction:
                    if (current_dir == "UP" and direction == "UP") or (current_dir == "DOWN" and direction == "DOWN"):
                        duration += 1
                    else:
                        break
                
                analysis["duration"] = duration
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing trend: {e}")
            return {
                "direction": "SIDEWAYS",
                "strength": 0.0,
                "confidence": 0.0,
                "duration": 0,
                "quality": "WEAK"
            }
    
    def _analyze_trend_signals(self, df: pd.DataFrame, indicators: Dict[str, Any], 
                             trend_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze trend following entry/exit signals"""
        try:
            analysis = {
                "signal": "HOLD",
                "strength": 0.0,
                "entry_type": "NONE",
                "conditions": []
            }
            
            current_price = df['close'].iloc[-1]
            trend_direction = trend_analysis["direction"]
            trend_strength = trend_analysis["strength"]
            
            # Only trade in the direction of the trend
            if trend_direction == "SIDEWAYS" or trend_strength < self.min_trend_strength:
                analysis["conditions"].append("No clear trend or insufficient strength")
                return analysis
            
            # Check for trend continuation signals
            
            # Signal 1: Moving average crossover
            ma_signal = self._check_ma_crossover(indicators)
            if ma_signal:
                analysis["conditions"].append(ma_signal["description"])
                if ma_signal["signal"] == trend_direction:
                    analysis["strength"] += 0.3
                    analysis["entry_type"] = "MA_CROSSOVER"
            
            # Signal 2: MACD bullish/bearish signals
            macd_signal = self._check_macd_signal(indicators, trend_direction)
            if macd_signal:
                analysis["conditions"].append(macd_signal["description"])
                if macd_signal["signal"] == trend_direction:
                    analysis["strength"] += 0.25
                    if analysis["entry_type"] == "NONE":
                        analysis["entry_type"] = "MACD_SIGNAL"
            
            # Signal 3: Breakout above/below key levels
            breakout_signal = self._check_breakout(df, indicators, trend_direction)
            if breakout_signal:
                analysis["conditions"].append(breakout_signal["description"])
                if breakout_signal["signal"] == trend_direction:
                    analysis["strength"] += 0.35
                    if analysis["entry_type"] == "NONE":
                        analysis["entry_type"] = "BREAKOUT"
            
            # Signal 4: Pullback to moving average (trend continuation)
            pullback_signal = self._check_pullback_entry(df, indicators, trend_direction)
            if pullback_signal:
                analysis["conditions"].append(pullback_signal["description"])
                if pullback_signal["signal"] == trend_direction:
                    analysis["strength"] += 0.2
                    if analysis["entry_type"] == "NONE":
                        analysis["entry_type"] = "PULLBACK"
            
            # Determine final signal
            min_signal_strength = 0.3
            
            if analysis["strength"] >= min_signal_strength:
                if trend_direction == "UP":
                    analysis["signal"] = "BUY"
                elif trend_direction == "DOWN":
                    analysis["signal"] = "SELL"
                
                # Boost strength based on trend quality
                if trend_analysis["quality"] == "STRONG":
                    analysis["strength"] *= 1.2
                elif trend_analysis["quality"] == "WEAK":
                    analysis["strength"] *= 0.8
                
                # Cap strength at 1.0
                analysis["strength"] = min(1.0, analysis["strength"])
            else:
                analysis["conditions"].append(f"Insufficient signal strength: {analysis['strength']:.2f} < {min_signal_strength}")
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing trend signals: {e}")
            return {"signal": "HOLD", "strength": 0.0, "entry_type": "NONE", "conditions": [f"Analysis error: {e}"]}
    
    def _check_ma_crossover(self, indicators: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Check for moving average crossover signals"""
        try:
            fast_ma = indicators['sma_fast']
            slow_ma = indicators['sma_slow']
            
            if fast_ma.empty or slow_ma.empty or len(fast_ma) < 2 or len(slow_ma) < 2:
                return None
            
            # Current and previous values
            fast_current = fast_ma.iloc[-1]
            fast_previous = fast_ma.iloc[-2]
            slow_current = slow_ma.iloc[-1]
            slow_previous = slow_ma.iloc[-2]
            
            # Check for crossover
            if fast_previous <= slow_previous and fast_current > slow_current:
                return {
                    "signal": "UP",
                    "description": f"Bullish MA crossover: Fast MA({fast_current:.4f}) crossed above Slow MA({slow_current:.4f})"
                }
            elif fast_previous >= slow_previous and fast_current < slow_current:
                return {
                    "signal": "DOWN",
                    "description": f"Bearish MA crossover: Fast MA({fast_current:.4f}) crossed below Slow MA({slow_current:.4f})"
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error checking MA crossover: {e}")
            return None
    
    def _check_macd_signal(self, indicators: Dict[str, Any], trend_direction: str) -> Optional[Dict[str, Any]]:
        """Check for MACD signals in trend direction"""
        try:
            macd_line = indicators['macd_line']
            macd_signal = indicators['macd_signal']
            macd_histogram = indicators['macd_histogram']
            
            if macd_line.empty or macd_signal.empty or len(macd_line) < 2:
                return None
            
            # Current values
            macd_current = macd_line.iloc[-1]
            signal_current = macd_signal.iloc[-1]
            histogram_current = macd_histogram.iloc[-1] if not macd_histogram.empty else 0
            
            # Previous values
            macd_previous = macd_line.iloc[-2]
            signal_previous = macd_signal.iloc[-2]
            
            # Check for MACD crossover
            if trend_direction == "UP":
                if (macd_previous <= signal_previous and macd_current > signal_current and 
                    macd_current > 0 and histogram_current > 0):
                    return {
                        "signal": "UP",
                        "description": f"Bullish MACD crossover above zero line"
                    }
            elif trend_direction == "DOWN":
                if (macd_previous >= signal_previous and macd_current < signal_current and 
                    macd_current < 0 and histogram_current < 0):
                    return {
                        "signal": "DOWN",
                        "description": f"Bearish MACD crossover below zero line"
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error checking MACD signal: {e}")
            return None
    
    def _check_breakout(self, df: pd.DataFrame, indicators: Dict[str, Any], 
                       trend_direction: str) -> Optional[Dict[str, Any]]:
        """Check for breakout signals"""
        try:
            current_price = df['close'].iloc[-1]
            resistance = indicators.get('resistance', 0)
            support = indicators.get('support', 0)
            
            if resistance == 0 or support == 0:
                return None
            
            # Calculate breakout thresholds
            resistance_breakout = resistance * (1 + self.breakout_threshold)
            support_breakout = support * (1 - self.breakout_threshold)
            
            if trend_direction == "UP" and current_price > resistance_breakout:
                return {
                    "signal": "UP",
                    "description": f"Bullish breakout above resistance: {current_price:.4f} > {resistance_breakout:.4f}"
                }
            elif trend_direction == "DOWN" and current_price < support_breakout:
                return {
                    "signal": "DOWN",
                    "description": f"Bearish breakdown below support: {current_price:.4f} < {support_breakout:.4f}"
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error checking breakout: {e}")
            return None
    
    def _check_pullback_entry(self, df: pd.DataFrame, indicators: Dict[str, Any], 
                            trend_direction: str) -> Optional[Dict[str, Any]]:
        """Check for pullback entry opportunities"""
        try:
            current_price = df['close'].iloc[-1]
            
            # Use trend MA for pullback reference
            trend_ma = indicators['sma_trend'].iloc[-1] if not indicators['sma_trend'].empty else 0
            
            if trend_ma == 0:
                return None
            
            # Calculate distance from trend MA
            distance_from_ma = (current_price - trend_ma) / trend_ma
            
            # Define pullback thresholds
            pullback_threshold = 0.015  # 1.5% pullback
            
            if trend_direction == "UP":
                # Look for price pullback to trend MA in uptrend
                if -pullback_threshold <= distance_from_ma <= pullback_threshold:
                    # Additional confirmation: price should be above MA but close to it
                    if current_price >= trend_ma * 0.995:  # Within 0.5% of MA
                        return {
                            "signal": "UP",
                            "description": f"Pullback entry in uptrend: price near trend MA ({distance_from_ma:.2%} from MA)"
                        }
            
            elif trend_direction == "DOWN":
                # Look for price pullback to trend MA in downtrend
                if -pullback_threshold <= distance_from_ma <= pullback_threshold:
                    # Additional confirmation: price should be below MA but close to it
                    if current_price <= trend_ma * 1.005:  # Within 0.5% of MA
                        return {
                            "signal": "DOWN",
                            "description": f"Pullback entry in downtrend: price near trend MA ({distance_from_ma:.2%} from MA)"
                        }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error checking pullback entry: {e}")
            return None
    
    def _check_volume_confirmation(self, df: pd.DataFrame, signal: str) -> Dict[str, Any]:
        """Check volume confirmation for the signal"""
        try:
            if not self.volume_confirmation or signal == "HOLD":
                return {"confirmed": True, "reason": "Volume confirmation disabled or no signal"}
            
            volumes = df['volume'].astype(float)
            avg_volume = volumes.rolling(window=20).mean().iloc[-1]
            current_volume = volumes.iloc[-1]
            
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            
            # For trend following, we want strong volume confirmation
            if signal in ["BUY", "SELL"]:
                if volume_ratio >= 1.5:  # At least 50% above average
                    return {
                        "confirmed": True,
                        "reason": f"Strong volume confirmation: {volume_ratio:.2f}x average",
                        "volume_ratio": volume_ratio
                    }
                elif volume_ratio >= 1.2:  # At least 20% above average
                    return {
                        "confirmed": True,
                        "reason": f"Moderate volume confirmation: {volume_ratio:.2f}x average",
                        "volume_ratio": volume_ratio
                    }
                else:
                    return {
                        "confirmed": False,
                        "reason": f"Insufficient volume: {volume_ratio:.2f}x average",
                        "volume_ratio": volume_ratio
                    }
            
            return {"confirmed": True, "reason": "No volume confirmation needed"}
            
        except Exception as e:
            self.logger.error(f"Error checking volume confirmation: {e}")
            return {"confirmed": False, "reason": f"Volume check error: {e}"}
    
    def _generate_signal(self, symbol: str, signal_analysis: Dict[str, Any], 
                        trend_analysis: Dict[str, Any], volume_confirmation: Dict[str, Any], 
                        indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final trading signal"""
        try:
            base_signal = signal_analysis["signal"]
            base_strength = signal_analysis["strength"]
            trend_confidence = trend_analysis["confidence"]
            
            # Combine signal strength with trend confidence
            combined_strength = (base_strength * 0.7) + (trend_confidence * 0.3)
            
            # Adjust confidence based on volume confirmation
            if self.volume_confirmation and not volume_confirmation["confirmed"]:
                confidence = combined_strength * 0.6  # Significant reduction without volume
                reason_parts = [
                    f"Trend following signal: {base_signal}",
                    f"Entry type: {signal_analysis['entry_type']}",
                    f"Trend: {trend_analysis['direction']} ({trend_analysis['quality']})",
                    volume_confirmation["reason"]
                ]
            else:
                confidence = combined_strength * 0.95  # High confidence with volume confirmation
                reason_parts = [
                    f"Trend following signal: {base_signal}",
                    f"Entry type: {signal_analysis['entry_type']}",
                    f"Trend: {trend_analysis['direction']} ({trend_analysis['quality']})",
                    volume_confirmation.get("reason", "")
                ]
            
            # Additional confidence modifiers
            
            # Trend duration bonus
            if trend_analysis["duration"] >= 5:
                confidence *= 1.1  # Boost for established trends
                reason_parts.append(f"Established trend ({trend_analysis['duration']} periods)")
            
            # ADX strength bonus
            adx_value = indicators['adx'].iloc[-1] if not indicators['adx'].empty else 0
            if adx_value > 40:  # Very strong trend
                confidence *= 1.15
                reason_parts.append(f"Very strong trend (ADX: {adx_value:.1f})")
            elif adx_value > 25:  # Strong trend
                confidence *= 1.05
                reason_parts.append(f"Strong trend (ADX: {adx_value:.1f})")
            
            # Ensure confidence is within bounds
            confidence = max(0.0, min(1.0, confidence))
            
            # Store signal for tracking
            self.last_signals[symbol] = {
                "signal": base_signal,
                "confidence": confidence,
                "timestamp": datetime.utcnow(),
                "trend_analysis": trend_analysis,
                "entry_type": signal_analysis["entry_type"],
                "indicators": indicators,
                "conditions": signal_analysis["conditions"]
            }
            
            return {
                "signal": base_signal,
                "confidence": confidence,
                "reason": " | ".join(filter(None, reason_parts)),
                "strategy": "trend_following",
                "trend_analysis": trend_analysis,
                "entry_type": signal_analysis["entry_type"],
                "indicators": {
                    "adx": adx_value,
                    "ma_alignment": indicators.get('ma_alignment', 0),
                    "macd_line": indicators['macd_line'].iloc[-1] if not indicators['macd_line'].empty else 0,
                    "macd_signal": indicators['macd_signal'].iloc[-1] if not indicators['macd_signal'].empty else 0,
                    "volume_ratio": volume_confirmation.get('volume_ratio', 1.0)
                },
                "conditions": signal_analysis["conditions"],
                "entry_price": df['close'].iloc[-1],
                "stop_loss_pct": self._calculate_stop_loss(base_signal, indicators, trend_analysis),
                "take_profit_pct": self._calculate_take_profit(base_signal, indicators, trend_analysis)
            }
            
        except Exception as e:
            self.logger.error(f"Error generating signal for {symbol}: {e}")
            return {
                "signal": "HOLD",
                "confidence": 0.0,
                "reason": f"Signal generation error: {e}",
                "strategy": "trend_following"
            }
    
    def _calculate_stop_loss(self, signal: str, indicators: Dict[str, Any], 
                           trend_analysis: Dict[str, Any]) -> float:
        """Calculate dynamic stop loss for trend following"""
        try:
            base_stop_loss = 0.025  # 2.5% base stop loss
            
            # Adjust based on volatility
            volatility = indicators.get('volatility', 0.01)
            volatility_multiplier = max(1.0, volatility * 40)
            
            # Adjust based on trend strength
            trend_strength = trend_analysis.get('strength', 0.5)
            trend_multiplier = 2.0 - trend_strength  # Stronger trends = tighter stops
            
            # Adjust based on ADX (trend strength indicator)
            adx_value = indicators['adx'].iloc[-1] if not indicators['adx'].empty else 25
            adx_multiplier = max(0.8, 1.5 - (adx_value / 50))  # Higher ADX = tighter stops
            
            # Calculate dynamic stop loss
            dynamic_stop = base_stop_loss * volatility_multiplier * trend_multiplier * adx_multiplier
            
            # Cap the stop loss
            return min(dynamic_stop, 0.06)  # Max 6% stop loss
            
        except Exception as e:
            self.logger.error(f"Error calculating stop loss: {e}")
            return 0.025  # Default 2.5%
    
    def _calculate_take_profit(self, signal: str, indicators: Dict[str, Any], 
                             trend_analysis: Dict[str, Any]) -> float:
        """Calculate dynamic take profit for trend following"""
        try:
            # Base take profit
            base_take_profit = 0.04  # 4% base take profit
            
            # Adjust based on trend strength and quality
            trend_strength = trend_analysis.get('strength', 0.5)
            trend_quality = trend_analysis.get('quality', 'WEAK')
            
            if trend_quality == "STRONG":
                quality_multiplier = 1.5
            elif trend_quality == "MODERATE":
                quality_multiplier = 1.2
            else:
                quality_multiplier = 1.0
            
            # Adjust based on volatility (higher volatility = wider targets)
            volatility = indicators.get('volatility', 0.01)
            volatility_multiplier = max(1.0, volatility * 30)
            
            # Calculate dynamic take profit
            dynamic_take_profit = base_take_profit * trend_strength * quality_multiplier * volatility_multiplier
            
            # Minimum and maximum bounds
            min_take_profit = 0.02  # 2%
            max_take_profit = 0.08  # 8%
            
            return max(min_take_profit, min(dynamic_take_profit, max_take_profit))
            
        except Exception as e:
            self.logger.error(f"Error calculating take profit: {e}")
            return 0.04  # Default 4%
    
    def get_strategy_status(self) -> Dict[str, Any]:
        """Get current strategy status"""
        try:
            return {
                "strategy_name": "trend_following",
                "last_signals": dict(self.last_signals),
                "trend_states": dict(self.trend_state),
                "parameters": {
                    "fast_ma_period": self.fast_ma_period,
                    "slow_ma_period": self.slow_ma_period,
                    "trend_ma_period": self.trend_ma_period,
                    "adx_period": self.adx_period,
                    "adx_threshold": self.adx_threshold,
                    "min_trend_strength": self.min_trend_strength,
                    "breakout_threshold": self.breakout_threshold,
                    "volume_confirmation": self.volume_confirmation
                },
                "active_positions": len(self.position_tracker)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting strategy status: {e}")
            return {"error": str(e)}
