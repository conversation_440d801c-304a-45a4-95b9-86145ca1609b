# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: Python CI

on:
  push:
    branches: [ "main", "dev" ]
  pull_request:
    branches: [ "main", "dev" ]

permissions:
  contents: read

jobs:
  lint:

    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
        ref: ${{ github.event.pull_request.head.ref }}
        repository: ${{ github.event.pull_request.head.repo.full_name }}

    - name: Set up Python 3.9
      uses: actions/setup-python@v3
      with:
        python-version: "3.9"

    - name: Cache Python dependencies
      uses: actions/cache@v2
      with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 pytest
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    - name: <PERSON><PERSON> with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
 
  test:

    permissions:
      # Gives the action the necessary permissions for publishing new
      # comments in pull requests.
      pull-requests: write
      # Gives the action the necessary permissions for pushing data to the
      # python-coverage-comment-action branch, and for editing existing
      # comments (to avoid publishing multiple comments in the same PR)
      contents: write

    runs-on: ubuntu-latest
    timeout-minutes: 30
    strategy:
      matrix:
        python-version: ["3.9"]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}
          submodules: true

      - name: Configure git user SuperAGI-Bot
        run: |
          git config --global user.name "SuperAGI-Bot"
          git config --global user.email "<EMAIL>"
          
      - name: Set up Python 3.9
        uses: actions/setup-python@v3
        with:
          python-version: "3.9"

      - name: Cache dependencies
        uses: actions/cache@v2
        with:
            path: ~/.cache/pip
            key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
            restore-keys: |
              ${{ runner.os }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8 pytest
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
          
      - name: Test with pytest
        run: |
          pytest --cov=superagi --cov-branch --cov-report term-missing --cov-report xml \
            tests/unit_tests -s
        env:
          CI: true
          ENV: DEV
          PLAIN_OUTPUT: True
          REDIS_URL: "localhost:6379"
          IS_TESTING: True
          ENCRYPTION_KEY: "abcdefghijklmnopqrstuvwxyz123456"

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v3
