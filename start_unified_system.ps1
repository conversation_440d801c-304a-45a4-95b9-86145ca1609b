# ============================================================================
# UNIFIED AUTONOMOUS TRADING SYSTEM - MASTER STARTUP SCRIPT (PowerShell)
# ============================================================================
# This is the SINGLE entry point for the complete autonomous trading system
# Includes: SuperGPT, AI Systems, Profit Engines, Multi-Agent Orchestration
# ============================================================================

param(
    [switch]$SkipValidation,
    [switch]$Development,
    [switch]$Production,
    [string]$Environment = "bybit-trader"
)

# Set up console
$host.UI.RawUI.WindowTitle = "Unified Autonomous Trading System - Master Launcher"

# Color functions
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Red"     = [ConsoleColor]::Red
        "Green"   = [ConsoleColor]::Green
        "Yellow"  = [ConsoleColor]::Yellow
        "Blue"    = [ConsoleColor]::Blue
        "Magenta" = [ConsoleColor]::Magenta
        "Cyan"    = [ConsoleColor]::Cyan
        "White"   = [ConsoleColor]::White
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

function Write-Header {
    Write-Host ""
    Write-ColorOutput "================================================================================" "Cyan"
    Write-ColorOutput "🚀 UNIFIED AUTONOMOUS TRADING SYSTEM - MASTER LAUNCHER" "White"
    Write-ColorOutput "================================================================================" "Cyan"
    Write-ColorOutput "🤖 Version: 4.0.0 - Complete AI Integration" "Yellow"
    Write-ColorOutput "🧠 Features: SuperGPT + Meta-Cognition + Self-Evolution + Hyper Profit" "Yellow"
    Write-ColorOutput "📊 Operation: Fully Autonomous with Zero Manual Intervention" "Yellow"
    Write-ColorOutput "================================================================================" "Cyan"
    Write-Host ""
}

function Test-Prerequisites {
    Write-ColorOutput "🔍 Checking prerequisites..." "Blue"
    
    # Check if conda is available
    try {
        $condaVersion = conda --version 2>$null
        if ($condaVersion) {
            Write-ColorOutput "✅ Conda found: $condaVersion" "Green"
        }
        else {
            throw "Conda not found"
        }
    }
    catch {
        Write-ColorOutput "❌ ERROR: Conda not found in PATH" "Red"
        Write-ColorOutput "💡 Please install Anaconda/Miniconda and add it to PATH" "Yellow"
        return $false
    }
    
    # Check if we're in the correct directory
    if (-not (Test-Path "main_unified_system.py")) {
        Write-ColorOutput "❌ ERROR: main_unified_system.py not found" "Red"
        Write-ColorOutput "💡 Please run this script from the BOT directory" "Yellow"
        return $false
    }
    
    return $true
}

function Initialize-Environment {
    param([string]$EnvName)
    
    Write-ColorOutput "🔧 Activating conda environment: $EnvName..." "Blue"
    
    # Activate conda environment
    try {
        & conda activate $EnvName
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Environment activated: $EnvName" "Green"
            return $true
        }
        else {
            throw "Failed to activate $EnvName"
        }
    }
    catch {
        Write-ColorOutput "⚠️ WARNING: Failed to activate $EnvName environment" "Yellow"
        
        # Try alternative environment
        $altEnv = if ($EnvName -eq "bybit-trader") { "autogpt-trader" } else { "bybit-trader" }
        Write-ColorOutput "💡 Trying alternative environment: $altEnv..." "Yellow"
        
        try {
            & conda activate $altEnv
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Environment activated: $altEnv" "Green"
                return $true
            }
            else {
                throw "Failed to activate $altEnv"
            }
        }
        catch {
            Write-ColorOutput "❌ ERROR: Neither $EnvName nor $altEnv environment found" "Red"
            Write-ColorOutput "💡 Please create conda environment first" "Yellow"
            return $false
        }
    }
}

function Test-PythonSetup {
    Write-ColorOutput "🐍 Checking Python setup..." "Blue"
    
    try {
        $pythonVersion = python --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Python version: $pythonVersion" "Green"
        }
        else {
            throw "Python not available"
        }
    }
    catch {
        Write-ColorOutput "❌ ERROR: Python not available" "Red"
        return $false
    }
    
    return $true
}

function Install-Dependencies {
    Write-ColorOutput "📦 Checking dependencies..." "Blue"
    
    if (Test-Path "requirements.txt") {
        Write-ColorOutput "🔄 Installing/updating dependencies..." "Yellow"
        
        try {
            & pip install -r requirements.txt --quiet
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✅ Dependencies updated successfully" "Green"
            }
            else {
                Write-ColorOutput "⚠️ WARNING: Some dependencies may have failed to install" "Yellow"
            }
        }
        catch {
            Write-ColorOutput "⚠️ WARNING: Failed to install dependencies" "Yellow"
        }
    }
    else {
        Write-ColorOutput "⚠️ WARNING: requirements.txt not found" "Yellow"
    }
}

function Initialize-Directories {
    Write-ColorOutput "📁 Creating necessary directories..." "Blue"
    
    $directories = @(
        "logs", "logs\system", "logs\trading", "logs\ai_systems",
        "logs\agents", "logs\performance", "logs\errors",
        "models", "data", "backups"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-ColorOutput "✅ Directories created" "Green"
}

function Start-SystemValidation {
    Write-Host ""
    Write-ColorOutput "================================================================================" "Cyan"
    Write-ColorOutput "🔍 RUNNING COMPREHENSIVE SYSTEM VALIDATION" "White"
    Write-ColorOutput "================================================================================" "Cyan"
    Write-Host ""
    
    try {
        & python launch_unified_system.py
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-ColorOutput "================================================================================" "Cyan"
            Write-ColorOutput "🎉 UNIFIED SYSTEM LAUNCH SEQUENCE COMPLETED" "Green"
            Write-ColorOutput "================================================================================" "Cyan"
            return $true
        }
        else {
            Write-ColorOutput "❌ System validation failed or launch was cancelled" "Red"
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ Error during system validation: $_" "Red"
        return $false
    }
}

function Show-SystemInfo {
    Write-Host ""
    Write-ColorOutput "📊 SYSTEM INFORMATION:" "Cyan"
    Write-ColorOutput "  💻 OS: $($env:OS)" "White"
    Write-ColorOutput "  🖥️ Computer: $($env:COMPUTERNAME)" "White"
    Write-ColorOutput "  👤 User: $($env:USERNAME)" "White"
    Write-ColorOutput "  📁 Location: $(Get-Location)" "White"
    Write-ColorOutput "  🕒 Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "White"
    
    if ($Development) {
        Write-ColorOutput "  🔧 Mode: Development" "Yellow"
    }
    elseif ($Production) {
        Write-ColorOutput "  🚀 Mode: Production" "Green"
    }
    else {
        Write-ColorOutput "  ⚙️ Mode: Standard" "Blue"
    }
    
    Write-Host ""
}

# Main execution
try {
    Write-Header
    Show-SystemInfo
    
    # Check prerequisites
    if (-not (Test-Prerequisites)) {
        exit 1
    }
    
    # Initialize environment
    if (-not (Initialize-Environment -EnvName $Environment)) {
        exit 1
    }
    
    # Test Python setup
    if (-not (Test-PythonSetup)) {
        exit 1
    }
    
    # Install dependencies (unless skipped)
    if (-not $SkipValidation) {
        Install-Dependencies
    }
    
    # Initialize directories
    Initialize-Directories
    
    # Start system validation and launch
    if (-not (Start-SystemValidation)) {
        Write-ColorOutput "💡 Please check the error messages above and fix any issues" "Yellow"
        exit 1
    }
    
    Write-ColorOutput "✅ System launched successfully!" "Green"
}
catch {
    Write-ColorOutput "❌ Unexpected error: $_" "Red"
    exit 1
}
finally {
    Write-Host ""
    Write-ColorOutput "Press any key to exit..." "Gray"
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
