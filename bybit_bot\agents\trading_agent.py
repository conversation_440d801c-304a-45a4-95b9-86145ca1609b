"""
Trading Agent - Specialized agent for trade execution and order management
Handles pure trading operations with focus on execution efficiency and order management
"""
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager
from ..exchange.bybit_client import BybitClient
from ..risk.advanced_risk_manager import AdvancedRiskManager


class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    PARTIAL = "partial"


class TradeDirection(Enum):
    """Trade direction enumeration"""
    LONG = "long"
    SHORT = "short"


@dataclass
class TradeSignal:
    """Trade signal structure"""
    symbol: str
    direction: TradeDirection
    confidence: float
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: float
    reasoning: str
    timestamp: datetime
    metadata: Dict[str, Any] = None


@dataclass
class OrderExecution:
    """Order execution details"""
    order_id: str
    symbol: str
    side: str
    quantity: float
    price: float
    status: OrderStatus
    filled_quantity: float
    average_price: float
    commission: float
    timestamp: datetime
    execution_time: float


class TradingAgent:
    """
    Specialized trading agent for order execution and management
    
    Capabilities:
    - Trade signal processing
    - Order execution and management
    - Position monitoring
    - Risk-adjusted position sizing
    - Execution optimization
    - Slippage minimization
    - Order book analysis
    - Market impact assessment
    """
    
    def __init__(self, agent_id: str, config: BotConfig, database_manager: DatabaseManager, orchestrator):
        self.agent_id = agent_id
        self.config = config
        self.db_manager = database_manager
        self.orchestrator = orchestrator
        self.logger = TradingBotLogger(f"TradingAgent_{agent_id}")
        
        # Trading components
        self.bybit_client = None
        self.risk_manager = None
        
        # Trading state
        self.active_orders: Dict[str, OrderExecution] = {}
        self.active_positions: Dict[str, Dict[str, Any]] = {}
        self.pending_signals: List[TradeSignal] = []
        self.execution_history: List[OrderExecution] = []
        
        # Performance metrics
        self.metrics = {
            'total_trades': 0,
            'successful_trades': 0,
            'total_profit': 0.0,
            'win_rate': 0.0,
            'average_execution_time': 0.0,
            'slippage_average': 0.0,
            'commission_paid': 0.0
        }
        
        # Control flags
        self.is_running = False
        self.emergency_stop = False
        
        # Task handlers
        self.task_handlers = {
            'execute_trade': self._execute_trade_task,
            'cancel_order': self._cancel_order_task,
            'modify_order': self._modify_order_task,
            'close_position': self._close_position_task,
            'get_positions': self._get_positions_task,
            'get_orders': self._get_orders_task,
            'calculate_position_size': self._calculate_position_size_task,
            'market_analysis': self._market_analysis_task
        }
    
    async def initialize(self):
        """Initialize the trading agent"""
        try:
            self.logger.info(f"Initializing Trading Agent {self.agent_id}")
            
            # Initialize Bybit client
            self.bybit_client = BybitClient(self.config)
            await self.bybit_client.initialize()
            
            # Initialize risk manager
            self.risk_manager = AdvancedRiskManager(
                self.config, 
                self.db_manager, 
                self.bybit_client
            )
            await self.risk_manager.initialize()
            
            # Start monitoring loops
            self.is_running = True
            asyncio.create_task(self._order_monitoring_loop())
            asyncio.create_task(self._position_monitoring_loop())
            asyncio.create_task(self._signal_processing_loop())
            
            self.logger.info(f"Trading Agent {self.agent_id} initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Trading Agent: {e}")
            raise
    
    async def assign_task(self, task):
        """Assign a task to this agent"""
        try:
            task_type = task.task_type
            handler = self.task_handlers.get(task_type)
            
            if handler:
                result = await handler(task.data)
                
                # Send result back to orchestrator
                await self.orchestrator.send_message(
                    sender=self.agent_id,
                    recipient='orchestrator',
                    message_type='task_response',
                    data={
                        'task_id': task.task_id,
                        'result': result,
                        'status': 'completed'
                    }
                )
                
                self.logger.info(f"Task {task.task_id} completed successfully")
            else:
                self.logger.error(f"Unknown task type: {task_type}")
                
        except Exception as e:
            self.logger.error(f"Error executing task {task.task_id}: {e}")
            
            # Send error response
            await self.orchestrator.send_message(
                sender=self.agent_id,
                recipient='orchestrator',
                message_type='task_response',
                data={
                    'task_id': task.task_id,
                    'error': str(e),
                    'status': 'failed'
                }
            )
    
    async def _execute_trade_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a trade based on signal"""
        try:
            # Parse trade signal
            signal = TradeSignal(
                symbol=data['symbol'],
                direction=TradeDirection(data['direction']),
                confidence=data['confidence'],
                entry_price=data['entry_price'],
                stop_loss=data['stop_loss'],
                take_profit=data['take_profit'],
                position_size=data['position_size'],
                reasoning=data['reasoning'],
                timestamp=datetime.now(),
                metadata=data.get('metadata', {})
            )
            
            # Risk assessment
            risk_check = await self.risk_manager.assess_trade_risk(signal)
            if not risk_check['approved']:
                return {
                    'status': 'rejected',
                    'reason': risk_check['reason']
                }
            
            # Calculate optimal position size
            position_size = await self._calculate_optimal_position_size(signal)
            
            # Execute the trade
            execution = await self._execute_order(signal, position_size)
            
            # Update metrics
            self._update_execution_metrics(execution)
            
            return {
                'status': 'success',
                'execution': execution.__dict__,
                'order_id': execution.order_id
            }
            
        except Exception as e:
            self.logger.error(f"Trade execution failed: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _execute_order(self, signal: TradeSignal, position_size: float) -> OrderExecution:
        """Execute an order on the exchange"""
        start_time = time.time()
        
        try:
            # Determine order parameters
            side = "Buy" if signal.direction == TradeDirection.LONG else "Sell"
            
            # Place order
            order_result = await self.bybit_client.place_order(
                symbol=signal.symbol,
                side=side,
                quantity=position_size,
                price=signal.entry_price,
                order_type="Limit",
                time_in_force="GTC"
            )
            
            execution_time = time.time() - start_time
            
            # Create execution record
            execution = OrderExecution(
                order_id=order_result['orderId'],
                symbol=signal.symbol,
                side=side,
                quantity=position_size,
                price=signal.entry_price,
                status=OrderStatus.PENDING,
                filled_quantity=0.0,
                average_price=0.0,
                commission=0.0,
                timestamp=datetime.now(),
                execution_time=execution_time
            )
            
            # Store active order
            self.active_orders[execution.order_id] = execution
            
            # Set stop loss and take profit
            if signal.stop_loss:
                await self._set_stop_loss(signal.symbol, signal.stop_loss, position_size)
            if signal.take_profit:
                await self._set_take_profit(signal.symbol, signal.take_profit, position_size)
            
            self.logger.info(f"Order executed: {execution.order_id} for {signal.symbol}")
            
            return execution
            
        except Exception as e:
            self.logger.error(f"Order execution failed: {e}")
            raise
    
    async def _calculate_optimal_position_size(self, signal: TradeSignal) -> float:
        """Calculate optimal position size based on risk management"""
        try:
            # Get account balance
            balance = await self.bybit_client.get_wallet_balance()
            
            # Calculate position size based on risk
            risk_amount = balance * self.config.trading.max_risk_per_trade
            
            # Calculate position size based on stop loss distance
            stop_distance = abs(signal.entry_price - signal.stop_loss) / signal.entry_price
            position_size = risk_amount / (stop_distance * signal.entry_price)
            
            # Apply maximum position size limit
            max_position_value = balance * self.config.trading.max_position_size
            max_position_size = max_position_value / signal.entry_price
            
            position_size = min(position_size, max_position_size)
            
            # Apply confidence adjustment
            position_size *= signal.confidence
            
            return round(position_size, 6)
            
        except Exception as e:
            self.logger.error(f"Position size calculation failed: {e}")
            return 0.0
    
    async def _set_stop_loss(self, symbol: str, stop_price: float, quantity: float):
        """Set stop loss order"""
        try:
            await self.bybit_client.place_order(
                symbol=symbol,
                side="Sell",  # Opposite to entry
                quantity=quantity,
                price=stop_price,
                order_type="StopMarket",
                time_in_force="GTC"
            )
            
            self.logger.info(f"Stop loss set for {symbol} at {stop_price}")
            
        except Exception as e:
            self.logger.error(f"Failed to set stop loss: {e}")
    
    async def _set_take_profit(self, symbol: str, take_profit_price: float, quantity: float):
        """Set take profit order"""
        try:
            await self.bybit_client.place_order(
                symbol=symbol,
                side="Sell",  # Opposite to entry
                quantity=quantity,
                price=take_profit_price,
                order_type="Limit",
                time_in_force="GTC"
            )
            
            self.logger.info(f"Take profit set for {symbol} at {take_profit_price}")
            
        except Exception as e:
            self.logger.error(f"Failed to set take profit: {e}")
    
    async def _cancel_order_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Cancel an order"""
        try:
            order_id = data['order_id']
            symbol = data['symbol']
            
            await self.bybit_client.cancel_order(symbol=symbol, order_id=order_id)
            
            # Update order status
            if order_id in self.active_orders:
                self.active_orders[order_id].status = OrderStatus.CANCELLED
            
            return {'status': 'success', 'order_id': order_id}
            
        except Exception as e:
            self.logger.error(f"Order cancellation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _modify_order_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Modify an existing order"""
        try:
            order_id = data['order_id']
            symbol = data['symbol']
            new_price = data.get('new_price')
            new_quantity = data.get('new_quantity')
            
            await self.bybit_client.modify_order(
                symbol=symbol,
                order_id=order_id,
                price=new_price,
                quantity=new_quantity
            )
            
            return {'status': 'success', 'order_id': order_id}
            
        except Exception as e:
            self.logger.error(f"Order modification failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _close_position_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Close a position"""
        try:
            symbol = data['symbol']
            
            # Get current position
            position = await self.bybit_client.get_position(symbol)
            
            if position and position['size'] > 0:
                # Close position
                await self.bybit_client.place_order(
                    symbol=symbol,
                    side="Sell" if position['side'] == "Buy" else "Buy",
                    quantity=position['size'],
                    order_type="Market"
                )
                
                return {'status': 'success', 'symbol': symbol}
            else:
                return {'status': 'no_position', 'symbol': symbol}
                
        except Exception as e:
            self.logger.error(f"Position closure failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _get_positions_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get current positions"""
        try:
            positions = await self.bybit_client.get_positions()
            return {'status': 'success', 'positions': positions}
            
        except Exception as e:
            self.logger.error(f"Failed to get positions: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _get_orders_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get current orders"""
        try:
            orders = await self.bybit_client.get_orders()
            return {'status': 'success', 'orders': orders}
            
        except Exception as e:
            self.logger.error(f"Failed to get orders: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _calculate_position_size_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate position size for a trade"""
        try:
            signal = TradeSignal(
                symbol=data['symbol'],
                direction=TradeDirection(data['direction']),
                confidence=data['confidence'],
                entry_price=data['entry_price'],
                stop_loss=data['stop_loss'],
                take_profit=data['take_profit'],
                position_size=0.0,
                reasoning=data['reasoning'],
                timestamp=datetime.now()
            )
            
            position_size = await self._calculate_optimal_position_size(signal)
            
            return {
                'status': 'success',
                'position_size': position_size,
                'symbol': signal.symbol
            }
            
        except Exception as e:
            self.logger.error(f"Position size calculation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _market_analysis_task(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform market analysis for trading"""
        try:
            symbol = data['symbol']
            
            # Get order book
            order_book = await self.bybit_client.get_order_book(symbol)
            
            # Calculate market impact
            market_impact = await self._calculate_market_impact(symbol, order_book)
            
            # Get recent trades
            recent_trades = await self.bybit_client.get_recent_trades(symbol)
            
            return {
                'status': 'success',
                'symbol': symbol,
                'order_book': order_book,
                'market_impact': market_impact,
                'recent_trades': recent_trades
            }
            
        except Exception as e:
            self.logger.error(f"Market analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _calculate_market_impact(self, symbol: str, order_book: Dict[str, Any]) -> Dict[str, float]:
        """Calculate market impact for different order sizes"""
        try:
            bids = order_book['bids']
            asks = order_book['asks']
            
            # Calculate impact for different order sizes
            impact_analysis = {}
            
            for size in [1000, 5000, 10000, 50000]:  # USD amounts
                impact_analysis[f'impact_{size}'] = {
                    'buy_impact': self._calculate_price_impact(asks, size),
                    'sell_impact': self._calculate_price_impact(bids, size)
                }
            
            return impact_analysis
            
        except Exception as e:
            self.logger.error(f"Market impact calculation failed: {e}")
            return {}
    
    def _calculate_price_impact(self, orders: List[List[float]], size: float) -> float:
        """Calculate price impact for a given order size"""
        try:
            cumulative_size = 0
            weighted_price = 0
            
            for price, quantity in orders:
                if cumulative_size >= size:
                    break
                
                remaining_size = min(quantity, size - cumulative_size)
                weighted_price += price * remaining_size
                cumulative_size += remaining_size
            
            if cumulative_size > 0:
                average_price = weighted_price / cumulative_size
                best_price = orders[0][0]
                return abs(average_price - best_price) / best_price
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"Price impact calculation failed: {e}")
            return 0.0
    
    async def _order_monitoring_loop(self):
        """Monitor active orders"""
        while self.is_running:
            try:
                for order_id, execution in list(self.active_orders.items()):
                    # Check order status
                    order_status = await self.bybit_client.get_order_status(
                        execution.symbol, order_id
                    )
                    
                    if order_status:
                        # Update execution
                        execution.status = OrderStatus(order_status['status'])
                        execution.filled_quantity = order_status['filled_quantity']
                        execution.average_price = order_status['average_price']
                        execution.commission = order_status['commission']
                        
                        # Remove completed orders
                        if execution.status in [OrderStatus.FILLED, OrderStatus.CANCELLED]:
                            self.execution_history.append(execution)
                            del self.active_orders[order_id]
                            
                            # Notify orchestrator
                            await self.orchestrator.send_message(
                                sender=self.agent_id,
                                recipient='orchestrator',
                                message_type='data_share',
                                data={
                                    'key': f'order_completed_{order_id}',
                                    'value': execution.__dict__
                                }
                            )
                
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Error in order monitoring: {e}")
                await asyncio.sleep(10)
    
    async def _position_monitoring_loop(self):
        """Monitor active positions"""
        while self.is_running:
            try:
                positions = await self.bybit_client.get_positions()
                
                for position in positions:
                    symbol = position['symbol']
                    self.active_positions[symbol] = position
                    
                    # Check for risk limits
                    if self.risk_manager:
                        risk_check = await self.risk_manager.check_position_risk(position)
                        if risk_check['action'] == 'close':
                            await self._emergency_close_position(symbol)
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Error in position monitoring: {e}")
                await asyncio.sleep(30)
    
    async def _signal_processing_loop(self):
        """Process pending trade signals"""
        while self.is_running:
            try:
                if self.pending_signals:
                    signal = self.pending_signals.pop(0)
                    
                    # Process signal
                    await self._process_trade_signal(signal)
                
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error in signal processing: {e}")
                await asyncio.sleep(5)
    
    async def _process_trade_signal(self, signal: TradeSignal):
        """Process a trade signal"""
        try:
            # Execute trade
            execution = await self._execute_order(signal, signal.position_size)
            
            # Update metrics
            self._update_execution_metrics(execution)
            
            self.logger.info(f"Processed signal for {signal.symbol}: {execution.order_id}")
            
        except Exception as e:
            self.logger.error(f"Signal processing failed: {e}")
    
    async def _emergency_close_position(self, symbol: str):
        """Emergency position closure"""
        try:
            position = self.active_positions.get(symbol)
            if position and position['size'] > 0:
                await self.bybit_client.place_order(
                    symbol=symbol,
                    side="Sell" if position['side'] == "Buy" else "Buy",
                    quantity=position['size'],
                    order_type="Market"
                )
                
                self.logger.warning(f"Emergency closure for {symbol}")
                
                # Notify orchestrator
                await self.orchestrator.send_message(
                    sender=self.agent_id,
                    recipient='orchestrator',
                    message_type='alert',
                    data={
                        'type': 'emergency_closure',
                        'severity': 'high',
                        'details': f'Emergency closure executed for {symbol}'
                    }
                )
                
        except Exception as e:
            self.logger.error(f"Emergency closure failed: {e}")
    
    def _update_execution_metrics(self, execution: OrderExecution):
        """Update execution performance metrics"""
        self.metrics['total_trades'] += 1
        self.metrics['average_execution_time'] = (
            (self.metrics['average_execution_time'] * (self.metrics['total_trades'] - 1) + 
             execution.execution_time) / self.metrics['total_trades']
        )
        
        if execution.status == OrderStatus.FILLED:
            self.metrics['successful_trades'] += 1
            self.metrics['commission_paid'] += execution.commission
    
    def get_capabilities(self) -> List[str]:
        """Get agent capabilities"""
        return [
            'execute_trade',
            'cancel_order',
            'modify_order',
            'close_position',
            'get_positions',
            'get_orders',
            'calculate_position_size',
            'market_analysis'
        ]
    
    async def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            'agent_id': self.agent_id,
            'status': 'active' if self.is_running else 'inactive',
            'active_orders': len(self.active_orders),
            'active_positions': len(self.active_positions),
            'pending_signals': len(self.pending_signals),
            'emergency_stop': self.emergency_stop
        }
    
    async def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics"""
        if self.metrics['total_trades'] > 0:
            self.metrics['win_rate'] = (
                self.metrics['successful_trades'] / self.metrics['total_trades']
            )
        
        return self.metrics.copy()
    
    async def shutdown(self):
        """Shutdown the trading agent"""
        self.logger.info(f"Shutting down Trading Agent {self.agent_id}")
        
        self.is_running = False
        
        # Cancel all active orders
        for order_id, execution in self.active_orders.items():
            try:
                await self.bybit_client.cancel_order(execution.symbol, order_id)
            except Exception as e:
                self.logger.error(f"Error cancelling order {order_id}: {e}")
        
        # Cleanup
        if self.bybit_client:
            await self.bybit_client.close()
        
        self.logger.info(f"Trading Agent {self.agent_id} shutdown complete")
