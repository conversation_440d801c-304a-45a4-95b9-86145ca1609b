"""
Database Setup and Initialization Script for Super-GPT Trading Bot
Creates all required tables, indexes, and initial data
"""
import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path
import logging

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger
from bybit_bot.database.connection import DatabaseManager


class DatabaseInitializer:
    """Database initialization and setup"""
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingBotLogger("DatabaseInitializer")
        self.db_manager = DatabaseManager(config)
        
        # Get database configuration
        self.db_config = {
            'host': config.database.get('host', 'localhost'),
            'port': config.database.get('port', 5432),
            'database': config.database.get('database', 'trading_bot'),
            'user': config.database.get('user', 'postgres'),
            'password': config.database.get('password', 'password')
        }
    
    async def initialize_database(self):
        """Initialize the complete database"""
        try:
            self.logger.info("Starting database initialization")
            
            # Create database if it doesn't exist
            await self._create_database_if_not_exists()
            
            # Create tables
            await self._create_tables()
            
            # Create indexes
            await self._create_indexes()
            
            # Create views
            await self._create_views()
            
            # Create functions and procedures
            await self._create_functions()
            
            # Create triggers
            await self._create_triggers()
            
            # Insert initial data
            await self._insert_initial_data()
            
            # Verify setup
            await self._verify_database_setup()
            
            self.logger.info("Database initialization completed successfully")
            
        except Exception as e:
            self.logger.error(f"Database initialization failed: {e}")
            raise
    
    async def _create_database_if_not_exists(self):
        """Create database if it doesn't exist"""
        try:
            # Connect to default database
            conn = psycopg2.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                database='postgres',
                user=self.db_config['user'],
                password=self.db_config['password']
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            
            cur = conn.cursor()
            
            # Check if database exists
            cur.execute("""
                SELECT 1 FROM pg_catalog.pg_database 
                WHERE datname = %s
            """, (self.db_config['database'],))
            
            if not cur.fetchone():
                # Create database
                cur.execute(f"""
                    CREATE DATABASE {self.db_config['database']}
                    WITH ENCODING 'UTF8'
                """)
                self.logger.info(f"Created database: {self.db_config['database']}")
            
            cur.close()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error creating database: {e}")
            raise
    
    async def _create_tables(self):
        """Create all tables"""
        try:
            self.logger.info("Creating database tables")
            
            # Core tables
            await self._create_core_tables()
            
            # Trading tables
            await self._create_trading_tables()
            
            # AI and ML tables
            await self._create_ai_tables()
            
            # Risk management tables
            await self._create_risk_tables()
            
            # Analytics tables
            await self._create_analytics_tables()
            
            # System tables
            await self._create_system_tables()
            
        except Exception as e:
            self.logger.error(f"Error creating tables: {e}")
            raise
    
    async def _create_core_tables(self):
        """Create core system tables"""
        async with self.db_manager.get_connection() as conn:
            
            # Symbols table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS symbols (
                    symbol VARCHAR(20) PRIMARY KEY,
                    name VARCHAR(100),
                    base_currency VARCHAR(10),
                    quote_currency VARCHAR(10),
                    status VARCHAR(20) DEFAULT 'active',
                    price_precision INTEGER DEFAULT 8,
                    quantity_precision INTEGER DEFAULT 8,
                    min_order_size DECIMAL(20,8),
                    max_order_size DECIMAL(20,8),
                    tick_size DECIMAL(20,8),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Market data table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS market_data (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) REFERENCES symbols(symbol),
                    timestamp TIMESTAMP,
                    open_price DECIMAL(20,8),
                    high_price DECIMAL(20,8),
                    low_price DECIMAL(20,8),
                    close_price DECIMAL(20,8),
                    volume DECIMAL(20,8),
                    turnover DECIMAL(20,8),
                    interval VARCHAR(10),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, timestamp, interval)
                )
            """)
            
            # Order book table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS order_book (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) REFERENCES symbols(symbol),
                    timestamp TIMESTAMP,
                    side VARCHAR(4) CHECK (side IN ('bid', 'ask')),
                    price DECIMAL(20,8),
                    quantity DECIMAL(20,8),
                    count INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Trades table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS trades (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) REFERENCES symbols(symbol),
                    timestamp TIMESTAMP,
                    price DECIMAL(20,8),
                    quantity DECIMAL(20,8),
                    side VARCHAR(4) CHECK (side IN ('buy', 'sell')),
                    trade_id VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(symbol, trade_id)
                )
            """)
    
    async def _create_trading_tables(self):
        """Create trading-related tables"""
        async with self.db_manager.get_connection() as conn:
            
            # Positions table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS positions (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) REFERENCES symbols(symbol),
                    side VARCHAR(4) CHECK (side IN ('long', 'short')),
                    size DECIMAL(20,8),
                    entry_price DECIMAL(20,8),
                    current_price DECIMAL(20,8),
                    unrealized_pnl DECIMAL(20,8),
                    realized_pnl DECIMAL(20,8),
                    margin DECIMAL(20,8),
                    leverage INTEGER,
                    stop_loss DECIMAL(20,8),
                    take_profit DECIMAL(20,8),
                    status VARCHAR(20) DEFAULT 'open',
                    opened_at TIMESTAMP,
                    closed_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Orders table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS orders (
                    id SERIAL PRIMARY KEY,
                    order_id VARCHAR(50) UNIQUE,
                    symbol VARCHAR(20) REFERENCES symbols(symbol),
                    side VARCHAR(4) CHECK (side IN ('buy', 'sell')),
                    type VARCHAR(20) CHECK (type IN ('market', 'limit', 'stop', 'stop_limit')),
                    quantity DECIMAL(20,8),
                    price DECIMAL(20,8),
                    filled_quantity DECIMAL(20,8) DEFAULT 0,
                    average_price DECIMAL(20,8),
                    status VARCHAR(20) DEFAULT 'pending',
                    time_in_force VARCHAR(10) DEFAULT 'GTC',
                    reduce_only BOOLEAN DEFAULT false,
                    close_on_trigger BOOLEAN DEFAULT false,
                    stop_price DECIMAL(20,8),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    filled_at TIMESTAMP,
                    cancelled_at TIMESTAMP
                )
            """)
            
            # Executions table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS executions (
                    id SERIAL PRIMARY KEY,
                    execution_id VARCHAR(50) UNIQUE,
                    order_id VARCHAR(50) REFERENCES orders(order_id),
                    symbol VARCHAR(20) REFERENCES symbols(symbol),
                    side VARCHAR(4) CHECK (side IN ('buy', 'sell')),
                    quantity DECIMAL(20,8),
                    price DECIMAL(20,8),
                    fee DECIMAL(20,8),
                    fee_currency VARCHAR(10),
                    is_maker BOOLEAN,
                    executed_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Strategies table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS strategies (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) UNIQUE,
                    description TEXT,
                    type VARCHAR(50),
                    parameters JSONB,
                    status VARCHAR(20) DEFAULT 'inactive',
                    performance JSONB,
                    risk_metrics JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Strategy executions table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_executions (
                    id SERIAL PRIMARY KEY,
                    strategy_id INTEGER REFERENCES strategies(id),
                    symbol VARCHAR(20) REFERENCES symbols(symbol),
                    action VARCHAR(20),
                    parameters JSONB,
                    result JSONB,
                    success BOOLEAN,
                    error_message TEXT,
                    execution_time INTERVAL,
                    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
    
    async def _create_ai_tables(self):
        """Create AI and ML related tables"""
        async with self.db_manager.get_connection() as conn:
            
            # AI models table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS ai_models (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) UNIQUE,
                    type VARCHAR(50),
                    version VARCHAR(20),
                    parameters JSONB,
                    performance_metrics JSONB,
                    training_data_info JSONB,
                    status VARCHAR(20) DEFAULT 'inactive',
                    file_path VARCHAR(500),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_trained_at TIMESTAMP
                )
            """)
            
            # Model predictions table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS model_predictions (
                    id SERIAL PRIMARY KEY,
                    model_id INTEGER REFERENCES ai_models(id),
                    symbol VARCHAR(20) REFERENCES symbols(symbol),
                    prediction_type VARCHAR(50),
                    prediction_value DECIMAL(20,8),
                    confidence DECIMAL(5,4),
                    features JSONB,
                    actual_value DECIMAL(20,8),
                    accuracy DECIMAL(5,4),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Memory store table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS memory_store (
                    id SERIAL PRIMARY KEY,
                    key VARCHAR(255) UNIQUE,
                    value JSONB,
                    type VARCHAR(50),
                    importance DECIMAL(3,2) DEFAULT 0.5,
                    access_count INTEGER DEFAULT 0,
                    last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP
                )
            """)
            
            # Learning patterns table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS learning_patterns (
                    id SERIAL PRIMARY KEY,
                    pattern_type VARCHAR(50),
                    pattern_data JSONB,
                    frequency INTEGER DEFAULT 1,
                    success_rate DECIMAL(5,4),
                    confidence DECIMAL(5,4),
                    last_seen_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Agent actions table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS agent_actions (
                    id SERIAL PRIMARY KEY,
                    agent_name VARCHAR(100),
                    action_type VARCHAR(50),
                    action_data JSONB,
                    context JSONB,
                    result JSONB,
                    success BOOLEAN,
                    execution_time INTERVAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
    
    async def _create_risk_tables(self):
        """Create risk management tables"""
        async with self.db_manager.get_connection() as conn:
            
            # Risk metrics table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS risk_metrics (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) REFERENCES symbols(symbol),
                    timestamp TIMESTAMP,
                    var_1d DECIMAL(20,8),
                    var_7d DECIMAL(20,8),
                    var_30d DECIMAL(20,8),
                    expected_shortfall DECIMAL(20,8),
                    beta DECIMAL(10,6),
                    sharpe_ratio DECIMAL(10,6),
                    max_drawdown DECIMAL(10,6),
                    volatility DECIMAL(10,6),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Risk limits table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS risk_limits (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) REFERENCES symbols(symbol),
                    limit_type VARCHAR(50),
                    limit_value DECIMAL(20,8),
                    current_value DECIMAL(20,8),
                    utilization DECIMAL(5,4),
                    breach_count INTEGER DEFAULT 0,
                    last_breach_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Risk events table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS risk_events (
                    id SERIAL PRIMARY KEY,
                    event_type VARCHAR(50),
                    severity VARCHAR(20),
                    symbol VARCHAR(20),
                    description TEXT,
                    impact JSONB,
                    mitigation_actions JSONB,
                    status VARCHAR(20) DEFAULT 'open',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    resolved_at TIMESTAMP
                )
            """)
    
    async def _create_analytics_tables(self):
        """Create analytics tables"""
        async with self.db_manager.get_connection() as conn:
            
            # Performance metrics table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id SERIAL PRIMARY KEY,
                    metric_type VARCHAR(50),
                    symbol VARCHAR(20),
                    period VARCHAR(20),
                    value DECIMAL(20,8),
                    benchmark_value DECIMAL(20,8),
                    rank INTEGER,
                    percentile DECIMAL(5,2),
                    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Market analysis table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS market_analysis (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) REFERENCES symbols(symbol),
                    analysis_type VARCHAR(50),
                    timeframe VARCHAR(20),
                    indicators JSONB,
                    signals JSONB,
                    confidence DECIMAL(5,4),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # News and sentiment table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS news_sentiment (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20),
                    headline TEXT,
                    content TEXT,
                    source VARCHAR(100),
                    sentiment_score DECIMAL(5,4),
                    impact_score DECIMAL(5,4),
                    relevance_score DECIMAL(5,4),
                    published_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
    
    async def _create_system_tables(self):
        """Create system tables"""
        async with self.db_manager.get_connection() as conn:
            
            # System logs table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS system_logs (
                    id SERIAL PRIMARY KEY,
                    level VARCHAR(20),
                    logger VARCHAR(100),
                    message TEXT,
                    module VARCHAR(100),
                    function VARCHAR(100),
                    line_number INTEGER,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # System metrics table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS system_metrics (
                    id SERIAL PRIMARY KEY,
                    metric_name VARCHAR(100),
                    metric_value DECIMAL(20,8),
                    unit VARCHAR(20),
                    tags JSONB,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Configuration table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS configuration (
                    id SERIAL PRIMARY KEY,
                    key VARCHAR(255) UNIQUE,
                    value JSONB,
                    type VARCHAR(50),
                    description TEXT,
                    is_sensitive BOOLEAN DEFAULT false,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Backups table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS backups (
                    id SERIAL PRIMARY KEY,
                    backup_type VARCHAR(50),
                    file_path VARCHAR(500),
                    size_bytes BIGINT,
                    checksum VARCHAR(64),
                    status VARCHAR(20),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP
                )
            """)
    
    async def _create_indexes(self):
        """Create database indexes"""
        async with self.db_manager.get_connection() as conn:
            
            # Market data indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_market_data_symbol_timestamp ON market_data(symbol, timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_market_data_symbol_interval ON market_data(symbol, interval)")
            
            # Order book indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_order_book_symbol_timestamp ON order_book(symbol, timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_order_book_timestamp ON order_book(timestamp)")
            
            # Trades indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_trades_symbol_timestamp ON trades(symbol, timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_trades_trade_id ON trades(trade_id)")
            
            # Positions indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(symbol)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_positions_status ON positions(status)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_positions_opened_at ON positions(opened_at)")
            
            # Orders indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_order_id ON orders(order_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_symbol ON orders(symbol)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)")
            
            # Executions indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_executions_order_id ON executions(order_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_executions_symbol ON executions(symbol)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_executions_executed_at ON executions(executed_at)")
            
            # AI models indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_ai_models_name ON ai_models(name)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_ai_models_type ON ai_models(type)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_ai_models_status ON ai_models(status)")
            
            # Model predictions indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_model_predictions_model_id ON model_predictions(model_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_model_predictions_symbol ON model_predictions(symbol)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_model_predictions_created_at ON model_predictions(created_at)")
            
            # Memory store indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_store_key ON memory_store(key)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_store_type ON memory_store(type)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_store_importance ON memory_store(importance)")
            
            # Performance indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_performance_metrics_type ON performance_metrics(metric_type)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_performance_metrics_symbol ON performance_metrics(symbol)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_performance_metrics_calculated_at ON performance_metrics(calculated_at)")
            
            # System logs indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_system_logs_logger ON system_logs(logger)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp)")
    
    async def _create_views(self):
        """Create database views"""
        async with self.db_manager.get_connection() as conn:
            
            # Active positions view
            await conn.execute("""
                CREATE OR REPLACE VIEW active_positions AS
                SELECT * FROM positions WHERE status = 'open'
            """)
            
            # Open orders view
            await conn.execute("""
                CREATE OR REPLACE VIEW open_orders AS
                SELECT * FROM orders WHERE status IN ('pending', 'partially_filled')
            """)
            
            # Recent trades view
            await conn.execute("""
                CREATE OR REPLACE VIEW recent_trades AS
                SELECT * FROM trades 
                WHERE timestamp >= NOW() - INTERVAL '24 hours'
                ORDER BY timestamp DESC
            """)
            
            # Performance summary view
            await conn.execute("""
                CREATE OR REPLACE VIEW performance_summary AS
                SELECT 
                    symbol,
                    COUNT(*) as trade_count,
                    SUM(CASE WHEN realized_pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                    SUM(CASE WHEN realized_pnl <= 0 THEN 1 ELSE 0 END) as losing_trades,
                    SUM(realized_pnl) as total_pnl,
                    AVG(realized_pnl) as avg_pnl,
                    MAX(realized_pnl) as max_win,
                    MIN(realized_pnl) as max_loss
                FROM positions 
                WHERE status = 'closed' AND closed_at >= NOW() - INTERVAL '30 days'
                GROUP BY symbol
            """)
    
    async def _create_functions(self):
        """Create database functions"""
        async with self.db_manager.get_connection() as conn:
            
            # Function to calculate position PnL
            await conn.execute("""
                CREATE OR REPLACE FUNCTION calculate_position_pnl(
                    p_entry_price DECIMAL(20,8),
                    p_current_price DECIMAL(20,8),
                    p_size DECIMAL(20,8),
                    p_side VARCHAR(4)
                ) RETURNS DECIMAL(20,8) AS $$
                BEGIN
                    IF p_side = 'long' THEN
                        RETURN (p_current_price - p_entry_price) * p_size;
                    ELSE
                        RETURN (p_entry_price - p_current_price) * p_size;
                    END IF;
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            # Function to update position PnL
            await conn.execute("""
                CREATE OR REPLACE FUNCTION update_position_pnl() RETURNS TRIGGER AS $$
                BEGIN
                    NEW.unrealized_pnl := calculate_position_pnl(
                        NEW.entry_price, 
                        NEW.current_price, 
                        NEW.size, 
                        NEW.side
                    );
                    NEW.updated_at := CURRENT_TIMESTAMP;
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            # Function to calculate volatility
            await conn.execute("""
                CREATE OR REPLACE FUNCTION calculate_volatility(
                    p_symbol VARCHAR(20),
                    p_period INTEGER DEFAULT 20
                ) RETURNS DECIMAL(10,6) AS $$
                DECLARE
                    volatility DECIMAL(10,6);
                BEGIN
                    SELECT STDDEV(LOG(close_price / LAG(close_price) OVER (ORDER BY timestamp)))
                    INTO volatility
                    FROM (
                        SELECT close_price, timestamp
                        FROM market_data
                        WHERE symbol = p_symbol
                        ORDER BY timestamp DESC
                        LIMIT p_period
                    ) price_data;
                    
                    RETURN COALESCE(volatility, 0);
                END;
                $$ LANGUAGE plpgsql;
            """)
    
    async def _create_triggers(self):
        """Create database triggers"""
        async with self.db_manager.get_connection() as conn:
            
            # Trigger to update position PnL
            await conn.execute("""
                CREATE TRIGGER trigger_update_position_pnl
                BEFORE UPDATE ON positions
                FOR EACH ROW
                EXECUTE FUNCTION update_position_pnl();
            """)
            
            # Trigger to update timestamps
            await conn.execute("""
                CREATE OR REPLACE FUNCTION update_timestamp() RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at := CURRENT_TIMESTAMP;
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            # Apply timestamp trigger to relevant tables
            for table in ['positions', 'orders', 'strategies', 'ai_models', 'configuration']:
                await conn.execute(f"""
                    CREATE TRIGGER trigger_update_{table}_timestamp
                    BEFORE UPDATE ON {table}
                    FOR EACH ROW
                    EXECUTE FUNCTION update_timestamp();
                """)
    
    async def _insert_initial_data(self):
        """Insert initial data"""
        async with self.db_manager.get_connection() as conn:
            
            # Insert common trading symbols
            symbols_data = [
                ('BTCUSDT', 'Bitcoin', 'BTC', 'USDT', 'active', 2, 6, 0.00001, 1000, 0.01),
                ('ETHUSDT', 'Ethereum', 'ETH', 'USDT', 'active', 2, 5, 0.0001, 10000, 0.01),
                ('ADAUSDT', 'Cardano', 'ADA', 'USDT', 'active', 4, 1, 1, 1000000, 0.0001),
                ('DOTUSDT', 'Polkadot', 'DOT', 'USDT', 'active', 3, 2, 0.01, 100000, 0.001),
                ('LINKUSDT', 'Chainlink', 'LINK', 'USDT', 'active', 3, 2, 0.01, 100000, 0.001),
                ('LTCUSDT', 'Litecoin', 'LTC', 'USDT', 'active', 2, 5, 0.0001, 10000, 0.01),
                ('BCHUSDT', 'Bitcoin Cash', 'BCH', 'USDT', 'active', 2, 5, 0.0001, 10000, 0.01),
                ('XRPUSDT', 'Ripple', 'XRP', 'USDT', 'active', 4, 1, 1, 1000000, 0.0001),
                ('EOSUSDT', 'EOS', 'EOS', 'USDT', 'active', 3, 2, 0.01, 100000, 0.001),
                ('TRXUSDT', 'Tron', 'TRX', 'USDT', 'active', 5, 0, 1, 10000000, 0.00001)
            ]
            
            for symbol_data in symbols_data:
                await conn.execute("""
                    INSERT INTO symbols (symbol, name, base_currency, quote_currency, status, 
                                       price_precision, quantity_precision, min_order_size, 
                                       max_order_size, tick_size)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    ON CONFLICT (symbol) DO NOTHING
                """, symbol_data)
            
            # Insert initial configuration
            config_data = [
                ('max_position_size', '0.1', 'decimal', 'Maximum position size as fraction of portfolio'),
                ('max_daily_loss', '0.05', 'decimal', 'Maximum daily loss as fraction of portfolio'),
                ('max_drawdown', '0.2', 'decimal', 'Maximum drawdown threshold'),
                ('risk_free_rate', '0.02', 'decimal', 'Risk-free rate for calculations'),
                ('default_leverage', '1', 'integer', 'Default leverage for new positions'),
                ('stop_loss_percentage', '0.05', 'decimal', 'Default stop loss percentage'),
                ('take_profit_percentage', '0.1', 'decimal', 'Default take profit percentage'),
                ('rebalance_threshold', '0.05', 'decimal', 'Portfolio rebalance threshold'),
                ('min_trade_size', '10', 'decimal', 'Minimum trade size in USD'),
                ('max_trade_size', '10000', 'decimal', 'Maximum trade size in USD')
            ]
            
            for config_item in config_data:
                await conn.execute("""
                    INSERT INTO configuration (key, value, type, description)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (key) DO NOTHING
                """, config_item)
            
            # Insert initial risk limits
            risk_limits_data = [
                ('BTCUSDT', 'position_size', 100000, 0, 0.0),
                ('ETHUSDT', 'position_size', 50000, 0, 0.0),
                ('ADAUSDT', 'position_size', 10000, 0, 0.0),
                ('portfolio', 'daily_loss', 0.05, 0, 0.0),
                ('portfolio', 'total_exposure', 1.0, 0, 0.0),
                ('portfolio', 'max_drawdown', 0.2, 0, 0.0)
            ]
            
            for limit_data in risk_limits_data:
                await conn.execute("""
                    INSERT INTO risk_limits (symbol, limit_type, limit_value, current_value, utilization)
                    VALUES ($1, $2, $3, $4, $5)
                """, limit_data)
    
    async def _verify_database_setup(self):
        """Verify database setup"""
        async with self.db_manager.get_connection() as conn:
            
            # Check if all tables exist
            result = await conn.fetch("""
                SELECT schemaname, tablename 
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY tablename
            """)
            
            table_count = len(result)
            self.logger.info(f"Created {table_count} tables")
            
            # Check if all indexes exist
            result = await conn.fetch("""
                SELECT indexname 
                FROM pg_indexes 
                WHERE schemaname = 'public'
                ORDER BY indexname
            """)
            
            index_count = len(result)
            self.logger.info(f"Created {index_count} indexes")
            
            # Check if all views exist
            result = await conn.fetch("""
                SELECT viewname 
                FROM pg_views 
                WHERE schemaname = 'public'
                ORDER BY viewname
            """)
            
            view_count = len(result)
            self.logger.info(f"Created {view_count} views")
            
            # Check if all functions exist
            result = await conn.fetch("""
                SELECT proname 
                FROM pg_proc 
                WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
                ORDER BY proname
            """)
            
            function_count = len(result)
            self.logger.info(f"Created {function_count} functions")
            
            # Verify data integrity
            symbol_count = await conn.fetchval("SELECT COUNT(*) FROM symbols")
            config_count = await conn.fetchval("SELECT COUNT(*) FROM configuration")
            limit_count = await conn.fetchval("SELECT COUNT(*) FROM risk_limits")
            
            self.logger.info(f"Inserted {symbol_count} symbols, {config_count} config items, {limit_count} risk limits")
            
            self.logger.info("Database setup verification completed successfully")


async def main():
    """Main function to run database initialization"""
    try:
        # Load configuration
        config = BotConfig()
        
        # Initialize database
        db_initializer = DatabaseInitializer(config)
        await db_initializer.initialize_database()
        
        print("Database initialization completed successfully!")
        
    except Exception as e:
        print(f"Database initialization failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
