"""
Database Models for Bybit Trading Bot
SQLAlchemy models for all trading-related data
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy import (
    Column, Integer, String, Float, DateTime, Boolean, 
    Text, JSON, Index, ForeignKey, UniqueConstraint, Date
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class Trade(Base):
    """
    Model for individual trade records
    """
    __tablename__ = 'trades'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    side = Column(String(10), nullable=False)  # 'buy' or 'sell'
    quantity = Column(Float, nullable=False)
    price = Column(Float, nullable=False)
    order_id = Column(String(50), index=True)
    exchange_order_id = Column(String(100), unique=True)
    status = Column(String(20), default='completed', index=True)
    strategy = Column(String(50), index=True)
    
    # Position tracking
    entry_price = Column(Float)
    exit_price = Column(Float)
    profit_loss = Column(Float, default=0.0)
    fees = Column(Float, default=0.0)
    
    # Timestamps
    executed_at = Column(DateTime, default=func.now(), index=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Additional data
    extra_data = Column(JSON, default=dict)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_trades_symbol_date', 'symbol', 'executed_at'),
        Index('idx_trades_strategy_date', 'strategy', 'executed_at'),
    )
    
    def __repr__(self):
        return f"<Trade(id={self.id}, symbol={self.symbol}, side={self.side}, quantity={self.quantity}, price={self.price})>"


class Position(Base):
    """
    Model for open/closed positions
    """
    __tablename__ = 'positions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    side = Column(String(10), nullable=False)  # 'long' or 'short'
    size = Column(Float, nullable=False)
    entry_price = Column(Float, nullable=False)
    current_price = Column(Float)
    
    # P&L tracking
    unrealized_pnl = Column(Float, default=0.0)
    realized_pnl = Column(Float, default=0.0)
    
    # Risk management
    stop_loss = Column(Float)
    take_profit = Column(Float)
    strategy = Column(String(50), index=True)
    
    # Status and timestamps
    status = Column(String(20), default='open', index=True)  # 'open', 'closed', 'partial'
    opened_at = Column(DateTime, default=func.now())
    closed_at = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Additional data
    extra_data = Column(JSON, default=dict)
    
    # Relationship to trades
    trades = relationship("Trade", backref="position", foreign_keys="Trade.order_id")
    
    __table_args__ = (
        Index('idx_positions_symbol_status', 'symbol', 'status'),
        Index('idx_positions_strategy_status', 'strategy', 'status'),
    )
    
    def __repr__(self):
        return f"<Position(id={self.id}, symbol={self.symbol}, side={self.side}, size={self.size}, status={self.status})>"


class Strategy(Base):
    """
    Model for trading strategy configurations and parameters
    """
    __tablename__ = 'strategies'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(50), unique=True, nullable=False, index=True)
    version = Column(String(20), default='1.0')
    description = Column(Text)
    
    # Strategy parameters
    parameters = Column(JSON, default=dict)
    
    # Performance tracking
    is_active = Column(Boolean, default=True)
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    total_pnl = Column(Float, default=0.0)
    max_drawdown = Column(Float, default=0.0)
    win_rate = Column(Float, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_optimized_at = Column(DateTime)
    
    # Additional data
    extra_data = Column(JSON, default=dict)
    
    def __repr__(self):
        return f"<Strategy(id={self.id}, name={self.name}, version={self.version}, active={self.is_active})>"


# === MARKET DATA MODELS ===

class MarketData(Base):
    """Market data storage"""
    __tablename__ = 'market_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # OHLCV data
    open_price = Column(Float, nullable=False)
    high_price = Column(Float, nullable=False)
    low_price = Column(Float, nullable=False)
    close_price = Column(Float, nullable=False)
    volume = Column(Float, nullable=False)
    
    # Additional market data
    volume_quote = Column(Float)
    trade_count = Column(Integer)
    
    # Metadata
    exchange = Column(String(20), default='bybit')
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_market_data_symbol_timestamp', 'symbol', 'timestamp'),
        UniqueConstraint('symbol', 'timestamp', 'exchange', name='uq_market_data_symbol_timestamp_exchange'),
    )


class OrderBookSnapshot(Base):
    """Order book snapshots"""
    __tablename__ = 'order_book_snapshots'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # Best bid/ask
    best_bid = Column(Float, nullable=False)
    best_ask = Column(Float, nullable=False)
    bid_size = Column(Float, nullable=False)
    ask_size = Column(Float, nullable=False)
    
    # Order book depth
    order_book_data = Column(JSON)  # Full order book data
    
    # Metrics
    spread = Column(Float)
    mid_price = Column(Float)
    
    exchange = Column(String(20), default='bybit')
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_order_book_symbol_timestamp', 'symbol', 'timestamp'),
    )


class MarketIndicators(Base):
    """Technical indicators and market metrics"""
    __tablename__ = 'market_indicators'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # Technical indicators
    sma_20 = Column(Float)
    sma_50 = Column(Float)
    ema_12 = Column(Float)
    ema_26 = Column(Float)
    rsi_14 = Column(Float)
    macd = Column(Float)
    macd_signal = Column(Float)
    bollinger_upper = Column(Float)
    bollinger_lower = Column(Float)
    
    # Volume indicators
    volume_sma = Column(Float)
    volume_ratio = Column(Float)
    
    # Volatility
    atr = Column(Float)
    volatility = Column(Float)
    
    # Market structure
    support_level = Column(Float)
    resistance_level = Column(Float)
    trend_direction = Column(String(10))  # 'up', 'down', 'sideways'
    
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_market_indicators_symbol_timestamp', 'symbol', 'timestamp'),
    )


class ArbitrageOpportunity(Base):
    """Cross-exchange arbitrage opportunities"""
    __tablename__ = 'arbitrage_opportunities'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # Exchange data
    buy_exchange = Column(String(20), nullable=False)
    sell_exchange = Column(String(20), nullable=False)
    buy_price = Column(Float, nullable=False)
    sell_price = Column(Float, nullable=False)
    
    # Opportunity metrics
    profit_percentage = Column(Float, nullable=False)
    profit_amount = Column(Float)
    volume_limit = Column(Float)
    
    # Status
    status = Column(String(20), default='detected')  # 'detected', 'executed', 'expired'
    
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_arbitrage_symbol_timestamp', 'symbol', 'timestamp'),
        Index('idx_arbitrage_profit', 'profit_percentage'),
    )


# === NEWS AND SENTIMENT MODELS ===

class NewsSentiment(Base):
    """News articles and sentiment analysis"""
    __tablename__ = 'news_sentiment'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    article_id = Column(String(255), unique=True, nullable=False)
    
    # Article data
    title = Column(Text, nullable=False)
    content = Column(Text)
    author = Column(String(255))
    source = Column(String(100), nullable=False, index=True)
    url = Column(Text)
    published_at = Column(DateTime, nullable=False, index=True)
    
    # Sentiment analysis
    sentiment_compound = Column(Float, nullable=False)
    sentiment_positive = Column(Float, nullable=False)
    sentiment_negative = Column(Float, nullable=False)
    sentiment_neutral = Column(Float, nullable=False)
    
    # Keywords and categories
    keywords = Column(Text)  # Comma-separated
    category = Column(String(50))
    impact_score = Column(Float, default=0.0)
    
    # Processing metadata
    processed = Column(Boolean, default=False)
    timestamp = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_news_sentiment_published', 'published_at'),
        Index('idx_news_sentiment_source', 'source'),
        Index('idx_news_sentiment_impact', 'impact_score'),
    )


class SocialSentiment(Base):
    """Social media sentiment data"""
    __tablename__ = 'social_sentiment'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    post_id = Column(String(255), nullable=False)
    platform = Column(String(50), nullable=False, index=True)
    
    # Post data
    author_id = Column(String(255))
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, nullable=False, index=True)
    
    # Sentiment analysis
    keyword = Column(String(100), index=True)
    sentiment_compound = Column(Float, nullable=False)
    sentiment_positive = Column(Float, nullable=False)
    sentiment_negative = Column(Float, nullable=False)
    sentiment_neutral = Column(Float, nullable=False)
    
    # Engagement metrics
    engagement_score = Column(Float, default=0.0)
    crypto_mentions = Column(Text)  # Comma-separated
    
    # Extra data
    extra_data = Column(JSON, default=dict)
    timestamp = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_social_sentiment_platform_created', 'platform', 'created_at'),
        Index('idx_social_sentiment_keyword', 'keyword'),
        UniqueConstraint('post_id', 'platform', name='uq_social_post_platform'),
    )


class SocialSentimentTrends(Base):
    """Aggregated social sentiment trends"""
    __tablename__ = 'social_sentiment_trends'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    platform = Column(String(50), nullable=False, index=True)
    keyword = Column(String(100), nullable=False, index=True)
    
    # Aggregated metrics
    avg_sentiment = Column(Float, nullable=False)
    post_count = Column(Integer, nullable=False)
    avg_engagement = Column(Float, nullable=False)
    trend_score = Column(Float, nullable=False)
    
    __table_args__ = (
        UniqueConstraint('timestamp', 'platform', 'keyword', name='uq_sentiment_trends'),
    )


class SocialInfluencers(Base):
    """Social media influencer tracking"""
    __tablename__ = 'social_influencers'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    author_id = Column(String(255), nullable=False, index=True)
    platform = Column(String(50), nullable=False, index=True)
    
    # Influence metrics
    post_count = Column(Integer, nullable=False)
    avg_engagement = Column(Float, nullable=False)
    avg_sentiment = Column(Float, nullable=False)
    viral_posts = Column(Integer, default=0)
    influence_score = Column(Float, nullable=False)
    
    __table_args__ = (
        UniqueConstraint('timestamp', 'author_id', 'platform', name='uq_influencers'),
    )


class SocialSymbolMetrics(Base):
    """Social metrics aggregated by trading symbols"""
    __tablename__ = 'social_symbol_metrics'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    
    # Aggregated metrics
    total_posts = Column(Integer, nullable=False)
    avg_sentiment = Column(Float, nullable=False)
    total_engagement = Column(Float, nullable=False)
    sentiment_momentum = Column(Float, nullable=False)
    timeframe_hours = Column(Integer, nullable=False)
    
    __table_args__ = (
        Index('idx_social_symbol_metrics', 'symbol', 'timestamp'),
    )


# === ECONOMIC DATA MODELS ===

class EconomicIndicators(Base):
    """Economic indicators data"""
    __tablename__ = 'economic_indicators'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    indicator_name = Column(String(100), nullable=False, index=True)
    series_id = Column(String(50), nullable=False)
    date = Column(Date, nullable=False, index=True)
    value = Column(Float, nullable=False)
    timestamp = Column(DateTime, default=func.now())
    
    __table_args__ = (
        UniqueConstraint('indicator_name', 'date', name='uq_economic_indicator_date'),
        Index('idx_economic_indicators_name_date', 'indicator_name', 'date'),
    )


class EconomicIndicatorAnalysis(Base):
    """Economic indicator trend analysis"""
    __tablename__ = 'economic_indicator_analysis'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    indicator_name = Column(String(100), nullable=False, index=True)
    series_id = Column(String(50), nullable=False)
    
    # Analysis data
    current_value = Column(Float, nullable=False)
    change_pct = Column(Float, nullable=False)
    trend = Column(String(20), nullable=False)  # 'UPTREND', 'DOWNTREND', 'SIDEWAYS'
    ma_short = Column(Float)
    ma_long = Column(Float)
    analysis_date = Column(DateTime, nullable=False, index=True)
    
    __table_args__ = (
        UniqueConstraint('indicator_name', 'analysis_date', name='uq_indicator_analysis'),
    )


class EconomicCryptoCorrelations(Base):
    """Correlations between economic data and crypto prices"""
    __tablename__ = 'economic_crypto_correlations'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    indicator_name = Column(String(100), nullable=False, index=True)
    correlation = Column(Float, nullable=False)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    __table_args__ = (
        Index('idx_econ_crypto_corr', 'symbol', 'indicator_name', 'timestamp'),
    )


class EconomicEvents(Base):
    """Economic calendar events"""
    __tablename__ = 'economic_events'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    event_name = Column(String(255), nullable=False)
    country = Column(String(10), nullable=False)
    date = Column(DateTime, nullable=False, index=True)
    importance = Column(String(20), nullable=False)  # 'Low', 'Medium', 'High'
    
    # Event data
    previous = Column(Float)
    forecast = Column(Float)
    actual = Column(Float)
    
    timestamp = Column(DateTime, default=func.now())
    
    __table_args__ = (
        UniqueConstraint('event_name', 'date', name='uq_economic_event'),
        Index('idx_economic_events_date_importance', 'date', 'importance'),
    )


class CentralBankEvents(Base):
    """Central bank announcements and meetings"""
    __tablename__ = 'central_bank_events'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    bank = Column(String(20), nullable=False, index=True)  # 'FED', 'ECB', 'BOJ', etc.
    event_name = Column(String(255), nullable=False)
    date = Column(DateTime, nullable=False, index=True)
    importance = Column(String(20), nullable=False)
    description = Column(Text)
    timestamp = Column(DateTime, default=func.now())
    
    __table_args__ = (
        UniqueConstraint('bank', 'event_name', 'date', name='uq_central_bank_event'),
    )


class EconomicIndexes(Base):
    """Custom economic indexes calculated by the bot"""
    __tablename__ = 'economic_indexes'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # Custom indexes (0-100 scale)
    fear_greed_index = Column(Float, nullable=False)
    inflation_index = Column(Float, nullable=False)
    liquidity_index = Column(Float, nullable=False)
    momentum_index = Column(Float, nullable=False)


# === ML AND PREDICTION MODELS ===

class MLPredictions(Base):
    """Machine learning predictions"""
    __tablename__ = 'ml_predictions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    horizon_name = Column(String(50), nullable=False)  # 'short_term', 'medium_term', etc.
    horizon_minutes = Column(Integer, nullable=False)
    
    # Predictions
    ensemble_prediction = Column(Float, nullable=False)
    ensemble_confidence = Column(Float, nullable=False)
    individual_predictions = Column(JSON)  # Individual model predictions
    
    timestamp = Column(DateTime, nullable=False, index=True)
    
    __table_args__ = (
        Index('idx_ml_predictions_symbol_timestamp', 'symbol', 'timestamp'),
    )


class MLModelPerformance(Base):
    """ML model performance tracking"""
    __tablename__ = 'ml_model_performance'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    evaluation_time = Column(DateTime, nullable=False, index=True)
    
    # Performance metrics
    sample_size = Column(Integer, nullable=False)
    ensemble_accuracy = Column(Float, nullable=False)
    performance_metrics = Column(JSON)  # Detailed metrics
    
    __table_args__ = (
        Index('idx_ml_performance_symbol_time', 'symbol', 'evaluation_time'),
    )


class MLTradingSignals(Base):
    """ML-generated trading signals"""
    __tablename__ = 'ml_trading_signals'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, index=True)
    
    # Signal data
    signal_direction = Column(String(10), nullable=False)  # 'BUY', 'SELL', 'HOLD'
    signal_strength = Column(Float, nullable=False)
    expected_return = Column(Float, nullable=False)
    confidence = Column(Float, nullable=False)
    
    # Price data
    current_price = Column(Float, nullable=False)
    predicted_price = Column(Float, nullable=False)
    
    # Risk metrics
    price_volatility = Column(Float, nullable=False)
    prediction_consistency = Column(Float, nullable=False)
    
    timestamp = Column(DateTime, nullable=False, index=True)
    
    __table_args__ = (
        Index('idx_ml_signals_symbol_timestamp', 'symbol', 'timestamp'),
    )


# === RISK MANAGEMENT MODELS ===

class RiskEvents(Base):
    """Risk management events and violations"""
    __tablename__ = 'risk_events'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # Event data
    event_type = Column(String(50), nullable=False, index=True)
    severity = Column(String(20), nullable=False)  # 'INFO', 'WARNING', 'CRITICAL'
    message = Column(Text, nullable=False)
    action_taken = Column(String(100))
    
    # Risk metrics at time of event
    risk_score = Column(Float)
    drawdown = Column(Float)
    daily_pnl = Column(Float)
    
    __table_args__ = (
        Index('idx_risk_events_type_severity', 'event_type', 'severity'),
    )


class PositionRiskTracking(Base):
    """Position-level risk tracking"""
    __tablename__ = 'position_risk_tracking'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    position_id = Column(Integer, ForeignKey('positions.id'), index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    
    # Risk metrics
    risk_amount = Column(Float, nullable=False)
    risk_percentage = Column(Float, nullable=False)
    volatility = Column(Float, nullable=False)
    correlation_adjustment = Column(Float, default=1.0)
    kelly_multiplier = Column(Float, default=1.0)
    
    # Position data at time
    current_price = Column(Float, nullable=False)
    unrealized_pnl = Column(Float, nullable=False)
    
    __table_args__ = (
        Index('idx_position_risk_tracking', 'position_id', 'timestamp'),
    )


class Performance(Base):
    """Performance metrics tracking"""
    __tablename__ = 'performance'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    strategy = Column(String(50), index=True)
    symbol = Column(String(20), index=True)
    period_start = Column(DateTime, nullable=False, index=True)
    period_end = Column(DateTime, nullable=False)
    
    # Performance metrics
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    win_rate = Column(Float, default=0.0)
    total_pnl = Column(Float, default=0.0)
    avg_win = Column(Float, default=0.0)
    avg_loss = Column(Float, default=0.0)
    max_drawdown = Column(Float, default=0.0)
    sharpe_ratio = Column(Float, default=0.0)
    volatility = Column(Float, default=0.0)
    
    # Additional data (renamed from metadata to avoid SQLAlchemy conflict)
    extra_data = Column(JSON, default=dict)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    __table_args__ = (
        Index('idx_performance_strategy_period', 'strategy', 'period_start'),
    )
    
    def __repr__(self):
        return f"<Performance(id={self.id}, strategy={self.strategy}, pnl={self.total_pnl})>"



class SystemLog(Base):
    """System log entries"""
    __tablename__ = 'system_logs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    level = Column(String(10), nullable=False, index=True)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    component = Column(String(50), nullable=False, index=True)
    message = Column(Text, nullable=False)
    details = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), index=True)
    
    __table_args__ = (
        Index('idx_system_logs_level_time', 'level', 'created_at'),
        Index('idx_system_logs_component_time', 'component', 'created_at'),
    )
    
    def __repr__(self):
        return f"<SystemLog(id={self.id}, level={self.level}, component={self.component})>"


class HardwareStatus(Base):
    """
    Model for hardware monitoring data
    """
    __tablename__ = 'hardware_status'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # CPU metrics
    cpu_usage_percent = Column(Float)
    cpu_temperature = Column(Float)
    cpu_frequency = Column(Float)
    
    # Memory metrics
    memory_usage_percent = Column(Float)
    memory_used_gb = Column(Float)
    memory_available_gb = Column(Float)
    
    # Disk metrics
    disk_usage_percent = Column(Float)
    disk_free_gb = Column(Float)
    disk_total_gb = Column(Float)
    
    # Network metrics
    network_sent_mb = Column(Float)
    network_received_mb = Column(Float)
    
    # System health
    overall_status = Column(String(20), default='healthy')  # healthy, warning, critical
    uptime_seconds = Column(Integer)
    
    # Timestamps
    timestamp = Column(DateTime, default=func.now(), index=True)
    
    # Additional data
    extra_data = Column(JSON, default=dict)
    
    __table_args__ = (
        Index('idx_hardware_status_timestamp', 'timestamp'),
        Index('idx_hardware_status_overall', 'overall_status'),
    )
    
    def __repr__(self):
        return f"<HardwareStatus(id={self.id}, status={self.overall_status}, cpu={self.cpu_usage_percent}%)>"


class Alert(Base):
    """
    Model for system alerts and notifications
    """
    __tablename__ = 'alerts'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    type = Column(String(50), nullable=False, index=True)  # trade, hardware, error, performance
    severity = Column(String(20), nullable=False, index=True)  # info, warning, error, critical
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    
    # Status
    is_acknowledged = Column(Boolean, default=False)
    acknowledged_at = Column(DateTime)
    
    # Related data
    related_id = Column(Integer)  # ID of related record (trade, position, etc.)
    related_type = Column(String(50))  # type of related record
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), index=True)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Additional data
    extra_data = Column(JSON, default=dict)
    
    __table_args__ = (
        Index('idx_alerts_type_severity', 'type', 'severity'),
        Index('idx_alerts_acknowledged', 'is_acknowledged'),
    )
    
    def __repr__(self):
        return f"<Alert(id={self.id}, type={self.type}, severity={self.severity}, acknowledged={self.is_acknowledged})>"


class BacktestResult(Base):
    """
    Model for storing backtest results
    """
    __tablename__ = 'backtest_results'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    strategy_name = Column(String(50), nullable=False, index=True)
    symbol = Column(String(20), nullable=False)
    
    # Test parameters
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    initial_balance = Column(Float, nullable=False)
    parameters = Column(JSON, default=dict)
    
    # Results
    final_balance = Column(Float)
    total_return = Column(Float)
    total_trades = Column(Integer)
    winning_trades = Column(Integer)
    win_rate = Column(Float)
    max_drawdown = Column(Float)
    sharpe_ratio = Column(Float)
    
    # Detailed results
    trade_history = Column(JSON, default=list)
    equity_curve = Column(JSON, default=list)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_backtest_strategy_symbol', 'strategy_name', 'symbol'),
    )
    
    def __repr__(self):
        return f"<BacktestResult(id={self.id}, strategy={self.strategy_name}, return={self.total_return})>"
