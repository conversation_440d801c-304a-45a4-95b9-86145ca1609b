<p align="center">
  <a href="https://superagi.com//#gh-light-mode-only">
    <img src="https://superagi.com/wp-content/uploads/2023/05/Logo-dark.svg" width="318px" alt="SuperAGI logo" />
  </a>
  <a href="https://superagi.com//#gh-dark-mode-only">
    <img src="https://superagi.com/wp-content/uploads/2023/05/Logo-light.svg" width="318px" alt="SuperAGI logo" />
  </a>
</p>

# SuperAGI Google SERP Search Toolkit

The SuperAGI Google Search Toolkit helps users perform a Google search and extract snippets and webpages.

## ⚙️ Installation

### 🛠 **Setting Up of SuperAGI**
Set up the SuperAGI by following the instructions given (https://github.com/TransformerOptimus/SuperAGI/blob/main/README.MD)

### 🔧 **Add Google Serp Search API Key in SuperAGI Dashboard**

1. Register an account at [https://serper.dev/](https://serper.dev/) with your Email ID. 

2. Your Private API Key would be made. Copy that and save it in a separate text file.

![Serper_Key](https://github.com/Phoenix2809/SuperAGI/assets/*********/dfe70b4f-11e2-483b-aa33-07b15150103d)


3. Open up the Google SERP Toolkit page in SuperAGI's Dashboard and paste your Private API Key. 

## Running SuperAGI Google Search Serp Tool

You can simply ask your agent about the latest information regarding anything and your agent will be able to browse the internet to get that information for you. 
