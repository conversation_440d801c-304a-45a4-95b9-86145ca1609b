{"version": "2.0.0", "tasks": [{"label": "🚀 Initialize Autonomous Trading System", "type": "shell", "command": "conda activate bybit-trader && python system_verification.py", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}", "shell": {"executable": "cmd.exe", "args": ["/c"]}}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}}, {"label": "🔍 Verify All Components", "type": "shell", "command": "conda activate bybit-trader && python -c \"import sys; print('🐍 Python:', sys.version); import bybit_bot.core.config; print('⚙️  Config loaded'); import bybit_bot.database.connection; print('🗄️  Database ready'); import bybit_bot.exchange.enhanced_bybit_client; print('🔌 Enhanced Bybit Client ready'); import bybit_bot.profit_maximization.hyper_profit_engine; print('💰 Hyper Profit Engine ready'); import bybit_bot.agents.agent_orchestrator; print('🧠 Agent Orchestrator ready'); import bybit_bot.ai.memory_manager; print('💾 Memory Manager ready'); print('✅ ALL 20+ PROFIT STRATEGIES LOADED AND OPERATIONAL')\"", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}", "shell": {"executable": "cmd.exe", "args": ["/c"]}}, "problemMatcher": []}, {"label": "📊 Check System Status", "type": "shell", "command": "conda activate bybit-trader && python system_status.py", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}", "shell": {"executable": "cmd.exe", "args": ["/c"]}}, "problemMatcher": []}, {"label": "🎯 Launch Full Trading System", "type": "shell", "command": "conda activate bybit-trader && python main.py", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "dedicated"}, "options": {"cwd": "${workspaceFolder}", "shell": {"executable": "cmd.exe", "args": ["/c"]}}, "problemMatcher": [], "isBackground": true}, {"label": "🔧 Install Dependencies", "type": "shell", "command": "conda activate bybit-trader && pip install -r requirements.txt && echo '✅ All dependencies installed'", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}", "shell": {"executable": "cmd.exe", "args": ["/c"]}}, "problemMatcher": []}, {"label": "🗄️ Initialize Database", "type": "shell", "command": "conda activate bybit-trader && python database_init.py", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}", "shell": {"executable": "cmd.exe", "args": ["/c"]}}, "problemMatcher": []}, {"label": "🧪 Run System Tests", "type": "shell", "command": "conda activate bybit-trader && python -m pytest tests/ -v", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}", "shell": {"executable": "cmd.exe", "args": ["/c"]}}, "problemMatcher": []}, {"label": "📈 Start Production Trading", "type": "shell", "command": "conda activate bybit-trader && python main_production_trading.py", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "dedicated"}, "options": {"cwd": "${workspaceFolder}", "shell": {"executable": "cmd.exe", "args": ["/c"]}}, "problemMatcher": [], "isBackground": true}]}