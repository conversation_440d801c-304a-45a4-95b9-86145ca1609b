{"version": "0.2.0", "configurations": [{"name": "🚀 Launch Autonomous Trading Bot", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/main.py", "console": "integratedTerminal", "python": "E:\\Miniconda\\envs\\bybit-trader\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "bybit-trader", "TRADING_ENV": "development"}, "args": [], "justMyCode": false, "stopOnEntry": false, "showReturnValue": true, "preLaunchTask": "🔍 Verify All Components"}, {"name": "💰 Launch Hyper Profit Engine", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/run_super_gpt_bot.py", "console": "integratedTerminal", "python": "E:\\Miniconda\\envs\\bybit-trader\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "bybit-trader", "TRADING_ENV": "development", "PROFIT_MODE": "maximum"}, "args": [], "justMyCode": false, "stopOnEntry": false, "showReturnValue": true}, {"name": "📈 Launch Production Trading", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/main_production_trading.py", "console": "integratedTerminal", "python": "E:\\Miniconda\\envs\\bybit-trader\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "bybit-trader", "TRADING_ENV": "production", "PROFIT_MODE": "maximum", "AUTONOMOUS_MODE": "true"}, "args": [], "justMyCode": false, "stopOnEntry": false, "showReturnValue": true, "preLaunchTask": "🗄️ Initialize Database"}, {"name": "🧠 Debug SuperGPT Agent System", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/start_enhanced_ai_system.py", "console": "integratedTerminal", "python": "E:\\Miniconda\\envs\\bybit-trader\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "bybit-trader", "DEBUG_MODE": "true", "AI_SYSTEMS": "all"}, "args": [], "justMyCode": false, "stopOnEntry": true, "showReturnValue": true}, {"name": "🔧 Debug Component Integration", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/test_ai_systems_integration.py", "console": "integratedTerminal", "python": "E:\\Miniconda\\envs\\bybit-trader\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "bybit-trader", "DEBUG_MODE": "true"}, "args": [], "justMyCode": false, "stopOnEntry": false, "showReturnValue": true}, {"name": "📊 System Status Monitor", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/system_status.py", "console": "integratedTerminal", "python": "E:\\Miniconda\\envs\\bybit-trader\\python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "bybit-trader"}, "args": [], "justMyCode": false, "stopOnEntry": false, "showReturnValue": true}]}