version: '3.8'

services:
  backend:
    volumes:
      - "./:/app"
    build: .
    ports:
      - "8001:8001"
    depends_on:
      - super__tgwui
      - super__redis
      - super__postgres
    networks:
      - super_network

  celery:
    volumes:
      - "./:/app"
    build:
      context: .
      dockerfile: DockerfileCelery
    depends_on:
      - super__tgwui
      - super__redis
      - super__postgres
    networks:
      - super_network
    
  gui:
    build: ./gui
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:8001
    networks:
      - super_network
    volumes:
      - ./gui:/app
      - /app/node_modules
      - /app/.next

  super__tgwui:
    build:
      context: .
      dockerfile: ./tgwui/DockerfileTGWUI
    container_name: super__tgwui
    environment:
      - EXTRA_LAUNCH_ARGS="--listen --verbose --extensions openai --threads 4 --n_ctx 1600"
    ports:
      - 7860:7860  # Default web port
      - 5000:5000  # Default API port
      - 5005:5005  # Default streaming port
      - 5001:5001  # Default OpenAI API extension port
    volumes:
      - ./tgwui/config/loras:/app/loras
      - ./tgwui/config/models:/app/models
      - ./tgwui/config/presets:/app/presets
      - ./tgwui/config/prompts:/app/prompts
      - ./tgwui/config/softprompts:/app/softprompts
      - ./tgwui/config/training:/app/training
    logging:
      driver:  json-file
      options:
        max-file: "3"   # number of files or file count
        max-size: '10m'
    networks:
      - super_network

  super__redis:
    image: "docker.io/library/redis:latest"
    networks:
      - super_network

  super__postgres:
    image: "docker.io/library/postgres:latest"
    environment:
      - POSTGRES_USER=superagi
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=super_agi_main
    volumes:
      - superagi_postgres_data:/var/lib/postgresql/data/
    networks:
      - super_network
    ports:
      - "5432:5432"

networks:
  super_network:
    driver: bridge

volumes:
  superagi_postgres_data:
