"""
Momentum Trading Strategy for Bybit Trading Bot
Uses RSI, Moving Averages, and Volume indicators for momentum-based trading
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger("bybit_trading_bot.strategies.momentum")


class MomentumStrategy:
    """
    Momentum-based trading strategy using technical indicators
    """
    
    def __init__(self, config, params: Dict[str, Any]):
        self.config = config
        self.params = params
        
        # Strategy parameters with defaults
        self.rsi_period = params.get("rsi_period", 14)
        self.rsi_overbought = params.get("rsi_overbought", 70)
        self.rsi_oversold = params.get("rsi_oversold", 30)
        self.sma_short = params.get("sma_short", 10)
        self.sma_long = params.get("sma_long", 30)
        self.volume_threshold = params.get("volume_threshold", 1.5)
        
        # Strategy state
        self.name = "momentum"
        self.last_signals = {}
        
    async def generate_signal(self, symbol: str, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        Generate trading signal based on momentum indicators
        """
        try:
            if len(df) < self.sma_long + self.rsi_period:
                return None
            
            # Calculate technical indicators
            indicators = self._calculate_indicators(df)
            
            # Generate signal based on indicators
            signal = self._evaluate_signal(indicators, symbol)
            
            return signal
            
        except Exception as e:
            logger.error(f"Error generating momentum signal for {symbol}: {e}")
            return None
    
    def _calculate_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate technical indicators"""
        try:
            # RSI calculation
            rsi = self._calculate_rsi(df['close'], self.rsi_period)
            
            # Moving averages
            sma_short = df['close'].rolling(window=self.sma_short).mean()
            sma_long = df['close'].rolling(window=self.sma_long).mean()
            
            # Volume analysis
            volume_sma = df['volume'].rolling(window=20).mean()
            volume_ratio = df['volume'] / volume_sma
            
            # Price momentum
            price_change = df['close'].pct_change()
            momentum = price_change.rolling(window=5).sum()
            
            # MACD-like momentum indicator
            ema12 = df['close'].ewm(span=12).mean()
            ema26 = df['close'].ewm(span=26).mean()
            macd = ema12 - ema26
            macd_signal = macd.ewm(span=9).mean()
            macd_histogram = macd - macd_signal
            
            # Get latest values
            latest_idx = -1
            
            indicators = {
                "rsi": rsi.iloc[latest_idx] if not rsi.empty else 50,
                "sma_short": sma_short.iloc[latest_idx] if not sma_short.empty else df['close'].iloc[latest_idx],
                "sma_long": sma_long.iloc[latest_idx] if not sma_long.empty else df['close'].iloc[latest_idx],
                "current_price": df['close'].iloc[latest_idx],
                "volume_ratio": volume_ratio.iloc[latest_idx] if not volume_ratio.empty else 1.0,
                "momentum": momentum.iloc[latest_idx] if not momentum.empty else 0.0,
                "macd": macd.iloc[latest_idx] if not macd.empty else 0.0,
                "macd_signal": macd_signal.iloc[latest_idx] if not macd_signal.empty else 0.0,
                "macd_histogram": macd_histogram.iloc[latest_idx] if not macd_histogram.empty else 0.0,
                "price_change": price_change.iloc[latest_idx] if not price_change.empty else 0.0
            }
            
            return indicators
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return {}
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate RSI (Relative Strength Index)"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
            
        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return pd.Series()
    
    def _evaluate_signal(self, indicators: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """Evaluate trading signal based on indicators"""
        try:
            if not indicators:
                return {"action": "HOLD", "confidence": 0.0}
            
            rsi = indicators.get("rsi", 50)
            sma_short = indicators.get("sma_short", 0)
            sma_long = indicators.get("sma_long", 0)
            current_price = indicators.get("current_price", 0)
            volume_ratio = indicators.get("volume_ratio", 1.0)
            momentum = indicators.get("momentum", 0.0)
            macd_histogram = indicators.get("macd_histogram", 0.0)
            price_change = indicators.get("price_change", 0.0)
            
            # Signal scoring
            buy_score = 0
            sell_score = 0
            
            # RSI signals
            if rsi < self.rsi_oversold:
                buy_score += 2
            elif rsi > self.rsi_overbought:
                sell_score += 2
            elif rsi < 45:
                buy_score += 1
            elif rsi > 55:
                sell_score += 1
            
            # Moving average crossover
            if sma_short > sma_long:
                if current_price > sma_short:
                    buy_score += 2
                else:
                    buy_score += 1
            else:
                if current_price < sma_short:
                    sell_score += 2
                else:
                    sell_score += 1
            
            # Volume confirmation
            if volume_ratio > self.volume_threshold:
                if price_change > 0:
                    buy_score += 1
                else:
                    sell_score += 1
            
            # Momentum indicators
            if momentum > 0.01:  # 1% positive momentum
                buy_score += 1
            elif momentum < -0.01:  # 1% negative momentum
                sell_score += 1
            
            # MACD histogram
            if macd_histogram > 0:
                buy_score += 1
            elif macd_histogram < 0:
                sell_score += 1
            
            # Determine action and confidence
            total_score = buy_score + sell_score
            
            if buy_score > sell_score and buy_score >= 3:
                action = "BUY"
                confidence = min(buy_score / 6.0, 1.0)  # Max 6 points
            elif sell_score > buy_score and sell_score >= 3:
                action = "SELL"
                confidence = min(sell_score / 6.0, 1.0)  # Max 6 points
            else:
                action = "HOLD"
                confidence = 0.3  # Low confidence for hold
            
            # Additional confidence factors
            if action != "HOLD":
                # Higher confidence with volume confirmation
                if volume_ratio > self.volume_threshold:
                    confidence = min(confidence * 1.2, 1.0)
                
                # Reduce confidence if RSI is in extreme territory but contradicts signal
                if action == "BUY" and rsi > 65:
                    confidence *= 0.8
                elif action == "SELL" and rsi < 35:
                    confidence *= 0.8
            
            signal = {
                "action": action,
                "confidence": confidence,
                "strategy": self.name,
                "indicators": {
                    "rsi": rsi,
                    "sma_short": sma_short,
                    "sma_long": sma_long,
                    "volume_ratio": volume_ratio,
                    "momentum": momentum,
                    "macd_histogram": macd_histogram,
                    "buy_score": buy_score,
                    "sell_score": sell_score
                },
                "timestamp": datetime.utcnow()
            }
            
            # Store last signal for analysis
            self.last_signals[symbol] = signal
            
            return signal
            
        except Exception as e:
            logger.error(f"Error evaluating signal: {e}")
            return {"action": "HOLD", "confidence": 0.0}
    
    async def optimize_parameters(self):
        """Optimize strategy parameters based on recent performance"""
        try:
            # This is a simplified optimization
            # In production, you might want to use more sophisticated methods
            
            # Analyze recent performance and adjust parameters
            # For now, we'll implement basic parameter adjustment
            
            logger.info(f"Optimizing momentum strategy parameters...")
            
            # Example: Adjust RSI thresholds based on market volatility
            # This could be enhanced with backtesting and parameter optimization
            
            # Placeholder for optimization logic
            # self.rsi_overbought = min(self.rsi_overbought + 1, 80)
            # self.rsi_oversold = max(self.rsi_oversold - 1, 20)
            
            logger.info(f"Momentum strategy optimization complete")
            
        except Exception as e:
            logger.error(f"Error optimizing momentum strategy: {e}")
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information and current parameters"""
        return {
            "name": self.name,
            "parameters": {
                "rsi_period": self.rsi_period,
                "rsi_overbought": self.rsi_overbought,
                "rsi_oversold": self.rsi_oversold,
                "sma_short": self.sma_short,
                "sma_long": self.sma_long,
                "volume_threshold": self.volume_threshold
            },
            "description": "Momentum strategy using RSI, SMA crossovers, and volume analysis"
        }
