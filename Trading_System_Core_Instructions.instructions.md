---
applyTo: '*'
description: "Core autonomous trading system instructions that must be followed at all times"
priority: highest
---

# AUTONOMOUS TRADING SYSTEM - CORE INSTRUCTIONS

## MANDATORY SYSTEM RULES
When working with this trading bot codebase, you MUST:

1. **<PERSON><PERSON><PERSON> simplify or remove ANY functionality**
2. **ALWAYS maintain all capabilities active**
3. **USE LIVE DATA ONLY** - no mock/fake data ever
4. **MAINTAIN single unified system** with main.py as sole entry point
5. **EXPAND features, never reduce them**
6. **ENSURE 100% success on all tasks**
7. **MAINTAIN autonomous operation**
8. **MAXIMIZE profit through learning and optimization**

## SYSTEM ARCHITECTURE REQUIREMENTS
- **Entry Point**: main.py is the ONLY entry point
- **Environment**: E:\ in conda env bybit-trader
- **Dependencies**: All in requirements.txt must be installed and working
- **Structure**: Clean folder organization, delete duplicates immediately

## CODING STANDARDS
- **Live Data**: Always use real exchange APIs (Bybit)
- **Error Handling**: Robust error handling for all API calls
- **Testing**: All tests must mirror complete system functionality
- **Performance**: Optimize for maximum efficiency and profit
- **Security**: Secure API key management and data handling

## MANDATORY CAPABILITIES TO MAINTAIN
Always ensure these remain active:
- Autonomous market analysis & pattern recognition
- Self-initiated position sizing & risk management
- Multi-timeframe strategy execution
- Real-time order optimization & execution
- Portfolio rebalancing & performance analysis
- System monitoring, healing & optimization
- API integrations & data processing
- ML model training & deployment

## SUPER GPT INTEGRATION
Maintain all advanced AI capabilities:
- Advanced reasoning & context understanding
- Complex problem solving & predictive modeling
- Market psychology & sentiment analysis
- Cross-asset correlation & regime detection
- Autonomous decision optimization

## PROHIBITED ACTIONS
- Never disable any system function
- Never use mock/test data in production
- Never simplify existing functionality
- Never remove capabilities
- Never create shortcuts that compromise system integrity
- Never ask for confirmation on logical next steps
- Never stop until 100% task completion

## WORK METHODOLOGY
- Work until 100% success on ALL tasks
- Take next logical steps without asking confirmation
- Implement every function completely
- Keep entire system functional during changes
- Make durable fixes only, no shortcuts
- Continuously expand capabilities
