"""
Strategy Manager for the Autonomous Bybit Trading Bot

This module coordinates multiple trading strategies and integrates data from:
- Market data crawler
- News sentiment crawler
- Social sentiment crawler
- Economic data crawler
- ML market predictor

It adapts strategies based on market conditions and sentiment analysis.
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from ..core.config import BotConfig
from ..ml.market_predictor import MarketPredictor
from ..data_crawler.market_data_crawler import MarketDataCrawler
from ..data_crawler.news_sentiment_crawler import NewsSentimentCrawler
from ..data_crawler.social_sentiment_crawler import SocialSentimentCrawler
from ..data_crawler.economic_data_crawler import EconomicDataCrawler

class MarketRegime(Enum):
    """Market regimes for strategy adaptation"""
    TRENDING_BULL = "trending_bull"
    TRENDING_BEAR = "trending_bear"
    RANGING = "ranging"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    NEWS_DRIVEN = "news_driven"
    SENTIMENT_DRIVEN = "sentiment_driven"

class StrategyType(Enum):
    """Available strategy types"""
    MOMENTUM = "momentum"
    MEAN_REVERSION = "mean_reversion"
    TREND_FOLLOWING = "trend_following"
    SCALPING = "scalping"
    NEWS_TRADING = "news_trading"
    SENTIMENT_TRADING = "sentiment_trading"
    ML_PREDICTION = "ml_prediction"
    HYBRID = "hybrid"

@dataclass
class TradingSignal:
    """Trading signal with comprehensive data"""
    symbol: str
    action: str  # "BUY", "SELL", "CLOSE", "HOLD"
    strength: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    strategy: str
    entry_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    reason: str
    timestamp: datetime
    risk_score: float  # 0.0 to 1.0
    expected_holding_time: Optional[int]  # minutes
    sentiment_score: Optional[float]
    news_impact: Optional[float]
    technical_score: Optional[float]
    ml_prediction: Optional[Dict]

class StrategyManager:
    """Advanced strategy manager with AI-driven adaptation"""
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.market_predictor = MarketPredictor(config)
        self.market_crawler = MarketDataCrawler(config)
        self.news_crawler = NewsSentimentCrawler(config)
        self.social_crawler = SocialSentimentCrawler(config)
        self.economic_crawler = EconomicDataCrawler(config)
        
        # Strategy configuration
        self.trading_symbols = getattr(config, 'trading_symbols', ['BTCUSDT', 'ETHUSDT'])
        self.active_strategies = {
            StrategyType.MOMENTUM: True,
            StrategyType.MEAN_REVERSION: True,
            StrategyType.TREND_FOLLOWING: True,
            StrategyType.ML_PREDICTION: True,
            StrategyType.SENTIMENT_TRADING: True,
            StrategyType.NEWS_TRADING: True
        }
        
        # Market state
        self.current_regime = MarketRegime.RANGING
        self.market_data_cache = {}
        self.sentiment_data_cache = {}
        self.news_data_cache = {}
        self.economic_data_cache = {}
        self.ml_predictions_cache = {}
        
        # Strategy weights based on performance
        self.strategy_weights = {
            StrategyType.MOMENTUM: 0.15,
            StrategyType.MEAN_REVERSION: 0.15,
            StrategyType.TREND_FOLLOWING: 0.15,
            StrategyType.ML_PREDICTION: 0.20,
            StrategyType.SENTIMENT_TRADING: 0.15,
            StrategyType.NEWS_TRADING: 0.10,
            StrategyType.HYBRID: 0.10
        }
        
        # Performance tracking
        self.strategy_performance = {strategy: {"wins": 0, "losses": 0, "total_pnl": 0.0} 
                                   for strategy in StrategyType}
        
        # Risk parameters
        self.max_positions_per_symbol = 1
        self.max_total_positions = 5
        self.risk_per_trade = 0.02  # 2% risk per trade
        
        self.logger.info("Strategy Manager initialized with advanced AI integration")
    
    async def generate_signals(self, symbols: List[str]) -> List[TradingSignal]:
        """Generate comprehensive trading signals for multiple symbols"""
        try:
            signals = []
            
            # Update all data sources
            await self._update_all_data_sources(symbols)
            
            # Detect market regime
            await self._detect_market_regime()
            
            for symbol in symbols:
                try:
                    # Generate signals from each strategy
                    symbol_signals = await self._generate_symbol_signals(symbol)
                    signals.extend(symbol_signals)
                    
                except Exception as e:
                    self.logger.error(f"Error generating signals for {symbol}: {e}")
            
            # Filter and rank signals
            filtered_signals = await self._filter_and_rank_signals(signals)
            
            self.logger.info(f"Generated {len(filtered_signals)} high-quality signals")
            return filtered_signals
            
        except Exception as e:
            self.logger.error(f"Error generating signals: {e}")
            return []
    
    async def _update_all_data_sources(self, symbols: List[str]):
        """Update all data sources in parallel"""
        try:
            tasks = []
            
            # Market data
            for symbol in symbols:
                tasks.append(self._update_market_data(symbol))
            
            # Sentiment and news data
            tasks.append(self._update_sentiment_data())
            tasks.append(self._update_news_data())
            tasks.append(self._update_economic_data())
            
            # ML predictions
            for symbol in symbols:
                tasks.append(self._update_ml_predictions(symbol))
            
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            self.logger.error(f"Error updating data sources: {e}")
    
    async def _update_market_data(self, symbol: str):
        """Update market data for a symbol"""
        try:
            # Get comprehensive market data
            data = await self.market_crawler.get_comprehensive_market_data(symbol)
            self.market_data_cache[symbol] = data
            
        except Exception as e:
            self.logger.error(f"Error updating market data for {symbol}: {e}")
    
    async def _update_sentiment_data(self):
        """Update social sentiment data"""
        try:
            sentiment_data = await self.social_crawler.get_comprehensive_sentiment()
            self.sentiment_data_cache = sentiment_data
            
        except Exception as e:
            self.logger.error(f"Error updating sentiment data: {e}")
    
    async def _update_news_data(self):
        """Update news sentiment data"""
        try:
            news_data = await self.news_crawler.get_comprehensive_news_analysis()
            self.news_data_cache = news_data
            
        except Exception as e:
            self.logger.error(f"Error updating news data: {e}")
    
    async def _update_economic_data(self):
        """Update economic data"""
        try:
            economic_data = await self.economic_crawler.get_comprehensive_economic_data()
            self.economic_data_cache = economic_data
            
        except Exception as e:
            self.logger.error(f"Error updating economic data: {e}")
    
    async def _update_ml_predictions(self, symbol: str):
        """Update ML predictions for a symbol"""
        try:
            if symbol in self.market_data_cache:
                predictions = await self.market_predictor.predict_price_movement(
                    symbol, self.market_data_cache[symbol]
                )
                self.ml_predictions_cache[symbol] = predictions
                
        except Exception as e:
            self.logger.error(f"Error updating ML predictions for {symbol}: {e}")
    
    async def _detect_market_regime(self):
        """Detect current market regime based on all available data"""
        try:
            # Analyze volatility
            volatility_scores = []
            trend_scores = []
            
            for symbol in self.trading_symbols:
                if symbol in self.market_data_cache:
                    data = self.market_data_cache[symbol]
                    
                    # Calculate volatility
                    if 'volatility' in data:
                        volatility_scores.append(data['volatility'])
                    
                    # Calculate trend strength
                    if 'technical_indicators' in data:
                        indicators = data['technical_indicators']
                        if 'trend_strength' in indicators:
                            trend_scores.append(indicators['trend_strength'])
            
            avg_volatility = np.mean(volatility_scores) if volatility_scores else 0.5
            avg_trend = np.mean(trend_scores) if trend_scores else 0.5
            
            # Check news impact
            news_impact = 0.0
            if 'overall_sentiment' in self.news_data_cache:
                news_impact = abs(self.news_data_cache['overall_sentiment'] - 0.5) * 2
            
            # Check social sentiment
            sentiment_impact = 0.0
            if 'overall_sentiment' in self.sentiment_data_cache:
                sentiment_impact = abs(self.sentiment_data_cache['overall_sentiment'] - 0.5) * 2
            
            # Determine regime
            if news_impact > 0.7:
                self.current_regime = MarketRegime.NEWS_DRIVEN
            elif sentiment_impact > 0.7:
                self.current_regime = MarketRegime.SENTIMENT_DRIVEN
            elif avg_volatility > 0.7:
                self.current_regime = MarketRegime.HIGH_VOLATILITY
            elif avg_volatility < 0.3:
                self.current_regime = MarketRegime.LOW_VOLATILITY
            elif avg_trend > 0.6:
                self.current_regime = MarketRegime.TRENDING_BULL if avg_trend > 0 else MarketRegime.TRENDING_BEAR
            else:
                self.current_regime = MarketRegime.RANGING
            
            self.logger.debug(f"Market regime detected: {self.current_regime.value}")
            
        except Exception as e:
            self.logger.error(f"Error detecting market regime: {e}")
    
    async def _generate_symbol_signals(self, symbol: str) -> List[TradingSignal]:
        """Generate signals for a specific symbol using all strategies"""
        signals = []
        
        try:
            # Get cached data
            market_data = self.market_data_cache.get(symbol, {})
            ml_predictions = self.ml_predictions_cache.get(symbol, {})
            
            # Generate signals from each active strategy
            if self.active_strategies.get(StrategyType.MOMENTUM):
                momentum_signal = await self._generate_momentum_signal(symbol, market_data)
                if momentum_signal:
                    signals.append(momentum_signal)
            
            if self.active_strategies.get(StrategyType.MEAN_REVERSION):
                reversion_signal = await self._generate_mean_reversion_signal(symbol, market_data)
                if reversion_signal:
                    signals.append(reversion_signal)
            
            if self.active_strategies.get(StrategyType.TREND_FOLLOWING):
                trend_signal = await self._generate_trend_following_signal(symbol, market_data)
                if trend_signal:
                    signals.append(trend_signal)
            
            if self.active_strategies.get(StrategyType.ML_PREDICTION):
                ml_signal = await self._generate_ml_signal(symbol, ml_predictions)
                if ml_signal:
                    signals.append(ml_signal)
            
            if self.active_strategies.get(StrategyType.SENTIMENT_TRADING):
                sentiment_signal = await self._generate_sentiment_signal(symbol)
                if sentiment_signal:
                    signals.append(sentiment_signal)
            
            if self.active_strategies.get(StrategyType.NEWS_TRADING):
                news_signal = await self._generate_news_signal(symbol)
                if news_signal:
                    signals.append(news_signal)
            
            # Generate hybrid signal combining multiple strategies
            hybrid_signal = await self._generate_hybrid_signal(symbol, signals)
            if hybrid_signal:
                signals.append(hybrid_signal)
            
        except Exception as e:
            self.logger.error(f"Error generating signals for {symbol}: {e}")
        
        return signals
    
    async def _generate_momentum_signal(self, symbol: str, market_data: Dict) -> Optional[TradingSignal]:
        """Generate momentum-based trading signal"""
        try:
            if not market_data or 'technical_indicators' not in market_data:
                return None
            
            indicators = market_data['technical_indicators']
            current_price = market_data.get('current_price', 0)
            
            # Momentum indicators
            rsi = indicators.get('rsi', 50)
            macd = indicators.get('macd', {})
            macd_signal = macd.get('signal', 0) if isinstance(macd, dict) else 0
            
            # Generate signal
            signal_strength = 0.0
            action = "HOLD"
            
            # RSI momentum
            if rsi > 70:
                signal_strength += 0.3  # Overbought momentum
                action = "SELL"
            elif rsi < 30:
                signal_strength += 0.3  # Oversold momentum
                action = "BUY"
            
            # MACD momentum
            if macd_signal > 0:
                signal_strength += 0.4
                action = "BUY" if action != "SELL" else action
            elif macd_signal < 0:
                signal_strength += 0.4
                action = "SELL" if action != "BUY" else action
            
            # Volume confirmation
            volume_ratio = market_data.get('volume_ratio', 1.0)
            if volume_ratio > 1.5:  # High volume confirmation
                signal_strength += 0.3
            
            if signal_strength > 0.6 and action != "HOLD":
                confidence = min(signal_strength, 1.0)
                
                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=signal_strength,
                    confidence=confidence,
                    strategy="momentum",
                    entry_price=current_price,
                    stop_loss=None,  # Will be calculated by risk manager
                    take_profit=None,  # Will be calculated by risk manager
                    reason=f"Momentum signal: RSI={rsi:.1f}, MACD={macd_signal:.3f}",
                    timestamp=datetime.utcnow(),
                    risk_score=1.0 - confidence,
                    expected_holding_time=30,  # 30 minutes for momentum
                    sentiment_score=None,
                    news_impact=None,
                    technical_score=signal_strength,
                    ml_prediction=None
                )
            
        except Exception as e:
            self.logger.error(f"Error generating momentum signal for {symbol}: {e}")
        
        return None
    
    async def _generate_mean_reversion_signal(self, symbol: str, market_data: Dict) -> Optional[TradingSignal]:
        """Generate mean reversion trading signal"""
        try:
            if not market_data or 'technical_indicators' not in market_data:
                return None
            
            indicators = market_data['technical_indicators']
            current_price = market_data.get('current_price', 0)
            
            # Mean reversion indicators
            bb_upper = indicators.get('bollinger_upper', 0)
            bb_lower = indicators.get('bollinger_lower', 0)
            bb_middle = indicators.get('bollinger_middle', 0)
            rsi = indicators.get('rsi', 50)
            
            if not all([bb_upper, bb_lower, bb_middle]):
                return None
            
            signal_strength = 0.0
            action = "HOLD"
            
            # Bollinger Bands mean reversion
            if current_price > bb_upper:
                signal_strength += 0.5  # Price above upper band - sell
                action = "SELL"
            elif current_price < bb_lower:
                signal_strength += 0.5  # Price below lower band - buy
                action = "BUY"
            
            # RSI confirmation
            if action == "BUY" and rsi < 35:
                signal_strength += 0.3
            elif action == "SELL" and rsi > 65:
                signal_strength += 0.3
            
            # Additional confirmation from price distance
            price_distance = abs(current_price - bb_middle) / bb_middle
            if price_distance > 0.02:  # 2% from middle
                signal_strength += 0.2
            
            if signal_strength > 0.7 and action != "HOLD":
                confidence = min(signal_strength, 1.0)
                
                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=signal_strength,
                    confidence=confidence,
                    strategy="mean_reversion",
                    entry_price=current_price,
                    stop_loss=None,
                    take_profit=bb_middle,  # Target middle band
                    reason=f"Mean reversion: Price={current_price:.2f}, BB_Middle={bb_middle:.2f}",
                    timestamp=datetime.utcnow(),
                    risk_score=1.0 - confidence,
                    expected_holding_time=60,  # 1 hour for mean reversion
                    sentiment_score=None,
                    news_impact=None,
                    technical_score=signal_strength,
                    ml_prediction=None
                )
            
        except Exception as e:
            self.logger.error(f"Error generating mean reversion signal for {symbol}: {e}")
        
        return None
    
    async def _generate_trend_following_signal(self, symbol: str, market_data: Dict) -> Optional[TradingSignal]:
        """Generate trend following trading signal"""
        try:
            if not market_data or 'technical_indicators' not in market_data:
                return None
            
            indicators = market_data['technical_indicators']
            current_price = market_data.get('current_price', 0)
            
            # Trend indicators
            ema_20 = indicators.get('ema_20', 0)
            ema_50 = indicators.get('ema_50', 0)
            trend_strength = indicators.get('trend_strength', 0)
            
            signal_strength = 0.0
            action = "HOLD"
            
            # EMA crossover
            if ema_20 > ema_50 and current_price > ema_20:
                signal_strength += 0.4  # Uptrend
                action = "BUY"
            elif ema_20 < ema_50 and current_price < ema_20:
                signal_strength += 0.4  # Downtrend
                action = "SELL"
            
            # Trend strength confirmation
            if abs(trend_strength) > 0.6:
                signal_strength += 0.3
                if trend_strength > 0 and action != "SELL":
                    action = "BUY"
                elif trend_strength < 0 and action != "BUY":
                    action = "SELL"
            
            # Volume confirmation
            volume_ratio = market_data.get('volume_ratio', 1.0)
            if volume_ratio > 1.2:
                signal_strength += 0.3
            
            if signal_strength > 0.6 and action != "HOLD":
                confidence = min(signal_strength, 1.0)
                
                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=signal_strength,
                    confidence=confidence,
                    strategy="trend_following",
                    entry_price=current_price,
                    stop_loss=None,
                    take_profit=None,
                    reason=f"Trend following: EMA20={ema_20:.2f}, EMA50={ema_50:.2f}, Strength={trend_strength:.2f}",
                    timestamp=datetime.utcnow(),
                    risk_score=1.0 - confidence,
                    expected_holding_time=120,  # 2 hours for trend following
                    sentiment_score=None,
                    news_impact=None,
                    technical_score=signal_strength,
                    ml_prediction=None
                )
            
        except Exception as e:
            self.logger.error(f"Error generating trend following signal for {symbol}: {e}")
        
        return None
    
    async def _generate_ml_signal(self, symbol: str, ml_predictions: Dict) -> Optional[TradingSignal]:
        """Generate ML-based trading signal"""
        try:
            if not ml_predictions:
                return None
            
            # Extract ML predictions
            price_prediction = ml_predictions.get('price_prediction', {})
            direction_prob = ml_predictions.get('direction_probability', {})
            confidence = ml_predictions.get('confidence', 0.5)
            
            if not price_prediction or not direction_prob:
                return None
            
            # Determine signal based on ML predictions
            up_prob = direction_prob.get('up', 0.5)
            down_prob = direction_prob.get('down', 0.5)
            
            signal_strength = max(up_prob, down_prob)
            action = "BUY" if up_prob > down_prob else "SELL"
            
            # Only generate signal if confidence is high enough
            if signal_strength > 0.65 and confidence > 0.6:
                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=signal_strength,
                    confidence=confidence,
                    strategy="ml_prediction",
                    entry_price=price_prediction.get('current_price', 0),
                    stop_loss=None,
                    take_profit=price_prediction.get('target_price'),
                    reason=f"ML prediction: {action} probability={signal_strength:.2f}, confidence={confidence:.2f}",
                    timestamp=datetime.utcnow(),
                    risk_score=1.0 - confidence,
                    expected_holding_time=ml_predictions.get('holding_time', 60),
                    sentiment_score=None,
                    news_impact=None,
                    technical_score=None,
                    ml_prediction=ml_predictions
                )
            
        except Exception as e:
            self.logger.error(f"Error generating ML signal for {symbol}: {e}")
        
        return None
    
    async def _generate_sentiment_signal(self, symbol: str) -> Optional[TradingSignal]:
        """Generate sentiment-based trading signal"""
        try:
            if not self.sentiment_data_cache:
                return None
            
            # Get sentiment scores
            overall_sentiment = self.sentiment_data_cache.get('overall_sentiment', 0.5)
            sentiment_strength = self.sentiment_data_cache.get('sentiment_strength', 0.0)
            trend_change = self.sentiment_data_cache.get('trend_change', 0.0)
            
            # Generate signal based on sentiment
            signal_strength = sentiment_strength
            action = "HOLD"
            
            if overall_sentiment > 0.7 and sentiment_strength > 0.6:
                action = "BUY"
            elif overall_sentiment < 0.3 and sentiment_strength > 0.6:
                action = "SELL"
            
            # Boost signal if sentiment is changing rapidly
            if abs(trend_change) > 0.3:
                signal_strength += 0.2
            
            if signal_strength > 0.6 and action != "HOLD":
                confidence = min(signal_strength, 1.0)
                
                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=signal_strength,
                    confidence=confidence,
                    strategy="sentiment_trading",
                    entry_price=None,
                    stop_loss=None,
                    take_profit=None,
                    reason=f"Sentiment: {overall_sentiment:.2f}, strength={sentiment_strength:.2f}",
                    timestamp=datetime.utcnow(),
                    risk_score=1.0 - confidence,
                    expected_holding_time=45,
                    sentiment_score=overall_sentiment,
                    news_impact=None,
                    technical_score=None,
                    ml_prediction=None
                )
            
        except Exception as e:
            self.logger.error(f"Error generating sentiment signal for {symbol}: {e}")
        
        return None
    
    async def _generate_news_signal(self, symbol: str) -> Optional[TradingSignal]:
        """Generate news-based trading signal"""
        try:
            if not self.news_data_cache:
                return None
            
            # Get news impact scores
            overall_sentiment = self.news_data_cache.get('overall_sentiment', 0.5)
            news_impact = self.news_data_cache.get('news_impact', 0.0)
            breaking_news = self.news_data_cache.get('breaking_news_detected', False)
            
            signal_strength = news_impact
            action = "HOLD"
            
            # Strong news sentiment
            if overall_sentiment > 0.75 and news_impact > 0.7:
                action = "BUY"
            elif overall_sentiment < 0.25 and news_impact > 0.7:
                action = "SELL"
            
            # Breaking news boost
            if breaking_news:
                signal_strength += 0.3
            
            if signal_strength > 0.7 and action != "HOLD":
                confidence = min(signal_strength, 1.0)
                
                return TradingSignal(
                    symbol=symbol,
                    action=action,
                    strength=signal_strength,
                    confidence=confidence,
                    strategy="news_trading",
                    entry_price=None,
                    stop_loss=None,
                    take_profit=None,
                    reason=f"News impact: sentiment={overall_sentiment:.2f}, impact={news_impact:.2f}",
                    timestamp=datetime.utcnow(),
                    risk_score=1.0 - confidence,
                    expected_holding_time=30,  # Quick news reaction
                    sentiment_score=None,
                    news_impact=news_impact,
                    technical_score=None,
                    ml_prediction=None
                )
            
        except Exception as e:
            self.logger.error(f"Error generating news signal for {symbol}: {e}")
        
        return None
    
    async def _generate_hybrid_signal(self, symbol: str, individual_signals: List[TradingSignal]) -> Optional[TradingSignal]:
        """Generate hybrid signal combining multiple strategies"""
        try:
            if len(individual_signals) < 2:
                return None
            
            # Aggregate signals by action
            buy_signals = [s for s in individual_signals if s.action == "BUY"]
            sell_signals = [s for s in individual_signals if s.action == "SELL"]
            
            # Calculate weighted scores
            buy_score = sum(s.strength * self.strategy_weights.get(StrategyType(s.strategy), 0.1) 
                           for s in buy_signals)
            sell_score = sum(s.strength * self.strategy_weights.get(StrategyType(s.strategy), 0.1) 
                            for s in sell_signals)
            
            # Determine hybrid action
            if buy_score > sell_score and buy_score > 0.7:
                action = "BUY"
                signal_strength = buy_score
                best_signals = buy_signals
            elif sell_score > buy_score and sell_score > 0.7:
                action = "SELL"
                signal_strength = sell_score
                best_signals = sell_signals
            else:
                return None
            
            # Calculate composite confidence
            confidence = sum(s.confidence for s in best_signals) / len(best_signals) if best_signals else 0.5
            
            # Combine reasons
            reasons = [f"{s.strategy}({s.strength:.2f})" for s in best_signals]
            
            return TradingSignal(
                symbol=symbol,
                action=action,
                strength=min(signal_strength, 1.0),
                confidence=confidence,
                strategy="hybrid",
                entry_price=None,
                stop_loss=None,
                take_profit=None,
                reason=f"Hybrid signal: {', '.join(reasons)}",
                timestamp=datetime.utcnow(),
                risk_score=1.0 - confidence,
                expected_holding_time=sum(s.expected_holding_time or 60 for s in best_signals) / len(best_signals) if best_signals else 60,
                sentiment_score=sum(s.sentiment_score for s in best_signals if s.sentiment_score) / max(1, len([s for s in best_signals if s.sentiment_score])),
                news_impact=sum(s.news_impact for s in best_signals if s.news_impact) / max(1, len([s for s in best_signals if s.news_impact])),
                technical_score=sum(s.technical_score for s in best_signals if s.technical_score) / max(1, len([s for s in best_signals if s.technical_score])),
                ml_prediction=None
            )
            
        except Exception as e:
            self.logger.error(f"Error generating hybrid signal for {symbol}: {e}")
        
        return None
    
    async def _filter_and_rank_signals(self, signals: List[TradingSignal]) -> List[TradingSignal]:
        """Filter and rank signals based on quality and market conditions"""
        try:
            if not signals:
                return []
            
            # Filter by minimum confidence and strength
            min_confidence = 0.6
            min_strength = 0.6
            
            filtered_signals = [
                s for s in signals 
                if s.confidence >= min_confidence and s.strength >= min_strength
            ]
            
            # Adjust for market regime
            for signal in filtered_signals:
                signal.strength = await self._adjust_for_market_regime(signal)
            
            # Sort by composite score
            def signal_score(signal):
                regime_bonus = 0.1 if self._signal_matches_regime(signal) else 0
                strategy_weight = self.strategy_weights.get(StrategyType(signal.strategy), 0.1)
                return (signal.strength * signal.confidence * strategy_weight) + regime_bonus
            
            filtered_signals.sort(key=signal_score, reverse=True)
            
            # Limit to top signals per symbol
            symbol_counts = {}
            final_signals = []
            
            for signal in filtered_signals:
                symbol_count = symbol_counts.get(signal.symbol, 0)
                if symbol_count < self.max_positions_per_symbol:
                    final_signals.append(signal)
                    symbol_counts[signal.symbol] = symbol_count + 1
                
                if len(final_signals) >= self.max_total_positions:
                    break
            
            return final_signals
            
        except Exception as e:
            self.logger.error(f"Error filtering signals: {e}")
            return signals[:self.max_total_positions]  # Fallback
    
    async def _adjust_for_market_regime(self, signal: TradingSignal) -> float:
        """Adjust signal strength based on current market regime"""
        try:
            adjustment = 1.0
            
            # Regime-based adjustments
            if self.current_regime == MarketRegime.TRENDING_BULL:
                if signal.action == "BUY":
                    adjustment = 1.2
                elif signal.action == "SELL":
                    adjustment = 0.8
                    
            elif self.current_regime == MarketRegime.TRENDING_BEAR:
                if signal.action == "SELL":
                    adjustment = 1.2
                elif signal.action == "BUY":
                    adjustment = 0.8
                    
            elif self.current_regime == MarketRegime.RANGING:
                if signal.strategy == "mean_reversion":
                    adjustment = 1.3
                elif signal.strategy == "trend_following":
                    adjustment = 0.7
                    
            elif self.current_regime == MarketRegime.HIGH_VOLATILITY:
                if signal.strategy in ["momentum", "news_trading"]:
                    adjustment = 1.2
                else:
                    adjustment = 0.9
                    
            elif self.current_regime == MarketRegime.NEWS_DRIVEN:
                if signal.strategy == "news_trading":
                    adjustment = 1.4
                    
            elif self.current_regime == MarketRegime.SENTIMENT_DRIVEN:
                if signal.strategy == "sentiment_trading":
                    adjustment = 1.4
            
            return min(signal.strength * adjustment, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error adjusting signal for regime: {e}")
            return signal.strength
    
    def _signal_matches_regime(self, signal: TradingSignal) -> bool:
        """Check if signal matches current market regime"""
        try:
            if self.current_regime == MarketRegime.NEWS_DRIVEN:
                return signal.strategy == "news_trading"
            elif self.current_regime == MarketRegime.SENTIMENT_DRIVEN:
                return signal.strategy == "sentiment_trading"
            elif self.current_regime == MarketRegime.HIGH_VOLATILITY:
                return signal.strategy in ["momentum", "news_trading"]
            elif self.current_regime == MarketRegime.RANGING:
                return signal.strategy == "mean_reversion"
            elif self.current_regime in [MarketRegime.TRENDING_BULL, MarketRegime.TRENDING_BEAR]:
                return signal.strategy == "trend_following"
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking regime match: {e}")
            return False
    
    async def update_strategy_performance(self, strategy: str, trade_result: Dict):
        """Update strategy performance metrics"""
        try:
            if strategy in [s.value for s in StrategyType]:
                strategy_type = StrategyType(strategy)
                
                if trade_result.get('pnl', 0) > 0:
                    self.strategy_performance[strategy_type]['wins'] += 1
                else:
                    self.strategy_performance[strategy_type]['losses'] += 1
                
                self.strategy_performance[strategy_type]['total_pnl'] += trade_result.get('pnl', 0)
                
                # Adjust strategy weights based on performance
                await self._adjust_strategy_weights()
            
        except Exception as e:
            self.logger.error(f"Error updating strategy performance: {e}")
    
    async def _adjust_strategy_weights(self):
        """Dynamically adjust strategy weights based on performance"""
        try:
            total_weight = 0.0
            
            for strategy_type, performance in self.strategy_performance.items():
                total_trades = performance['wins'] + performance['losses']
                
                if total_trades > 10:  # Minimum trades for adjustment
                    win_rate = performance['wins'] / total_trades
                    avg_pnl = performance['total_pnl'] / total_trades
                    
                    # Calculate performance score
                    performance_score = (win_rate * 0.6) + (max(avg_pnl, 0) * 0.4)
                    
                    # Adjust weight
                    base_weight = 1.0 / len(StrategyType)
                    new_weight = base_weight * (0.5 + performance_score)
                    
                    self.strategy_weights[strategy_type] = new_weight
                    total_weight += new_weight
            
            # Normalize weights
            if total_weight > 0:
                for strategy_type in self.strategy_weights:
                    self.strategy_weights[strategy_type] /= total_weight
            
        except Exception as e:
            self.logger.error(f"Error adjusting strategy weights: {e}")
    
    def get_strategy_status(self) -> Dict[str, Any]:
        """Get current strategy manager status"""
        return {
            "current_regime": self.current_regime.value,
            "active_strategies": {k.value: v for k, v in self.active_strategies.items()},
            "strategy_weights": {k.value: v for k, v in self.strategy_weights.items()},
            "strategy_performance": {
                k.value: v for k, v in self.strategy_performance.items()
            },
            "data_cache_status": {
                "market_data": len(self.market_data_cache),
                "sentiment_data": bool(self.sentiment_data_cache),
                "news_data": bool(self.news_data_cache),
                "economic_data": bool(self.economic_data_cache),
                "ml_predictions": len(self.ml_predictions_cache)
            }
        }
