"""
Performance Analysis Module for Bybit Trading Bot
Tracks performance metrics and enables adaptive learning
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import statistics
import numpy as np
from collections import defaultdict

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger
from bybit_bot.database.connection import DatabaseManager

logger = logging.getLogger("bybit_trading_bot.performance_analyzer")


class PerformanceAnalyzer:
    """
    Comprehensive performance analysis and adaptive learning system
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db = database_manager
        self.logger = TradingBotLogger(config)
        
        # Performance tracking
        self.performance_metrics = {}
        self.strategy_performance = defaultdict(dict)
        self.symbol_performance = defaultdict(dict)
        
        # Adaptive learning parameters
        self.learning_window = 100  # Number of trades to analyze
        self.adaptation_threshold = 0.05  # 5% performance change threshold
        
        # Performance history
        self.daily_performance = []
        self.weekly_performance = []
        self.monthly_performance = []
        
        # Real-time metrics
        self.current_metrics = {
            "total_pnl": 0.0,
            "win_rate": 0.0,
            "profit_factor": 0.0,
            "sharpe_ratio": 0.0,
            "max_drawdown": 0.0,
            "average_trade_duration": 0.0,
            "trades_today": 0,
            "best_strategy": None,
            "worst_strategy": None
        }
        
    async def initialize(self):
        """Initialize performance analyzer"""
        try:
            self.logger.info("📊 Initializing Performance Analyzer...")
            
            # Load historical performance data
            await self._load_historical_performance()
            
            # Calculate current metrics
            await self._calculate_current_metrics()
            
            # Initialize strategy performance tracking
            await self._initialize_strategy_tracking()
            
            self.logger.info("✅ Performance Analyzer initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Performance Analyzer: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown performance analyzer"""
        try:
            # Save final performance metrics
            await self._save_performance_metrics()
            self.logger.info("✅ Performance Analyzer shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error shutting down Performance Analyzer: {e}")
    
    async def _load_historical_performance(self):
        """Load historical performance data from database"""
        try:
            # Load last 30 days of performance
            start_date = datetime.utcnow() - timedelta(days=30)
            performance_data = await self.db.get_performance_data(
                start_date=start_date,
                end_date=datetime.utcnow()
            )
            
            if performance_data:
                # Process daily performance
                daily_data = {}
                for perf in performance_data:
                    date_key = perf.date.date()
                    if date_key not in daily_data:
                        daily_data[date_key] = {
                            "total_pnl": 0.0,
                            "trades": 0,
                            "wins": 0,
                            "losses": 0
                        }
                    
                    daily_data[date_key]["total_pnl"] += perf.total_pnl or 0.0
                    daily_data[date_key]["trades"] += 1
                    
                    if perf.total_pnl and perf.total_pnl > 0:
                        daily_data[date_key]["wins"] += 1
                    else:
                        daily_data[date_key]["losses"] += 1
                
                self.daily_performance = list(daily_data.values())
                
                self.logger.info(f"📈 Loaded {len(self.daily_performance)} days of performance data")
            
        except Exception as e:
            self.logger.error(f"Error loading historical performance: {e}")
    
    async def _calculate_current_metrics(self):
        """Calculate current performance metrics"""
        try:
            # Get all trades
            all_trades = await self.db.get_trades()
            
            if not all_trades:
                self.logger.info("📊 No trades found for performance calculation")
                return
            
            # Calculate basic metrics
            total_trades = len(all_trades)
            profitable_trades = [t for t in all_trades if t.profit_loss and t.profit_loss > 0]
            losing_trades = [t for t in all_trades if t.profit_loss and t.profit_loss <= 0]
            
            # Total P&L
            self.current_metrics["total_pnl"] = sum(t.profit_loss for t in all_trades if t.profit_loss)
            
            # Win rate
            if total_trades > 0:
                self.current_metrics["win_rate"] = len(profitable_trades) / total_trades
            
            # Profit factor
            total_profits = sum(t.profit_loss for t in profitable_trades)
            total_losses = abs(sum(t.profit_loss for t in losing_trades))
            
            if total_losses > 0:
                self.current_metrics["profit_factor"] = total_profits / total_losses
            
            # Average trade duration
            trade_durations = []
            for trade in all_trades:
                if trade.exit_time and trade.entry_time:
                    duration = (trade.exit_time - trade.entry_time).total_seconds() / 3600  # Hours
                    trade_durations.append(duration)
            
            if trade_durations:
                self.current_metrics["average_trade_duration"] = statistics.mean(trade_durations)
            
            # Trades today
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            today_trades = [t for t in all_trades if t.entry_time >= today_start]
            self.current_metrics["trades_today"] = len(today_trades)
            
            # Calculate Sharpe ratio
            await self._calculate_sharpe_ratio()
            
            # Calculate max drawdown
            await self._calculate_max_drawdown()
            
            self.logger.info(f"📊 Current metrics: P&L: ${self.current_metrics['total_pnl']:.2f}, "
                           f"Win Rate: {self.current_metrics['win_rate']:.2%}, "
                           f"Profit Factor: {self.current_metrics['profit_factor']:.2f}")
            
        except Exception as e:
            self.logger.error(f"Error calculating current metrics: {e}")
    
    async def _calculate_sharpe_ratio(self):
        """Calculate Sharpe ratio for returns"""
        try:
            # Get daily returns
            if len(self.daily_performance) < 10:
                self.current_metrics["sharpe_ratio"] = 0.0
                return
            
            returns = [day["total_pnl"] for day in self.daily_performance]
            
            if not returns:
                self.current_metrics["sharpe_ratio"] = 0.0
                return
            
            # Calculate Sharpe ratio
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            
            if std_return > 0:
                # Annualized Sharpe ratio (assuming 252 trading days)
                self.current_metrics["sharpe_ratio"] = (mean_return / std_return) * np.sqrt(252)
            else:
                self.current_metrics["sharpe_ratio"] = 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating Sharpe ratio: {e}")
            self.current_metrics["sharpe_ratio"] = 0.0
    
    async def _calculate_max_drawdown(self):
        """Calculate maximum drawdown"""
        try:
            if not self.daily_performance:
                self.current_metrics["max_drawdown"] = 0.0
                return
            
            # Calculate cumulative returns
            cumulative_pnl = 0.0
            peak = 0.0
            max_drawdown = 0.0
            
            for day in self.daily_performance:
                cumulative_pnl += day["total_pnl"]
                
                if cumulative_pnl > peak:
                    peak = cumulative_pnl
                
                drawdown = (peak - cumulative_pnl) / peak if peak > 0 else 0.0
                max_drawdown = max(max_drawdown, drawdown)
            
            self.current_metrics["max_drawdown"] = max_drawdown
            
        except Exception as e:
            self.logger.error(f"Error calculating max drawdown: {e}")
            self.current_metrics["max_drawdown"] = 0.0
    
    async def _initialize_strategy_tracking(self):
        """Initialize strategy performance tracking"""
        try:
            # Get recent trades by strategy
            recent_trades = await self.db.get_trades(limit=self.learning_window)
            
            strategy_stats = defaultdict(lambda: {
                "trades": 0,
                "wins": 0,
                "total_pnl": 0.0,
                "avg_duration": 0.0,
                "win_rate": 0.0,
                "profit_factor": 0.0
            })
            
            for trade in recent_trades:
                strategy = trade.strategy_used or "unknown"
                stats = strategy_stats[strategy]
                
                stats["trades"] += 1
                stats["total_pnl"] += trade.profit_loss or 0.0
                
                if trade.profit_loss and trade.profit_loss > 0:
                    stats["wins"] += 1
                
                # Calculate duration
                if trade.exit_time and trade.entry_time:
                    duration = (trade.exit_time - trade.entry_time).total_seconds() / 3600
                    stats["avg_duration"] = ((stats["avg_duration"] * (stats["trades"] - 1)) + duration) / stats["trades"]
            
            # Calculate derived metrics
            for strategy, stats in strategy_stats.items():
                if stats["trades"] > 0:
                    stats["win_rate"] = stats["wins"] / stats["trades"]
                
                # Calculate profit factor
                profitable_trades = [t for t in recent_trades 
                                   if t.strategy_used == strategy and t.profit_loss and t.profit_loss > 0]
                losing_trades = [t for t in recent_trades 
                               if t.strategy_used == strategy and t.profit_loss and t.profit_loss <= 0]
                
                total_profits = sum(t.profit_loss for t in profitable_trades)
                total_losses = abs(sum(t.profit_loss for t in losing_trades))
                
                if total_losses > 0:
                    stats["profit_factor"] = total_profits / total_losses
                
                self.strategy_performance[strategy] = stats
            
            # Identify best and worst performing strategies
            if self.strategy_performance:
                best_strategy = max(self.strategy_performance.items(), 
                                  key=lambda x: x[1]["total_pnl"])
                worst_strategy = min(self.strategy_performance.items(), 
                                   key=lambda x: x[1]["total_pnl"])
                
                self.current_metrics["best_strategy"] = best_strategy[0]
                self.current_metrics["worst_strategy"] = worst_strategy[0]
            
            self.logger.info(f"📊 Strategy performance initialized for {len(self.strategy_performance)} strategies")
            
        except Exception as e:
            self.logger.error(f"Error initializing strategy tracking: {e}")
    
    async def analyze_trade_performance(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze individual trade performance and provide insights
        """
        try:
            analysis = {
                "trade_quality": "unknown",
                "profit_potential": 0.0,
                "risk_reward_ratio": 0.0,
                "duration_analysis": "normal",
                "strategy_effectiveness": 0.0,
                "recommendations": []
            }
            
            # Analyze profit/loss
            pnl = trade_data.get("profit_loss", 0.0)
            entry_price = trade_data.get("entry_price", 0.0)
            exit_price = trade_data.get("exit_price", 0.0)
            
            if pnl > 0:
                analysis["trade_quality"] = "profitable"
                if pnl > entry_price * 0.02:  # > 2% profit
                    analysis["trade_quality"] = "highly_profitable"
            else:
                analysis["trade_quality"] = "loss"
                if abs(pnl) > entry_price * 0.02:  # > 2% loss
                    analysis["trade_quality"] = "significant_loss"
            
            # Calculate risk-reward ratio
            if entry_price > 0 and exit_price > 0:
                price_change = abs(exit_price - entry_price)
                analysis["risk_reward_ratio"] = price_change / entry_price
            
            # Analyze duration
            entry_time = trade_data.get("entry_time")
            exit_time = trade_data.get("exit_time")
            
            if entry_time and exit_time:
                duration_hours = (exit_time - entry_time).total_seconds() / 3600
                
                if duration_hours < 1:
                    analysis["duration_analysis"] = "very_short"
                elif duration_hours < 4:
                    analysis["duration_analysis"] = "short"
                elif duration_hours > 24:
                    analysis["duration_analysis"] = "long"
                elif duration_hours > 72:
                    analysis["duration_analysis"] = "very_long"
            
            # Strategy effectiveness
            strategy = trade_data.get("strategy_used", "unknown")
            if strategy in self.strategy_performance:
                strategy_stats = self.strategy_performance[strategy]
                analysis["strategy_effectiveness"] = strategy_stats.get("win_rate", 0.0)
            
            # Generate recommendations
            recommendations = []
            
            if analysis["trade_quality"] == "significant_loss":
                recommendations.append("Review entry criteria - significant loss detected")
            
            if analysis["duration_analysis"] == "very_long":
                recommendations.append("Consider tighter time-based exit rules")
            
            if analysis["strategy_effectiveness"] < 0.4:  # < 40% win rate
                recommendations.append(f"Strategy '{strategy}' underperforming - consider adjustment")
            
            if analysis["risk_reward_ratio"] < 0.01:  # < 1% price movement
                recommendations.append("Consider wider price targets for better risk-reward")
            
            analysis["recommendations"] = recommendations
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing trade performance: {e}")
            return {"error": str(e)}
    
    async def get_strategy_recommendations(self) -> Dict[str, Any]:
        """
        Get adaptive strategy recommendations based on performance
        """
        try:
            recommendations = {
                "strategy_weights": {},
                "parameter_adjustments": {},
                "market_conditions": "unknown",
                "risk_adjustments": {},
                "focus_symbols": []
            }
            
            if not self.strategy_performance:
                return recommendations
            
            # Calculate dynamic strategy weights based on performance
            total_pnl = sum(stats["total_pnl"] for stats in self.strategy_performance.values())
            
            for strategy, stats in self.strategy_performance.items():
                if total_pnl != 0:
                    # Base weight on profit contribution and win rate
                    profit_weight = stats["total_pnl"] / total_pnl if total_pnl > 0 else 0.0
                    win_rate_weight = stats["win_rate"]
                    
                    # Combined weight with profit factor consideration
                    combined_weight = (profit_weight * 0.5 + win_rate_weight * 0.3 + 
                                     min(stats["profit_factor"] / 2.0, 1.0) * 0.2)
                    
                    recommendations["strategy_weights"][strategy] = max(0.1, min(0.6, combined_weight))
                else:
                    recommendations["strategy_weights"][strategy] = 1.0 / len(self.strategy_performance)
            
            # Normalize weights
            total_weight = sum(recommendations["strategy_weights"].values())
            if total_weight > 0:
                for strategy in recommendations["strategy_weights"]:
                    recommendations["strategy_weights"][strategy] /= total_weight
            
            # Parameter adjustments based on performance
            for strategy, stats in self.strategy_performance.items():
                adjustments = {}
                
                if stats["win_rate"] < 0.4:  # Low win rate
                    adjustments["entry_threshold"] = "increase"  # More selective entries
                    adjustments["position_size"] = "decrease"  # Smaller positions
                
                if stats["profit_factor"] < 1.0:  # Unprofitable
                    adjustments["stop_loss"] = "tighten"  # Tighter stops
                    adjustments["take_profit"] = "extend"  # Wider targets
                
                if stats["avg_duration"] > 24:  # Long holding periods
                    adjustments["time_exit"] = "implement"  # Time-based exits
                
                if adjustments:
                    recommendations["parameter_adjustments"][strategy] = adjustments
            
            # Market condition analysis
            recent_performance = self.daily_performance[-5:] if len(self.daily_performance) >= 5 else self.daily_performance
            
            if recent_performance:
                recent_pnl = [day["total_pnl"] for day in recent_performance]
                avg_recent_pnl = statistics.mean(recent_pnl)
                
                if avg_recent_pnl > 0:
                    recommendations["market_conditions"] = "favorable"
                elif avg_recent_pnl < 0:
                    recommendations["market_conditions"] = "challenging"
                else:
                    recommendations["market_conditions"] = "neutral"
            
            # Risk adjustments
            if self.current_metrics["max_drawdown"] > 0.15:  # > 15% drawdown
                recommendations["risk_adjustments"]["position_size"] = "reduce"
                recommendations["risk_adjustments"]["diversification"] = "increase"
            
            if self.current_metrics["win_rate"] < 0.35:  # < 35% win rate
                recommendations["risk_adjustments"]["entry_criteria"] = "strengthen"
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Error getting strategy recommendations: {e}")
            return {"error": str(e)}
    
    async def update_performance_metrics(self, trade_data: Dict[str, Any]):
        """
        Update performance metrics with new trade data
        """
        try:
            # Add trade to current metrics
            if trade_data.get("profit_loss"):
                self.current_metrics["total_pnl"] += trade_data["profit_loss"]
            
            self.current_metrics["trades_today"] += 1
            
            # Update strategy performance
            strategy = trade_data.get("strategy_used", "unknown")
            if strategy not in self.strategy_performance:
                self.strategy_performance[strategy] = {
                    "trades": 0,
                    "wins": 0,
                    "total_pnl": 0.0,
                    "win_rate": 0.0
                }
            
            stats = self.strategy_performance[strategy]
            stats["trades"] += 1
            stats["total_pnl"] += trade_data.get("profit_loss", 0.0)
            
            if trade_data.get("profit_loss", 0.0) > 0:
                stats["wins"] += 1
            
            stats["win_rate"] = stats["wins"] / stats["trades"]
            
            # Save performance data to database
            await self._save_performance_update(trade_data)
            
            self.logger.info(f"📊 Performance metrics updated for {strategy}")
            
        except Exception as e:
            self.logger.error(f"Error updating performance metrics: {e}")
    
    async def _save_performance_update(self, trade_data: Dict[str, Any]):
        """Save performance update to database"""
        try:
            performance_data = {
                "date": datetime.utcnow(),
                "strategy": trade_data.get("strategy_used", "unknown"),
                "symbol": trade_data.get("symbol", "unknown"),
                "total_pnl": trade_data.get("profit_loss", 0.0),
                "trade_count": 1,
                "win_count": 1 if trade_data.get("profit_loss", 0.0) > 0 else 0,
                "details": {
                    "current_metrics": self.current_metrics,
                    "trade_analysis": await self.analyze_trade_performance(trade_data)
                }
            }
            
            await self.db.save_performance_data(performance_data)
            
        except Exception as e:
            self.logger.error(f"Error saving performance update: {e}")
    
    async def _save_performance_metrics(self):
        """Save current performance metrics to database"""
        try:
            metrics_data = {
                "date": datetime.utcnow(),
                "strategy": "system_wide",
                "symbol": "ALL",
                "total_pnl": self.current_metrics["total_pnl"],
                "trade_count": self.current_metrics["trades_today"],
                "win_count": 0,  # Will be calculated from database
                "details": {
                    "all_metrics": self.current_metrics,
                    "strategy_performance": dict(self.strategy_performance),
                    "daily_performance": self.daily_performance[-7:] if self.daily_performance else []
                }
            }
            
            await self.db.save_performance_data(metrics_data)
            
        except Exception as e:
            self.logger.error(f"Error saving performance metrics: {e}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        try:
            return {
                "current_metrics": self.current_metrics,
                "strategy_performance": dict(self.strategy_performance),
                "daily_performance": self.daily_performance[-30:] if self.daily_performance else [],
                "performance_trends": {
                    "last_7_days": self.daily_performance[-7:] if len(self.daily_performance) >= 7 else [],
                    "last_30_days": self.daily_performance[-30:] if len(self.daily_performance) >= 30 else []
                },
                "summary_stats": {
                    "total_trading_days": len(self.daily_performance),
                    "profitable_days": len([d for d in self.daily_performance if d["total_pnl"] > 0]),
                    "average_daily_pnl": statistics.mean([d["total_pnl"] for d in self.daily_performance]) if self.daily_performance else 0.0,
                    "best_day": max(self.daily_performance, key=lambda x: x["total_pnl"]) if self.daily_performance else None,
                    "worst_day": min(self.daily_performance, key=lambda x: x["total_pnl"]) if self.daily_performance else None
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error getting performance summary: {e}")
            return {"error": str(e)}
