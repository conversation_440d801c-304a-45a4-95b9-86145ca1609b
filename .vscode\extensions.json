{"recommendations": ["ms-python.python", "ms-python.debugpy", "ms-python.pylint", "ms-python.flake8", "ms-python.black-formatter", "ms-toolsai.jupyter", "ms-toolsai.jupyter-keymap", "ms-toolsai.jupyter-renderers", "ms-vscode.vscode-json", "ms-vscode.vscode-yaml", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-typescript-next", "ms-vscode.vscode-eslint", "github.copilot", "github.copilot-chat", "github.copilot-labs", "ms-vscode.remote-containers", "ms-azuretools.vscode-docker", "ms-vscode.powershell", "ms-vscode-remote.remote-ssh", "ms-vscode-remote.remote-wsl", "redhat.vscode-yaml", "ms-vscode.cmake-tools", "ms-vscode.cpptools", "formulahendry.auto-rename-tag", "formulahendry.code-runner", "streetsidesoftware.code-spell-checker", "visualstudioexptteam.vscodeintellicode", "ms-vscode.vscode-git-lfs", "eamodio.gitlens", "donjayamanne.githistory", "github.vscode-pull-request-github", "ms-vscode.hexeditor", "ms-vscode.vscode-serial-monitor", "ms-vscode.vscode-embedded-tools", "ms-python.conda", "kevinrose.vsc-python-indent", "njpwerner.autodocstring", "ms-python.isort", "charliermarsh.ruff", "ms-python.mypy-type-checker"], "unwantedRecommendations": ["ms-python.python-legacy-debug"]}