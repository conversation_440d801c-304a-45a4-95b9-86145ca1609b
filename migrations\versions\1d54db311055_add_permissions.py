"""add permissions

Revision ID: 1d54db311055
Revises: 3356a2f89a33
Create Date: 2023-06-14 11:05:59.678961

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1d54db311055'
down_revision = '516ecc1c723d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('agent_execution_permissions',
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('agent_execution_id', sa.Integer(), nullable=True),
    sa.Column('agent_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('tool_name', sa.String(), nullable=True),
    sa.Column('user_feedback', sa.Text(), nullable=True),
    sa.Column('assistant_reply', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('agent_executions', sa.Column('permission_id', sa.Integer(), nullable=True))
    # index on agent_execution_id
    op.create_index(op.f('ix_agent_execution_permissions_agent_execution_id')
                    , 'agent_execution_permissions', ['agent_execution_id'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('agent_executions', 'permission_id')
    op.drop_table('agent_execution_permissions')
    # ### end Alembic commands ###
