{
  // ===== USER RULES: workspace, code, and agent behaviour =====
  // No partial fixes; clean workspace; live data only; main.py entry-point; etc.
  // ===== Python / Conda =====
  "python.defaultInterpreterPath": "E:\\Miniconda\\envs\\bybit-trader\\python.exe",
  "python.condaPath": "E:\\Miniconda\\Scripts\\conda.exe",
  "python.terminal.activateEnvironment": true,
  "python.terminal.activateEnvInCurrentTerminal": true,
  "python.venvPath": "E:\\Miniconda\\envs",
  "python.terminal.executeInFileDir": true,
  "python.terminal.launchArgs": [
    "-c",
    "import sys; print('Python Environment:', sys.executable); print('Available packages ready for autonomous trading')"
  ],
  "python.analysis.autoImportCompletions": true,
  "python.analysis.typeCheckingMode": "basic",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  // ===== Terminal: Node.js & npm =====
  "terminal.integrated.env.windows": {
    "PATH": "E:\\Program Files\\nodejs;E:\\npm-global;E:\\Miniconda\\Scripts;E:\\Miniconda\\envs\\bybit-trader\\Scripts;${env:PATH}",
    "CONDA_DEFAULT_ENV": "bybit-trader",
    "CONDA_PREFIX": "E:\\Miniconda\\envs\\bybit-trader"
  },
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  "terminal.integrated.profiles.windows": {
    "Bybit Trader Environment": {
      "path": "cmd.exe",
      "args": [
        "/k",
        "E:\\Miniconda\\Scripts\\activate.bat && conda activate bybit-trader && cd /d E:\\The_real_deal_copy\\Bybit_Bot\\BOT && echo Bybit Trader Environment Activated && echo Ready for autonomous profit generation && echo All SuperGPT systems online"
      ],
      "icon": "rocket",
      "color": "terminal.ansiGreen"
    },
    "Command Prompt": {
      "path": "cmd.exe",
      "args": [],
      "icon": "terminal-cmd"
    },
    "PowerShell": {
      "path": "powershell.exe",
      "args": [
        "-NoExit",
        "-Command",
        "& {conda activate bybit-trader; cd 'E:\\The_real_deal_copy\\Bybit_Bot\\BOT'; Write-Host 'Bybit Trader Environment Activated' -ForegroundColor Green; Write-Host 'Ready for autonomous profit generation' -ForegroundColor Cyan; Write-Host 'All SuperGPT systems online' -ForegroundColor Yellow}"
      ],
      "icon": "terminal-powershell"
    }
  },
  // ===== GitHub Copilot =====
  "github.copilot.enable": {
    "*": false
  },
  "github.copilot.chat.experimental.models": [
    "gpt-4",
    "gpt-4-turbo",
    "gpt-4o"
  ],
  "github.copilot.experimental.agents": true,
  "github.copilot.performance.optimization": "maximum",
  "github.copilot.response.timeout": 60000,
  "github.copilot.cache.enabled": true,
  "github.copilot.parallel.processing": true,
  "github.copilot.context.maximization": true,
  "github.copilot.chat.experimental.systemPrompt": [
    "You are an autonomous trading-bot builder. Follow these immutable rules:",
    "1. Work until all tasks are 100% successful; no partial fixes.",
    "2. Keep the workspace clean; create files in correct folders only.",
    "3. Improve efficiency without removing functionality or tests.",
    "4. Run representative tests that execute main.py.",
    "5. Double-check all syntax and imports before committing.",
    "6. Implement every function; no stubs left behind.",
    "7. Provide durable solutions, never temporary work-arounds.",
    "8. Do not duplicate files; if duplication is required, delete the original.",
    "9. Never use fake or hard-coded data; only live data.",
    "10. Operate continuously until the full task list is complete.",
    "11. Take logical next steps without requesting confirmation.",
    "12. Exploit exchange APIs to their full capabilities.",
    "13. main.py is the single entry point.",
    "14. Consult official API documentation where needed.",
    "15. Maximise profit and learn from trading/back-testing.",
    "16. Run everything on E:\\ in conda env bybit-trader.",
    "17. No fallbacks, no mock data, no shortcuts.",
    "18. Maintain a clean folder hierarchy; delete duplicates.",
    "19. Use PostgreSQL for persistent memory and storage.",
    "20. Implement AI-driven decision making with continuous learning.",
    "21. Execute autonomously without human intervention.",
    "22. Research and integrate best practices from open-source projects.",
    "23. NEVER EVER RUN ANYTHING SIMPLIFIED OR COMPROMISE ON THE FUNCTIONING OF THE SYSTEM.",
    "24. ALWAYS RUN THE FULL SYSTEM WITH ALL FEATURES ACTIVE AND FLAWLESS."
  ],
  // ===== MCP Servers =====
  "mcp.autoDiscovery": true,
  "mcp.timeout": 30000,
  "mcp.retry.attempts": 3,
  "mcp.retry.delay": 1000,
  "mcp.servers": {
    "bybit": {
      "type": "stdio",
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-p",
        "3333:3333",
        "ghcr.io/sammcj/bybit-mcp-server:latest"
      ],
      "timeout": 30000,
      "retry": {
        "attempts": 3,
        "delay": 1000
      }
    },
    "memory": {
      "type": "stdio",
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "--network",
        "host",
        "-e",
        "DATABASE_URL=postgresql://localhost:5432/trading_bot",
        "ghcr.io/stuzero/pg-mcp-server:latest"
      ],
      "timeout": 30000,
      "retry": {
        "attempts": 3,
        "delay": 1000
      }
    },
    "filesystem": {
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "E:\\The_real_deal_copy\\Bybit_Bot\\BOT"
      ],
      "timeout": 30000,
      "retry": {
        "attempts": 3,
        "delay": 1000
      }
    },
    "github": {
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-github"
      ],
      "timeout": 30000,
      "retry": {
        "attempts": 3,
        "delay": 1000
      }
    },
    "web-search": {
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "WebSearch-MCP"
      ],
      "timeout": 30000,
      "retry": {
        "attempts": 3,
        "delay": 1000
      }
    },
    "git": {
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@cyanheads/git-mcp-server",
        "E:\\The_real_deal_copy\\Bybit_Bot\\BOT"
      ],
      "timeout": 30000,
      "retry": {
        "attempts": 3,
        "delay": 1000
      }
    },
    "performance-boost": {
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/mcp-stdio-server@latest"
      ],
      "timeout": 30000,
      "retry": {
        "attempts": 3,
        "delay": 1000
      }
    }
  },
  // ===== Error Prevention =====
  "typescript.preferences.noSemicolons": "off",
  "javascript.preferences.noSemicolons": "off",
  "editor.formatOnSave": true,
  "files.autoSave": "afterDelay",
  "files.autoSaveDelay": 1000,
  // ===== Autonomous Trading Bot Workspace Settings =====
  "workbench.startupEditor": "welcomePage",
  "explorer.confirmDelete": false,
  "explorer.confirmDragAndDrop": false,
  "workbench.editor.enablePreview": false,
  "workbench.editor.enablePreviewFromQuickOpen": false,
  "terminal.integrated.cwd": "E:\\The_real_deal_copy\\Bybit_Bot\\BOT",
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/*/**": true,
    "**/__pycache__/**": true,
    "**/*.pyc": true,
    "**/logs/**": true,
    "**/data/**": true
  },
  "search.exclude": {
    "**/__pycache__": true,
    "**/*.pyc": true,
    "**/logs": true,
    "**/data": true,
    "**/node_modules": true
  },
  // ===== Automatic System Verification =====
  "tasks.autoRunLast": true,
  "terminal.integrated.automationShell.windows": "cmd.exe",
  "terminal.integrated.shellIntegration.enabled": true,
  "terminal.integrated.shellIntegration.showWelcome": false,
  // ===== Auto-Verification on Startup =====
  "workspace.autoStartTasks": [
    "Initialize Autonomous Trading System"
  ],
  "terminal.integrated.defaultLocation": "view",
  "terminal.integrated.showExitAlert": false
}