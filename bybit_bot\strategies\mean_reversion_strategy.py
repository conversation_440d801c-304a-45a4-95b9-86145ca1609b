"""
Mean Reversion Trading Strategy for Bybit Trading Bot
Identifies oversold/overbought conditions and trades reversals
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger

logger = logging.getLogger("bybit_trading_bot.mean_reversion_strategy")


class MeanReversionStrategy:
    """
    Mean reversion strategy using multiple indicators to identify
    oversold/overbought conditions and potential price reversals
    """
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingBotLogger(config)
        
        # Strategy parameters
        self.rsi_period = 14
        self.rsi_oversold = 30
        self.rsi_overbought = 70
        self.bb_period = 20
        self.bb_std = 2.0
        self.stoch_k_period = 14
        self.stoch_d_period = 3
        self.stoch_oversold = 20
        self.stoch_overbought = 80
        
        # Mean reversion parameters
        self.lookback_period = 50
        self.z_score_threshold = 2.0
        self.volume_confirmation = True
        
        # Strategy state
        self.last_signals = {}
        self.position_tracker = {}
        
    async def initialize(self):
        """Initialize mean reversion strategy"""
        try:
            self.logger.info("📈 Initializing Mean Reversion Strategy...")
            
            # Load strategy parameters from config if available
            strategy_config = getattr(self.config, 'mean_reversion_config', {})
            
            if strategy_config:
                self.rsi_period = strategy_config.get('rsi_period', self.rsi_period)
                self.rsi_oversold = strategy_config.get('rsi_oversold', self.rsi_oversold)
                self.rsi_overbought = strategy_config.get('rsi_overbought', self.rsi_overbought)
                self.bb_period = strategy_config.get('bb_period', self.bb_period)
                self.z_score_threshold = strategy_config.get('z_score_threshold', self.z_score_threshold)
            
            self.logger.info("✅ Mean Reversion Strategy initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Mean Reversion Strategy: {e}")
            raise
    
    async def analyze_market(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze market for mean reversion opportunities
        """
        try:
            # Extract price data
            price_data = market_data.get("price_data", [])
            if len(price_data) < max(self.rsi_period, self.bb_period, self.lookback_period):
                return {
                    "signal": "HOLD",
                    "confidence": 0.0,
                    "reason": "Insufficient data for mean reversion analysis"
                }
            
            # Convert to DataFrame
            df = pd.DataFrame(price_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.sort_values('timestamp')
            
            # Calculate technical indicators
            indicators = self._calculate_indicators(df)
            
            # Identify mean reversion signals
            signal_analysis = self._analyze_mean_reversion(df, indicators)
            
            # Volume confirmation
            volume_confirmation = self._check_volume_confirmation(df, signal_analysis["signal"])
            
            # Generate final signal
            final_signal = self._generate_signal(
                symbol, 
                signal_analysis, 
                volume_confirmation, 
                indicators
            )
            
            # Log signal generation
            self.logger.info(
                f"🔄 Mean Reversion Signal for {symbol}: {final_signal['signal']} "
                f"(confidence: {final_signal['confidence']:.2f})"
            )
            
            return final_signal
            
        except Exception as e:
            self.logger.error(f"Error analyzing market for mean reversion {symbol}: {e}")
            return {
                "signal": "HOLD",
                "confidence": 0.0,
                "reason": f"Analysis error: {e}"
            }
    
    def _calculate_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate technical indicators for mean reversion"""
        try:
            indicators = {}
            
            # Close prices
            close_prices = df['close'].astype(float)
            high_prices = df['high'].astype(float)
            low_prices = df['low'].astype(float)
            volumes = df['volume'].astype(float)
            
            # RSI (Relative Strength Index)
            rsi = self._calculate_rsi(close_prices, self.rsi_period)
            indicators['rsi'] = rsi.iloc[-1] if not rsi.empty else 50
            indicators['rsi_series'] = rsi
            
            # Bollinger Bands
            bb_middle, bb_upper, bb_lower = self._calculate_bollinger_bands(
                close_prices, self.bb_period, self.bb_std
            )
            indicators['bb_middle'] = bb_middle.iloc[-1] if not bb_middle.empty else close_prices.iloc[-1]
            indicators['bb_upper'] = bb_upper.iloc[-1] if not bb_upper.empty else close_prices.iloc[-1]
            indicators['bb_lower'] = bb_lower.iloc[-1] if not bb_lower.empty else close_prices.iloc[-1]
            
            # Bollinger Band position
            current_price = close_prices.iloc[-1]
            bb_width = indicators['bb_upper'] - indicators['bb_lower']
            if bb_width > 0:
                indicators['bb_position'] = (current_price - indicators['bb_lower']) / bb_width
            else:
                indicators['bb_position'] = 0.5
            
            # Stochastic Oscillator
            stoch_k, stoch_d = self._calculate_stochastic(
                high_prices, low_prices, close_prices, 
                self.stoch_k_period, self.stoch_d_period
            )
            indicators['stoch_k'] = stoch_k.iloc[-1] if not stoch_k.empty else 50
            indicators['stoch_d'] = stoch_d.iloc[-1] if not stoch_d.empty else 50
            
            # Z-Score for mean reversion
            z_score = self._calculate_z_score(close_prices, self.lookback_period)
            indicators['z_score'] = z_score.iloc[-1] if not z_score.empty else 0
            
            # Price distance from moving average
            sma_20 = close_prices.rolling(window=20).mean()
            sma_50 = close_prices.rolling(window=50).mean()
            
            indicators['sma_20'] = sma_20.iloc[-1] if not sma_20.empty else current_price
            indicators['sma_50'] = sma_50.iloc[-1] if not sma_50.empty else current_price
            
            # Distance from moving averages (as percentage)
            if indicators['sma_20'] > 0:
                indicators['distance_sma20'] = (current_price - indicators['sma_20']) / indicators['sma_20']
            else:
                indicators['distance_sma20'] = 0
            
            if indicators['sma_50'] > 0:
                indicators['distance_sma50'] = (current_price - indicators['sma_50']) / indicators['sma_50']
            else:
                indicators['distance_sma50'] = 0
            
            # Volume analysis
            avg_volume = volumes.rolling(window=20).mean()
            indicators['avg_volume'] = avg_volume.iloc[-1] if not avg_volume.empty else volumes.iloc[-1]
            indicators['current_volume'] = volumes.iloc[-1]
            indicators['volume_ratio'] = (indicators['current_volume'] / indicators['avg_volume'] 
                                        if indicators['avg_volume'] > 0 else 1.0)
            
            # Volatility
            returns = close_prices.pct_change()
            volatility = returns.rolling(window=20).std()
            indicators['volatility'] = volatility.iloc[-1] if not volatility.empty else 0.01
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
            return {}
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate Relative Strength Index"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
            
        except Exception as e:
            self.logger.error(f"Error calculating RSI: {e}")
            return pd.Series()
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int, std_dev: float) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands"""
        try:
            sma = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()
            
            upper_band = sma + (std * std_dev)
            lower_band = sma - (std * std_dev)
            
            return sma, upper_band, lower_band
            
        except Exception as e:
            self.logger.error(f"Error calculating Bollinger Bands: {e}")
            return pd.Series(), pd.Series(), pd.Series()
    
    def _calculate_stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                            k_period: int, d_period: int) -> Tuple[pd.Series, pd.Series]:
        """Calculate Stochastic Oscillator"""
        try:
            lowest_low = low.rolling(window=k_period).min()
            highest_high = high.rolling(window=k_period).max()
            
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            d_percent = k_percent.rolling(window=d_period).mean()
            
            return k_percent, d_percent
            
        except Exception as e:
            self.logger.error(f"Error calculating Stochastic: {e}")
            return pd.Series(), pd.Series()
    
    def _calculate_z_score(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate Z-Score for mean reversion"""
        try:
            rolling_mean = prices.rolling(window=period).mean()
            rolling_std = prices.rolling(window=period).std()
            
            z_score = (prices - rolling_mean) / rolling_std
            
            return z_score
            
        except Exception as e:
            self.logger.error(f"Error calculating Z-Score: {e}")
            return pd.Series()
    
    def _analyze_mean_reversion(self, df: pd.DataFrame, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze mean reversion opportunities"""
        try:
            analysis = {
                "signal": "HOLD",
                "strength": 0.0,
                "conditions": []
            }
            
            current_price = df['close'].iloc[-1]
            
            # Condition 1: RSI oversold/overbought
            rsi_signal = "NEUTRAL"
            rsi_strength = 0.0
            
            if indicators['rsi'] <= self.rsi_oversold:
                rsi_signal = "BUY"  # Oversold, expect bounce
                rsi_strength = (self.rsi_oversold - indicators['rsi']) / self.rsi_oversold
                analysis["conditions"].append(f"RSI oversold: {indicators['rsi']:.1f}")
            elif indicators['rsi'] >= self.rsi_overbought:
                rsi_signal = "SELL"  # Overbought, expect pullback
                rsi_strength = (indicators['rsi'] - self.rsi_overbought) / (100 - self.rsi_overbought)
                analysis["conditions"].append(f"RSI overbought: {indicators['rsi']:.1f}")
            
            # Condition 2: Bollinger Band position
            bb_signal = "NEUTRAL"
            bb_strength = 0.0
            
            if indicators['bb_position'] <= 0.1:  # Near lower band
                bb_signal = "BUY"
                bb_strength = (0.1 - indicators['bb_position']) / 0.1
                analysis["conditions"].append(f"Price near BB lower band: {indicators['bb_position']:.2f}")
            elif indicators['bb_position'] >= 0.9:  # Near upper band
                bb_signal = "SELL"
                bb_strength = (indicators['bb_position'] - 0.9) / 0.1
                analysis["conditions"].append(f"Price near BB upper band: {indicators['bb_position']:.2f}")
            
            # Condition 3: Z-Score extreme values
            z_score_signal = "NEUTRAL"
            z_score_strength = 0.0
            
            if indicators['z_score'] <= -self.z_score_threshold:
                z_score_signal = "BUY"  # Extremely undervalued
                z_score_strength = abs(indicators['z_score']) / self.z_score_threshold - 1
                analysis["conditions"].append(f"Z-Score extreme low: {indicators['z_score']:.2f}")
            elif indicators['z_score'] >= self.z_score_threshold:
                z_score_signal = "SELL"  # Extremely overvalued
                z_score_strength = indicators['z_score'] / self.z_score_threshold - 1
                analysis["conditions"].append(f"Z-Score extreme high: {indicators['z_score']:.2f}")
            
            # Condition 4: Stochastic confirmation
            stoch_signal = "NEUTRAL"
            stoch_strength = 0.0
            
            if (indicators['stoch_k'] <= self.stoch_oversold and 
                indicators['stoch_d'] <= self.stoch_oversold):
                stoch_signal = "BUY"
                stoch_strength = (self.stoch_oversold - min(indicators['stoch_k'], indicators['stoch_d'])) / self.stoch_oversold
                analysis["conditions"].append(f"Stochastic oversold: K={indicators['stoch_k']:.1f}, D={indicators['stoch_d']:.1f}")
            elif (indicators['stoch_k'] >= self.stoch_overbought and 
                  indicators['stoch_d'] >= self.stoch_overbought):
                stoch_signal = "SELL"
                stoch_strength = (min(indicators['stoch_k'], indicators['stoch_d']) - self.stoch_overbought) / (100 - self.stoch_overbought)
                analysis["conditions"].append(f"Stochastic overbought: K={indicators['stoch_k']:.1f}, D={indicators['stoch_d']:.1f}")
            
            # Condition 5: Distance from moving averages
            ma_distance_signal = "NEUTRAL"
            ma_distance_strength = 0.0
            
            # If price is significantly below moving averages (oversold)
            if indicators['distance_sma20'] <= -0.05 and indicators['distance_sma50'] <= -0.03:
                ma_distance_signal = "BUY"
                ma_distance_strength = abs(indicators['distance_sma20']) * 2  # Amplify signal
                analysis["conditions"].append(f"Price below MAs: SMA20={indicators['distance_sma20']:.2%}")
            # If price is significantly above moving averages (overbought)
            elif indicators['distance_sma20'] >= 0.05 and indicators['distance_sma50'] >= 0.03:
                ma_distance_signal = "SELL"
                ma_distance_strength = indicators['distance_sma20'] * 2
                analysis["conditions"].append(f"Price above MAs: SMA20={indicators['distance_sma20']:.2%}")
            
            # Combine signals
            buy_signals = [s for s in [rsi_signal, bb_signal, z_score_signal, stoch_signal, ma_distance_signal] if s == "BUY"]
            sell_signals = [s for s in [rsi_signal, bb_signal, z_score_signal, stoch_signal, ma_distance_signal] if s == "SELL"]
            
            # Calculate combined strength
            buy_strength = np.mean([s for s, sig in zip([rsi_strength, bb_strength, z_score_strength, stoch_strength, ma_distance_strength], 
                                                       [rsi_signal, bb_signal, z_score_signal, stoch_signal, ma_distance_signal]) if sig == "BUY"])
            sell_strength = np.mean([s for s, sig in zip([rsi_strength, bb_strength, z_score_strength, stoch_strength, ma_distance_strength], 
                                                        [rsi_signal, bb_signal, z_score_signal, stoch_signal, ma_distance_signal]) if sig == "SELL"])
            
            # Determine final signal
            min_confirmations = 2  # Need at least 2 indicators agreeing
            
            if len(buy_signals) >= min_confirmations and len(buy_signals) > len(sell_signals):
                analysis["signal"] = "BUY"
                analysis["strength"] = min(buy_strength or 0, 1.0)
            elif len(sell_signals) >= min_confirmations and len(sell_signals) > len(buy_signals):
                analysis["signal"] = "SELL"
                analysis["strength"] = min(sell_strength or 0, 1.0)
            else:
                analysis["signal"] = "HOLD"
                analysis["strength"] = 0.0
                analysis["conditions"].append("Insufficient confirmation from indicators")
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing mean reversion: {e}")
            return {"signal": "HOLD", "strength": 0.0, "conditions": [f"Analysis error: {e}"]}
    
    def _check_volume_confirmation(self, df: pd.DataFrame, signal: str) -> Dict[str, Any]:
        """Check volume confirmation for the signal"""
        try:
            if not self.volume_confirmation or signal == "HOLD":
                return {"confirmed": True, "reason": "Volume confirmation disabled or no signal"}
            
            volumes = df['volume'].astype(float)
            avg_volume = volumes.rolling(window=20).mean().iloc[-1]
            current_volume = volumes.iloc[-1]
            
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            
            # For mean reversion, we want confirmation but not extreme volume spikes
            if signal in ["BUY", "SELL"]:
                if volume_ratio >= 1.2:  # At least 20% above average
                    return {
                        "confirmed": True,
                        "reason": f"Volume confirmation: {volume_ratio:.2f}x average",
                        "volume_ratio": volume_ratio
                    }
                else:
                    return {
                        "confirmed": False,
                        "reason": f"Insufficient volume: {volume_ratio:.2f}x average",
                        "volume_ratio": volume_ratio
                    }
            
            return {"confirmed": True, "reason": "No volume confirmation needed"}
            
        except Exception as e:
            self.logger.error(f"Error checking volume confirmation: {e}")
            return {"confirmed": False, "reason": f"Volume check error: {e}"}
    
    def _generate_signal(self, symbol: str, signal_analysis: Dict[str, Any], 
                        volume_confirmation: Dict[str, Any], indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final trading signal"""
        try:
            base_signal = signal_analysis["signal"]
            base_strength = signal_analysis["strength"]
            
            # Adjust confidence based on volume confirmation
            if self.volume_confirmation and not volume_confirmation["confirmed"]:
                confidence = base_strength * 0.5  # Reduce confidence without volume confirmation
                reason_parts = [
                    f"Mean reversion signal: {base_signal}",
                    f"Base strength: {base_strength:.2f}",
                    volume_confirmation["reason"]
                ]
            else:
                confidence = base_strength * 0.9  # High confidence with confirmation
                reason_parts = [
                    f"Mean reversion signal: {base_signal}",
                    f"Strength: {base_strength:.2f}",
                    volume_confirmation.get("reason", "")
                ]
            
            # Additional confidence modifiers
            
            # Volatility adjustment - mean reversion works better in high volatility
            if indicators.get('volatility', 0) > 0.03:  # > 3% daily volatility
                confidence *= 1.1  # Boost confidence
                reason_parts.append("High volatility environment")
            elif indicators.get('volatility', 0) < 0.01:  # < 1% daily volatility
                confidence *= 0.8  # Reduce confidence
                reason_parts.append("Low volatility environment")
            
            # Ensure confidence is within bounds
            confidence = max(0.0, min(1.0, confidence))
            
            # Store signal for tracking
            self.last_signals[symbol] = {
                "signal": base_signal,
                "confidence": confidence,
                "timestamp": datetime.utcnow(),
                "indicators": indicators,
                "conditions": signal_analysis["conditions"]
            }
            
            return {
                "signal": base_signal,
                "confidence": confidence,
                "reason": " | ".join(filter(None, reason_parts)),
                "strategy": "mean_reversion",
                "indicators": {
                    "rsi": indicators.get('rsi', 0),
                    "bb_position": indicators.get('bb_position', 0),
                    "z_score": indicators.get('z_score', 0),
                    "stoch_k": indicators.get('stoch_k', 0),
                    "stoch_d": indicators.get('stoch_d', 0),
                    "volume_ratio": volume_confirmation.get('volume_ratio', 1.0)
                },
                "conditions": signal_analysis["conditions"],
                "entry_price": indicators.get('current_price', 0),
                "stop_loss_pct": self._calculate_stop_loss(base_signal, indicators),
                "take_profit_pct": self._calculate_take_profit(base_signal, indicators)
            }
            
        except Exception as e:
            self.logger.error(f"Error generating signal for {symbol}: {e}")
            return {
                "signal": "HOLD",
                "confidence": 0.0,
                "reason": f"Signal generation error: {e}",
                "strategy": "mean_reversion"
            }
    
    def _calculate_stop_loss(self, signal: str, indicators: Dict[str, Any]) -> float:
        """Calculate dynamic stop loss based on volatility and support/resistance"""
        try:
            base_stop_loss = 0.02  # 2% base stop loss
            
            # Adjust based on volatility
            volatility = indicators.get('volatility', 0.01)
            volatility_multiplier = max(1.0, volatility * 50)  # Scale volatility
            
            # Adjust based on Bollinger Band width (volatility measure)
            bb_width = (indicators.get('bb_upper', 0) - indicators.get('bb_lower', 0)) / indicators.get('bb_middle', 1)
            bb_multiplier = max(1.0, bb_width * 5)
            
            # Calculate dynamic stop loss
            dynamic_stop = base_stop_loss * volatility_multiplier * bb_multiplier
            
            # Cap the stop loss
            return min(dynamic_stop, 0.05)  # Max 5% stop loss
            
        except Exception as e:
            self.logger.error(f"Error calculating stop loss: {e}")
            return 0.02  # Default 2%
    
    def _calculate_take_profit(self, signal: str, indicators: Dict[str, Any]) -> float:
        """Calculate dynamic take profit based on mean reversion targets"""
        try:
            # For mean reversion, target is typically the mean (moving average)
            distance_to_sma20 = abs(indicators.get('distance_sma20', 0))
            distance_to_sma50 = abs(indicators.get('distance_sma50', 0))
            
            # Target 50-80% of the distance back to the mean
            mean_reversion_target = max(distance_to_sma20, distance_to_sma50) * 0.6
            
            # Minimum take profit
            min_take_profit = 0.015  # 1.5%
            
            # Maximum take profit for mean reversion
            max_take_profit = 0.04  # 4%
            
            return max(min_take_profit, min(mean_reversion_target, max_take_profit))
            
        except Exception as e:
            self.logger.error(f"Error calculating take profit: {e}")
            return 0.025  # Default 2.5%
    
    def get_strategy_status(self) -> Dict[str, Any]:
        """Get current strategy status"""
        try:
            return {
                "strategy_name": "mean_reversion",
                "last_signals": dict(self.last_signals),
                "parameters": {
                    "rsi_period": self.rsi_period,
                    "rsi_oversold": self.rsi_oversold,
                    "rsi_overbought": self.rsi_overbought,
                    "bb_period": self.bb_period,
                    "bb_std": self.bb_std,
                    "z_score_threshold": self.z_score_threshold,
                    "lookback_period": self.lookback_period,
                    "volume_confirmation": self.volume_confirmation
                },
                "active_positions": len(self.position_tracker)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting strategy status: {e}")
            return {"error": str(e)}
