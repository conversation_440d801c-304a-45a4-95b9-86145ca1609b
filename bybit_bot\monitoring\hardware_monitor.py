"""
Hardware Monitoring Module for Bybit Trading Bot
Monitors system health including CPU, memory, disk, and network
"""

import asyncio
import logging
import platform
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

import psutil

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger

logger = logging.getLogger("bybit_trading_bot.hardware")


class HardwareMonitor:
    """
    Monitors system hardware health and performance
    """
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingBotLogger(config)
        self.is_running = False
        self.monitor_task = None
        self.last_check_time = None
        self.system_alerts = []
        
        # Thresholds
        self.cpu_temp_threshold = config.max_cpu_temperature
        self.ram_threshold = config.max_ram_usage_percentage
        self.disk_threshold = config.min_disk_space_gb
        
        # Monitoring history
        self.cpu_history = []
        self.memory_history = []
        self.temperature_history = []
        
        # System info
        self.system_info = self._get_system_info()
        
    async def start(self):
        """Start hardware monitoring"""
        if self.is_running:
            logger.warning("Hardware monitor is already running")
            return
        
        self.is_running = True
        logger.info("🖥️ Starting hardware monitoring...")
        
        # Start monitoring task
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        
        # Log initial system info
        await self._log_system_info()
        
    async def stop(self):
        """Stop hardware monitoring"""
        if not self.is_running:
            return
        
        logger.info("🛑 Stopping hardware monitoring...")
        self.is_running = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("✅ Hardware monitoring stopped")
    
    async def _monitor_loop(self):
        """Main monitoring loop"""
        try:
            while self.is_running:
                try:
                    # Collect hardware metrics
                    metrics = await self._collect_metrics()
                    
                    # Check for issues
                    issues = await self._check_for_issues(metrics)
                    
                    # Log issues if any
                    if issues:
                        for issue in issues:
                            self.logger.log_hardware(
                                component=issue["component"],
                                status=issue["status"],
                                value=issue.get("value"),
                                unit=issue.get("unit", "")
                            )
                    
                    # Update monitoring history
                    self._update_history(metrics)
                    
                    # Sleep until next check
                    await asyncio.sleep(self.config.hardware_check_interval)
                    
                except Exception as e:
                    logger.error(f"Error in hardware monitoring loop: {e}")
                    await asyncio.sleep(30)  # Wait before retrying
                    
        except asyncio.CancelledError:
            logger.info("Hardware monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Fatal error in hardware monitoring loop: {e}")
    
    async def _collect_metrics(self) -> Dict[str, Any]:
        """Collect comprehensive hardware metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_freq = psutil.cpu_freq()
            cpu_count = psutil.cpu_count()
            
            # Memory metrics
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # Disk metrics
            disk_usage = psutil.disk_usage('E:' if platform.system() == 'Windows' else '/')
            
            # Network metrics
            network = psutil.net_io_counters()
            
            # Temperature (if available)
            cpu_temp = await self._get_cpu_temperature()
            
            # System uptime
            boot_time = psutil.boot_time()
            uptime = time.time() - boot_time
            
            metrics = {
                'timestamp': datetime.utcnow(),
                'cpu': {
                    'usage_percent': cpu_percent,
                    'frequency_mhz': cpu_freq.current if cpu_freq else None,
                    'temperature_celsius': cpu_temp,
                    'count': cpu_count
                },
                'memory': {
                    'usage_percent': memory.percent,
                    'used_gb': memory.used / (1024**3),
                    'available_gb': memory.available / (1024**3),
                    'total_gb': memory.total / (1024**3)
                },
                'swap': {
                    'usage_percent': swap.percent,
                    'used_gb': swap.used / (1024**3),
                    'total_gb': swap.total / (1024**3)
                },
                'disk': {
                    'usage_percent': (disk_usage.used / disk_usage.total) * 100,
                    'free_gb': disk_usage.free / (1024**3),
                    'used_gb': disk_usage.used / (1024**3),
                    'total_gb': disk_usage.total / (1024**3)
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_received': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_received': network.packets_recv
                },
                'system': {
                    'uptime_seconds': uptime,
                    'boot_time': datetime.fromtimestamp(boot_time)
                }
            }
            
            self.last_check_time = metrics['timestamp']
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to collect hardware metrics: {e}")
            return {}
    
    async def _get_cpu_temperature(self) -> Optional[float]:
        """Get CPU temperature (platform-dependent)"""
        try:
            if hasattr(psutil, "sensors_temperatures"):
                temps = psutil.sensors_temperatures()
                if temps:
                    # Try different temperature sensor names
                    for name in ['coretemp', 'cpu_thermal', 'acpi']:
                        if name in temps:
                            return temps[name][0].current
            return None
        except Exception:
            return None
    
    async def _check_for_issues(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for hardware issues based on thresholds"""
        issues = []
        
        if not metrics:
            return issues
        
        try:
            # Check CPU temperature
            cpu_temp = metrics['cpu'].get('temperature_celsius')
            if cpu_temp and cpu_temp > self.cpu_temp_threshold:
                issues.append({
                    'component': 'CPU',
                    'status': 'HIGH_TEMPERATURE',
                    'value': cpu_temp,
                    'unit': '°C',
                    'severity': 'critical' if cpu_temp > self.cpu_temp_threshold + 10 else 'warning'
                })
            
            # Check CPU usage
            cpu_usage = metrics['cpu'].get('usage_percent', 0)
            if cpu_usage > 90:
                issues.append({
                    'component': 'CPU',
                    'status': 'HIGH_USAGE',
                    'value': cpu_usage,
                    'unit': '%',
                    'severity': 'warning'
                })
            
            # Check memory usage
            memory_usage = metrics['memory'].get('usage_percent', 0)
            if memory_usage > self.ram_threshold:
                issues.append({
                    'component': 'Memory',
                    'status': 'HIGH_USAGE',
                    'value': memory_usage,
                    'unit': '%',
                    'severity': 'critical' if memory_usage > 95 else 'warning'
                })
            
            # Check disk space
            disk_free = metrics['disk'].get('free_gb', float('inf'))
            if disk_free < self.disk_threshold:
                issues.append({
                    'component': 'Disk',
                    'status': 'LOW_SPACE',
                    'value': disk_free,
                    'unit': 'GB',
                    'severity': 'critical' if disk_free < 1 else 'warning'
                })
            
            # Check disk usage percentage
            disk_usage = metrics['disk'].get('usage_percent', 0)
            if disk_usage > 95:
                issues.append({
                    'component': 'Disk',
                    'status': 'HIGH_USAGE',
                    'value': disk_usage,
                    'unit': '%',
                    'severity': 'critical'
                })
            
            return issues
            
        except Exception as e:
            logger.error(f"Failed to check for hardware issues: {e}")
            return []
    
    def _update_history(self, metrics: Dict[str, Any]):
        """Update monitoring history for trend analysis"""
        try:
            max_history = 100  # Keep last 100 readings
            
            # Update CPU history
            if metrics.get('cpu'):
                self.cpu_history.append({
                    'timestamp': metrics['timestamp'],
                    'usage': metrics['cpu'].get('usage_percent'),
                    'temperature': metrics['cpu'].get('temperature_celsius')
                })
                if len(self.cpu_history) > max_history:
                    self.cpu_history.pop(0)
            
            # Update memory history
            if metrics.get('memory'):
                self.memory_history.append({
                    'timestamp': metrics['timestamp'],
                    'usage_percent': metrics['memory'].get('usage_percent'),
                    'used_gb': metrics['memory'].get('used_gb')
                })
                if len(self.memory_history) > max_history:
                    self.memory_history.pop(0)
            
        except Exception as e:
            logger.error(f"Failed to update hardware history: {e}")
    
    async def check_system_health(self) -> bool:
        """
        Check if system is healthy enough for trading
        Returns True if healthy, False if critical issues detected
        """
        try:
            metrics = await self._collect_metrics()
            if not metrics:
                return False
            
            issues = await self._check_for_issues(metrics)
            
            # Check for critical issues that should stop trading
            critical_issues = [issue for issue in issues if issue.get('severity') == 'critical']
            
            if critical_issues:
                logger.warning(f"Critical hardware issues detected: {len(critical_issues)} issues")
                for issue in critical_issues:
                    logger.error(f"CRITICAL: {issue['component']} - {issue['status']}: {issue.get('value', 'N/A')}{issue.get('unit', '')}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to check system health: {e}")
            return False
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            metrics = await self._collect_metrics()
            issues = await self._check_for_issues(metrics) if metrics else []
            
            # Determine overall status
            critical_issues = [issue for issue in issues if issue.get('severity') == 'critical']
            warning_issues = [issue for issue in issues if issue.get('severity') == 'warning']
            
            if critical_issues:
                overall_status = 'critical'
            elif warning_issues:
                overall_status = 'warning'
            else:
                overall_status = 'healthy'
            
            return {
                'overall_status': overall_status,
                'last_check': self.last_check_time.isoformat() if self.last_check_time else None,
                'metrics': metrics,
                'issues': issues,
                'system_info': self.system_info,
                'monitoring_uptime': time.time() - (self.monitor_task.get_name() if self.monitor_task else time.time())
            }
            
        except Exception as e:
            logger.error(f"Failed to get system status: {e}")
            return {'overall_status': 'error', 'error': str(e)}
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get static system information"""
        try:
            return {
                'platform': platform.system(),
                'platform_release': platform.release(),
                'platform_version': platform.version(),
                'architecture': platform.machine(),
                'processor': platform.processor(),
                'python_version': platform.python_version(),
                'cpu_count': psutil.cpu_count(),
                'cpu_count_logical': psutil.cpu_count(logical=True),
                'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                'hostname': platform.node()
            }
        except Exception as e:
            logger.error(f"Failed to get system info: {e}")
            return {}
    
    async def _log_system_info(self):
        """Log initial system information"""
        try:
            info = self.system_info
            logger.info(f"🖥️ System: {info.get('platform')} {info.get('platform_release')}")
            logger.info(f"🏗️ Architecture: {info.get('architecture')}")
            logger.info(f"🧠 CPU: {info.get('processor')} ({info.get('cpu_count_logical')} cores)")
            logger.info(f"💾 Memory: {info.get('memory_total_gb', 0):.1f} GB")
            logger.info(f"🐍 Python: {info.get('python_version')}")
            logger.info(f"🏠 Hostname: {info.get('hostname')}")
            
        except Exception as e:
            logger.error(f"Failed to log system info: {e}")
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get performance summary for the last N hours"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            # Filter recent history
            recent_cpu = [h for h in self.cpu_history if h['timestamp'] > cutoff_time]
            recent_memory = [h for h in self.memory_history if h['timestamp'] > cutoff_time]
            
            if not recent_cpu or not recent_memory:
                return {'error': 'Insufficient data'}
            
            # Calculate averages and peaks
            cpu_usages = [h['usage'] for h in recent_cpu if h['usage'] is not None]
            cpu_temps = [h['temperature'] for h in recent_cpu if h['temperature'] is not None]
            memory_usages = [h['usage_percent'] for h in recent_memory if h['usage_percent'] is not None]
            
            summary = {
                'period_hours': hours,
                'data_points': len(recent_cpu),
                'cpu': {
                    'avg_usage': sum(cpu_usages) / len(cpu_usages) if cpu_usages else 0,
                    'max_usage': max(cpu_usages) if cpu_usages else 0,
                    'avg_temperature': sum(cpu_temps) / len(cpu_temps) if cpu_temps else None,
                    'max_temperature': max(cpu_temps) if cpu_temps else None
                },
                'memory': {
                    'avg_usage': sum(memory_usages) / len(memory_usages) if memory_usages else 0,
                    'max_usage': max(memory_usages) if memory_usages else 0
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get performance summary: {e}")
            return {'error': str(e)}
