"""
Advanced Database Schema for Enhanced Bybit Trading Bot
Supports persistent memory, AI learning, time management, and data integration
"""

-- Enhanced Trading Memories Table
CREATE TABLE IF NOT EXISTS trading_memories (
    memory_id VARCHAR(100) PRIMARY KEY,
    pattern_type VARCHAR(50) NOT NULL,
    importance VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    market_conditions JSONB NOT NULL,
    strategy_used VARCHAR(100) NOT NULL,
    action_taken VARCHAR(100) NOT NULL,
    outcome JSONB NOT NULL,
    context JSONB,
    pattern_hash VARCHAR(64) NOT NULL,
    confidence FLOAT DEFAULT 0.5,
    usage_count INTEGER DEFAULT 0,
    success_rate FLOAT DEFAULT 0.0,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Strategy Memories Table
CREATE TABLE IF NOT EXISTS strategy_memories (
    strategy_name VARCHAR(100) PRIMARY KEY,
    memory_data JSONB NOT NULL,
    best_parameters JSONB,
    best_performance JSONB,
    parameter_history JSONB,
    performance_history JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Pattern Performance Tracking
CREATE TABLE IF NOT EXISTS pattern_performance (
    pattern_hash VARCHAR(64) PRIMARY KEY,
    total_occurrences INTEGER DEFAULT 0,
    successful_occurrences INTEGER DEFAULT 0,
    success_rate FLOAT DEFAULT 0.0,
    avg_profit FLOAT DEFAULT 0.0,
    avg_loss FLOAT DEFAULT 0.0,
    last_occurrence TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Time-Based Performance Analytics
CREATE TABLE IF NOT EXISTS time_performance_analytics (
    id SERIAL PRIMARY KEY,
    analysis_date DATE NOT NULL,
    hour_of_day INTEGER CHECK (hour_of_day >= 0 AND hour_of_day <= 23),
    day_of_week INTEGER CHECK (day_of_week >= 0 AND day_of_week <= 6),
    market_session VARCHAR(50),
    symbol VARCHAR(20),
    total_trades INTEGER DEFAULT 0,
    profitable_trades INTEGER DEFAULT 0,
    total_pnl FLOAT DEFAULT 0.0,
    avg_trade_duration_minutes INTEGER DEFAULT 0,
    volatility FLOAT DEFAULT 0.0,
    volume_ratio FLOAT DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(analysis_date, hour_of_day, day_of_week, market_session, symbol)
);

-- Market Session Analytics
CREATE TABLE IF NOT EXISTS market_session_analytics (
    id SERIAL PRIMARY KEY,
    session_date DATE NOT NULL,
    session_name VARCHAR(50) NOT NULL,
    session_start TIMESTAMP WITH TIME ZONE,
    session_end TIMESTAMP WITH TIME ZONE,
    total_volume FLOAT DEFAULT 0.0,
    avg_volatility FLOAT DEFAULT 0.0,
    price_change_percent FLOAT DEFAULT 0.0,
    trades_executed INTEGER DEFAULT 0,
    session_pnl FLOAT DEFAULT 0.0,
    optimal_for_trading BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(session_date, session_name)
);

-- HuggingFace Dataset Metadata
CREATE TABLE IF NOT EXISTS hf_dataset_metadata (
    dataset_name VARCHAR(200) PRIMARY KEY,
    source VARCHAR(50) DEFAULT 'huggingface',
    description TEXT,
    data_type VARCHAR(50),
    features JSONB,
    size INTEGER DEFAULT 0,
    relevance_score FLOAT DEFAULT 0.0,
    last_updated TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active',
    processing_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Firecrawl Data Storage
CREATE TABLE IF NOT EXISTS firecrawl_data (
    id SERIAL PRIMARY KEY,
    url TEXT NOT NULL,
    crawl_id VARCHAR(100),
    success BOOLEAN DEFAULT FALSE,
    content TEXT,
    metadata JSONB,
    extracted_data JSONB,
    status_code INTEGER,
    error_message TEXT,
    data_type VARCHAR(50),
    relevance_score FLOAT DEFAULT 0.0,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Market Intelligence Aggregation
CREATE TABLE IF NOT EXISTS market_intelligence (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    data JSONB NOT NULL,
    confidence_score FLOAT DEFAULT 0.0,
    total_sources INTEGER DEFAULT 0,
    news_sentiment FLOAT DEFAULT 0.0,
    social_sentiment FLOAT DEFAULT 0.0,
    technical_score FLOAT DEFAULT 0.0,
    fundamental_score FLOAT DEFAULT 0.0,
    risk_assessment JSONB,
    recommendations JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Learning Progress Tracking
CREATE TABLE IF NOT EXISTS ai_learning_progress (
    id SERIAL PRIMARY KEY,
    learning_session_id VARCHAR(100),
    strategy_name VARCHAR(100),
    learning_type VARCHAR(50), -- 'pattern_recognition', 'parameter_optimization', 'risk_assessment'
    input_data JSONB,
    output_data JSONB,
    performance_before JSONB,
    performance_after JSONB,
    improvement_score FLOAT DEFAULT 0.0,
    confidence FLOAT DEFAULT 0.0,
    validation_status VARCHAR(20) DEFAULT 'pending',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Real-time Market Conditions
CREATE TABLE IF NOT EXISTS real_time_market_conditions (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    price FLOAT NOT NULL,
    volume FLOAT DEFAULT 0.0,
    volatility FLOAT DEFAULT 0.0,
    bid FLOAT,
    ask FLOAT,
    spread_percent FLOAT,
    market_session VARCHAR(50),
    technical_indicators JSONB,
    sentiment_score FLOAT DEFAULT 0.0,
    news_impact_score FLOAT DEFAULT 0.0,
    social_volume INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trading Decisions Log
CREATE TABLE IF NOT EXISTS trading_decisions_log (
    id SERIAL PRIMARY KEY,
    decision_id VARCHAR(100) UNIQUE NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    strategy_used VARCHAR(100),
    decision_type VARCHAR(50), -- 'buy', 'sell', 'hold', 'close'
    decision_reason TEXT,
    market_conditions JSONB,
    ai_confidence FLOAT DEFAULT 0.0,
    risk_assessment JSONB,
    expected_outcome JSONB,
    actual_outcome JSONB,
    decision_quality_score FLOAT,
    lessons_learned TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance Optimization History
CREATE TABLE IF NOT EXISTS performance_optimization_history (
    id SERIAL PRIMARY KEY,
    optimization_id VARCHAR(100) UNIQUE NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    optimization_type VARCHAR(50), -- 'strategy_params', 'risk_management', 'timing'
    parameters_before JSONB,
    parameters_after JSONB,
    performance_before JSONB,
    performance_after JSONB,
    improvement_metrics JSONB,
    validation_period_days INTEGER DEFAULT 30,
    success BOOLEAN,
    rollback_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Social Media Sentiment Aggregation
CREATE TABLE IF NOT EXISTS social_sentiment_aggregation (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    symbol VARCHAR(20),
    platform VARCHAR(50), -- 'twitter', 'reddit', 'telegram', 'discord'
    total_mentions INTEGER DEFAULT 0,
    positive_mentions INTEGER DEFAULT 0,
    negative_mentions INTEGER DEFAULT 0,
    neutral_mentions INTEGER DEFAULT 0,
    sentiment_score FLOAT DEFAULT 0.0,
    trending_topics JSONB,
    influencer_sentiment FLOAT DEFAULT 0.0,
    volume_trend VARCHAR(20) DEFAULT 'stable',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Economic Calendar Integration
CREATE TABLE IF NOT EXISTS economic_calendar (
    id SERIAL PRIMARY KEY,
    event_id VARCHAR(100) UNIQUE,
    event_name VARCHAR(200) NOT NULL,
    event_date TIMESTAMP WITH TIME ZONE NOT NULL,
    country VARCHAR(50),
    importance VARCHAR(20), -- 'low', 'medium', 'high'
    currency VARCHAR(10),
    actual_value FLOAT,
    forecast_value FLOAT,
    previous_value FLOAT,
    market_impact_prediction JSONB,
    actual_market_impact JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Risk Management Rules
CREATE TABLE IF NOT EXISTS risk_management_rules (
    id SERIAL PRIMARY KEY,
    rule_id VARCHAR(100) UNIQUE NOT NULL,
    rule_name VARCHAR(200) NOT NULL,
    rule_type VARCHAR(50), -- 'position_size', 'stop_loss', 'take_profit', 'exposure_limit'
    conditions JSONB NOT NULL,
    actions JSONB NOT NULL,
    priority INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    success_rate FLOAT DEFAULT 0.0,
    times_triggered INTEGER DEFAULT 0,
    avg_impact FLOAT DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Advanced Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_trading_memories_pattern_hash ON trading_memories(pattern_hash);
CREATE INDEX IF NOT EXISTS idx_trading_memories_timestamp ON trading_memories(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_trading_memories_importance ON trading_memories(importance);
CREATE INDEX IF NOT EXISTS idx_trading_memories_success_rate ON trading_memories(success_rate DESC);

CREATE INDEX IF NOT EXISTS idx_time_performance_hour_day ON time_performance_analytics(hour_of_day, day_of_week);
CREATE INDEX IF NOT EXISTS idx_time_performance_session ON time_performance_analytics(market_session);
CREATE INDEX IF NOT EXISTS idx_time_performance_symbol ON time_performance_analytics(symbol);

CREATE INDEX IF NOT EXISTS idx_market_conditions_symbol_timestamp ON real_time_market_conditions(symbol, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_market_conditions_session ON real_time_market_conditions(market_session);

CREATE INDEX IF NOT EXISTS idx_decisions_log_timestamp ON trading_decisions_log(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_decisions_log_symbol ON trading_decisions_log(symbol);
CREATE INDEX IF NOT EXISTS idx_decisions_log_strategy ON trading_decisions_log(strategy_used);

CREATE INDEX IF NOT EXISTS idx_firecrawl_data_type ON firecrawl_data(data_type);
CREATE INDEX IF NOT EXISTS idx_firecrawl_timestamp ON firecrawl_data(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_firecrawl_relevance ON firecrawl_data(relevance_score DESC);

CREATE INDEX IF NOT EXISTS idx_market_intelligence_timestamp ON market_intelligence(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_market_intelligence_confidence ON market_intelligence(confidence_score DESC);

CREATE INDEX IF NOT EXISTS idx_social_sentiment_timestamp ON social_sentiment_aggregation(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_social_sentiment_symbol ON social_sentiment_aggregation(symbol);
CREATE INDEX IF NOT EXISTS idx_social_sentiment_platform ON social_sentiment_aggregation(platform);

CREATE INDEX IF NOT EXISTS idx_economic_calendar_date ON economic_calendar(event_date);
CREATE INDEX IF NOT EXISTS idx_economic_calendar_importance ON economic_calendar(importance);
CREATE INDEX IF NOT EXISTS idx_economic_calendar_currency ON economic_calendar(currency);

-- Views for Quick Analytics
CREATE OR REPLACE VIEW daily_performance_summary AS
SELECT 
    DATE(timestamp) as trade_date,
    COUNT(*) as total_trades,
    SUM(CASE WHEN (actual_outcome->>'pnl')::float > 0 THEN 1 ELSE 0 END) as profitable_trades,
    SUM((actual_outcome->>'pnl')::float) as daily_pnl,
    AVG(ai_confidence) as avg_confidence,
    COUNT(DISTINCT strategy_used) as strategies_used
FROM trading_decisions_log
WHERE actual_outcome IS NOT NULL
GROUP BY DATE(timestamp)
ORDER BY trade_date DESC;

CREATE OR REPLACE VIEW hourly_performance_analysis AS
SELECT 
    EXTRACT(hour FROM timestamp) as hour_of_day,
    COUNT(*) as total_trades,
    AVG((actual_outcome->>'pnl')::float) as avg_pnl,
    STDDEV((actual_outcome->>'pnl')::float) as pnl_volatility,
    SUM(CASE WHEN (actual_outcome->>'pnl')::float > 0 THEN 1 ELSE 0 END)::float / COUNT(*) as win_rate
FROM trading_decisions_log
WHERE actual_outcome IS NOT NULL
GROUP BY EXTRACT(hour FROM timestamp)
ORDER BY hour_of_day;

CREATE OR REPLACE VIEW strategy_performance_ranking AS
SELECT 
    strategy_used,
    COUNT(*) as total_trades,
    SUM((actual_outcome->>'pnl')::float) as total_pnl,
    AVG((actual_outcome->>'pnl')::float) as avg_pnl,
    SUM(CASE WHEN (actual_outcome->>'pnl')::float > 0 THEN 1 ELSE 0 END)::float / COUNT(*) as win_rate,
    AVG(ai_confidence) as avg_confidence,
    MAX(timestamp) as last_used
FROM trading_decisions_log
WHERE actual_outcome IS NOT NULL AND strategy_used IS NOT NULL
GROUP BY strategy_used
ORDER BY total_pnl DESC;

CREATE OR REPLACE VIEW memory_utilization_stats AS
SELECT 
    pattern_type,
    importance,
    COUNT(*) as memory_count,
    AVG(confidence) as avg_confidence,
    AVG(usage_count) as avg_usage,
    AVG(success_rate) as avg_success_rate,
    MAX(last_accessed) as last_accessed
FROM trading_memories
GROUP BY pattern_type, importance
ORDER BY avg_success_rate DESC;

-- Functions for Advanced Analytics
CREATE OR REPLACE FUNCTION calculate_pattern_similarity(
    conditions1 JSONB,
    conditions2 JSONB
) RETURNS FLOAT AS $$
DECLARE
    similarity FLOAT := 0.0;
    total_weight FLOAT := 0.0;
    feature_weights JSONB := '{"volatility": 0.2, "volume": 0.15, "trend_strength": 0.2, "rsi": 0.1, "macd": 0.1, "time_of_day": 0.1, "market_session": 0.15}';
    feature TEXT;
    weight FLOAT;
    val1 FLOAT;
    val2 FLOAT;
    feature_similarity FLOAT;
BEGIN
    FOR feature, weight IN SELECT * FROM jsonb_each_text(feature_weights) LOOP
        IF conditions1 ? feature AND conditions2 ? feature THEN
            val1 := (conditions1->>feature)::FLOAT;
            val2 := (conditions2->>feature)::FLOAT;
            
            IF val1 = val2 THEN
                feature_similarity := 1.0;
            ELSE
                feature_similarity := 1.0 - ABS(val1 - val2) / GREATEST(ABS(val1), ABS(val2), 1.0);
            END IF;
            
            similarity := similarity + feature_similarity * weight::FLOAT;
            total_weight := total_weight + weight::FLOAT;
        END IF;
    END LOOP;
    
    IF total_weight > 0 THEN
        RETURN similarity / total_weight;
    ELSE
        RETURN 0.0;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_optimal_trading_hours()
RETURNS TABLE(hour_of_day INTEGER, avg_pnl FLOAT, win_rate FLOAT, trade_count BIGINT) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        EXTRACT(hour FROM t.timestamp)::INTEGER as hour_of_day,
        AVG((t.actual_outcome->>'pnl')::FLOAT) as avg_pnl,
        (SUM(CASE WHEN (t.actual_outcome->>'pnl')::FLOAT > 0 THEN 1 ELSE 0 END)::FLOAT / COUNT(*)) as win_rate,
        COUNT(*) as trade_count
    FROM trading_decisions_log t
    WHERE t.actual_outcome IS NOT NULL
        AND t.timestamp >= NOW() - INTERVAL '30 days'
    GROUP BY EXTRACT(hour FROM t.timestamp)
    HAVING COUNT(*) >= 5  -- At least 5 trades in the hour
    ORDER BY avg_pnl DESC;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_pattern_performance()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO pattern_performance (pattern_hash, total_occurrences, successful_occurrences, last_occurrence)
    VALUES (
        NEW.pattern_hash,
        1,
        CASE WHEN (NEW.outcome->>'success')::BOOLEAN THEN 1 ELSE 0 END,
        NEW.timestamp
    )
    ON CONFLICT (pattern_hash) DO UPDATE SET
        total_occurrences = pattern_performance.total_occurrences + 1,
        successful_occurrences = pattern_performance.successful_occurrences + 
            CASE WHEN (NEW.outcome->>'success')::BOOLEAN THEN 1 ELSE 0 END,
        success_rate = (pattern_performance.successful_occurrences + 
            CASE WHEN (NEW.outcome->>'success')::BOOLEAN THEN 1 ELSE 0 END)::FLOAT / 
            (pattern_performance.total_occurrences + 1),
        last_occurrence = NEW.timestamp,
        updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for Automatic Updates
CREATE TRIGGER update_pattern_performance_trigger
    AFTER INSERT ON trading_memories
    FOR EACH ROW
    EXECUTE FUNCTION update_pattern_performance();

-- Update timestamps automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_trading_memories_updated_at
    BEFORE UPDATE ON trading_memories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_strategy_memories_updated_at
    BEFORE UPDATE ON strategy_memories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trading_decisions_updated_at
    BEFORE UPDATE ON trading_decisions_log
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Data Retention Policies (Optional - for large datasets)
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- Clean up old market conditions (keep 30 days)
    DELETE FROM real_time_market_conditions 
    WHERE timestamp < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Clean up old firecrawl data (keep 7 days for raw data, keep processed insights longer)
    DELETE FROM firecrawl_data 
    WHERE timestamp < NOW() - INTERVAL '7 days' 
    AND extracted_data IS NULL;
    
    -- Archive old trading memories with low importance
    UPDATE trading_memories 
    SET importance = 'archived'
    WHERE importance = 'low' 
    AND timestamp < NOW() - INTERVAL '90 days'
    AND usage_count < 3;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Performance Monitoring
CREATE OR REPLACE VIEW system_performance_metrics AS
SELECT 
    'trading_memories' as table_name,
    COUNT(*) as row_count,
    pg_size_pretty(pg_total_relation_size('trading_memories')) as table_size
FROM trading_memories
UNION ALL
SELECT 
    'market_intelligence' as table_name,
    COUNT(*) as row_count,
    pg_size_pretty(pg_total_relation_size('market_intelligence')) as table_size
FROM market_intelligence
UNION ALL
SELECT 
    'trading_decisions_log' as table_name,
    COUNT(*) as row_count,
    pg_size_pretty(pg_total_relation_size('trading_decisions_log')) as table_size
FROM trading_decisions_log;
