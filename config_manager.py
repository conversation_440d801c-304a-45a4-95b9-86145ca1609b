"""
Production Configuration Management for Super-GPT Trading Bot
Handles configuration, secrets, and environment management
"""
import os
import sys
import json
import yaml
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import secrets
import hashlib
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import warnings
warnings.filterwarnings('ignore')

# Add parent directory to path
sys.path.append(str(Path(__file__).parent))

from bybit_bot.core.logger import TradingBotLogger


class Environment(Enum):
    """Environment types"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


class ConfigLevel(Enum):
    """Configuration levels"""
    SYSTEM = "system"
    APPLICATION = "application"
    STRATEGY = "strategy"
    RUNTIME = "runtime"


class SecretType(Enum):
    """Secret types"""
    API_KEY = "api_key"
    SECRET_KEY = "secret_key"
    PASSWORD = "password"
    TOKEN = "token"
    CERTIFICATE = "certificate"
    PRIVATE_KEY = "private_key"


@dataclass
class DatabaseConfig:
    """Database configuration"""
    host: str
    port: int
    database: str
    user: str
    password: str
    ssl_mode: str = "require"
    connection_pool_size: int = 20
    max_connections: int = 100
    timeout: int = 30


@dataclass
class ExchangeConfig:
    """Exchange configuration"""
    name: str
    api_key: str
    secret_key: str
    passphrase: Optional[str] = None
    sandbox: bool = False
    testnet: bool = False
    base_url: Optional[str] = None
    websocket_url: Optional[str] = None
    rate_limit: int = 100
    timeout: int = 30


@dataclass
class RiskConfig:
    """Risk management configuration"""
    max_position_size: float = 0.1
    max_daily_loss: float = 0.05
    max_drawdown: float = 0.2
    stop_loss_percentage: float = 0.05
    take_profit_percentage: float = 0.1
    risk_free_rate: float = 0.02
    var_confidence: float = 0.95
    max_leverage: int = 10
    max_correlation: float = 0.8
    emergency_stop_loss: float = 0.15


@dataclass
class TradingConfig:
    """Trading configuration"""
    symbols: List[str]
    default_quantity: float = 100.0
    min_trade_size: float = 10.0
    max_trade_size: float = 10000.0
    slippage_tolerance: float = 0.001
    execution_timeout: int = 30
    order_retry_count: int = 3
    price_precision: int = 8
    quantity_precision: int = 8
    paper_trading: bool = False


@dataclass
class MLConfig:
    """Machine Learning configuration"""
    model_path: str = "models/"
    data_path: str = "data/"
    feature_store_path: str = "features/"
    training_data_size: int = 10000
    validation_split: float = 0.2
    test_split: float = 0.1
    cross_validation_folds: int = 5
    model_retrain_interval: int = 86400
    performance_threshold: float = 0.8
    max_models: int = 10


@dataclass
class SystemConfig:
    """System configuration"""
    log_level: str = "INFO"
    log_file: str = "logs/trading_bot.log"
    max_log_size: int = 100 * 1024 * 1024  # 100MB
    log_retention_days: int = 30
    backup_interval: int = 3600
    backup_retention_days: int = 7
    health_check_interval: int = 60
    metrics_collection_interval: int = 10
    alert_webhook_url: Optional[str] = None
    monitoring_enabled: bool = True


@dataclass
class SecurityConfig:
    """Security configuration"""
    encryption_key: str
    jwt_secret: str
    password_salt: str
    session_timeout: int = 3600
    max_login_attempts: int = 5
    lockout_duration: int = 300
    two_factor_enabled: bool = False
    ip_whitelist: List[str] = None
    rate_limiting_enabled: bool = True
    audit_logging: bool = True


class ConfigManager:
    """
    Production Configuration Manager
    
    Features:
    - Environment-specific configuration
    - Secret management with encryption
    - Configuration validation
    - Hot-reload capability
    - Backup and versioning
    - Security and access control
    - Configuration templates
    - Environment variable integration
    - Configuration monitoring
    - Audit logging
    """
    
    def __init__(self, 
                 config_dir: str = "config",
                 environment: Environment = Environment.DEVELOPMENT,
                 encryption_key: Optional[str] = None):
        
        self.config_dir = Path(config_dir)
        self.environment = environment
        self.logger = TradingBotLogger("ConfigManager")
        
        # Create config directory if it doesn't exist
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize encryption
        self.encryption_key = encryption_key or self._generate_encryption_key()
        self.cipher_suite = self._initialize_encryption()
        
        # Configuration storage
        self.config: Dict[str, Any] = {}
        self.secrets: Dict[str, Any] = {}
        self.config_history: List[Dict[str, Any]] = []
        
        # File paths
        self.config_file = self.config_dir / f"{environment.value}.yaml"
        self.secrets_file = self.config_dir / "secrets.encrypted"
        self.template_file = self.config_dir / "template.yaml"
        self.backup_dir = self.config_dir / "backups"
        
        # Create backup directory
        self.backup_dir.mkdir(exist_ok=True)
        
        # Configuration metadata
        self.config_metadata = {
            'version': '1.0.0',
            'environment': environment.value,
            'created_at': datetime.now().isoformat(),
            'last_modified': None,
            'checksum': None
        }
        
        # Load configuration
        self._load_configuration()
    
    def _generate_encryption_key(self) -> str:
        """Generate encryption key"""
        return Fernet.generate_key().decode()
    
    def _initialize_encryption(self) -> Fernet:
        """Initialize encryption"""
        key = self.encryption_key.encode()
        return Fernet(key)
    
    def _load_configuration(self):
        """Load configuration from files"""
        try:
            # Load main configuration
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    self.config = yaml.safe_load(f) or {}
                self.logger.info(f"Loaded configuration from {self.config_file}")
            else:
                self.config = self._create_default_config()
                self._save_configuration()
            
            # Load secrets
            if self.secrets_file.exists():
                self.secrets = self._load_encrypted_secrets()
                self.logger.info("Loaded encrypted secrets")
            else:
                self.secrets = self._create_default_secrets()
                self._save_encrypted_secrets()
            
            # Merge environment variables
            self._merge_environment_variables()
            
            # Validate configuration
            self._validate_configuration()
            
            # Update metadata
            self.config_metadata['last_modified'] = datetime.now().isoformat()
            self.config_metadata['checksum'] = self._calculate_checksum()
            
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            raise
    
    def _create_default_config(self) -> Dict[str, Any]:
        """Create default configuration"""
        return {
            'database': {
                'host': 'localhost',
                'port': 5432,
                'database': 'trading_bot',
                'user': 'postgres',
                'password': '${DB_PASSWORD}',
                'ssl_mode': 'require',
                'connection_pool_size': 20,
                'max_connections': 100,
                'timeout': 30
            },
            'exchange': {
                'name': 'bybit',
                'api_key': '${BYBIT_API_KEY}',
                'secret_key': '${BYBIT_SECRET_KEY}',
                'sandbox': True,
                'testnet': True,
                'rate_limit': 100,
                'timeout': 30
            },
            'risk': {
                'max_position_size': 0.1,
                'max_daily_loss': 0.05,
                'max_drawdown': 0.2,
                'stop_loss_percentage': 0.05,
                'take_profit_percentage': 0.1,
                'risk_free_rate': 0.02,
                'var_confidence': 0.95,
                'max_leverage': 10,
                'max_correlation': 0.8,
                'emergency_stop_loss': 0.15
            },
            'trading': {
                'symbols': ['BTCUSDT', 'ETHUSDT', 'ADAUSDT'],
                'default_quantity': 100.0,
                'min_trade_size': 10.0,
                'max_trade_size': 10000.0,
                'slippage_tolerance': 0.001,
                'execution_timeout': 30,
                'order_retry_count': 3,
                'price_precision': 8,
                'quantity_precision': 8,
                'paper_trading': True
            },
            'ml': {
                'model_path': 'models/',
                'data_path': 'data/',
                'feature_store_path': 'features/',
                'training_data_size': 10000,
                'validation_split': 0.2,
                'test_split': 0.1,
                'cross_validation_folds': 5,
                'model_retrain_interval': 86400,
                'performance_threshold': 0.8,
                'max_models': 10
            },
            'system': {
                'log_level': 'INFO',
                'log_file': 'logs/trading_bot.log',
                'max_log_size': 100 * 1024 * 1024,
                'log_retention_days': 30,
                'backup_interval': 3600,
                'backup_retention_days': 7,
                'health_check_interval': 60,
                'metrics_collection_interval': 10,
                'monitoring_enabled': True
            },
            'security': {
                'encryption_key': '${ENCRYPTION_KEY}',
                'jwt_secret': '${JWT_SECRET}',
                'password_salt': '${PASSWORD_SALT}',
                'session_timeout': 3600,
                'max_login_attempts': 5,
                'lockout_duration': 300,
                'two_factor_enabled': False,
                'rate_limiting_enabled': True,
                'audit_logging': True
            },
            'features': {
                'autonomous_trading': True,
                'risk_management': True,
                'ml_prediction': True,
                'portfolio_optimization': True,
                'news_sentiment': True,
                'social_sentiment': False,
                'backtesting': True,
                'paper_trading': True,
                'live_trading': False
            }
        }
    
    def _create_default_secrets(self) -> Dict[str, Any]:
        """Create default secrets"""
        return {
            'DB_PASSWORD': 'changeme',
            'BYBIT_API_KEY': 'your_api_key_here',
            'BYBIT_SECRET_KEY': 'your_secret_key_here',
            'ENCRYPTION_KEY': self._generate_encryption_key(),
            'JWT_SECRET': secrets.token_urlsafe(32),
            'PASSWORD_SALT': secrets.token_urlsafe(16)
        }
    
    def _load_encrypted_secrets(self) -> Dict[str, Any]:
        """Load encrypted secrets"""
        try:
            with open(self.secrets_file, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.cipher_suite.decrypt(encrypted_data)
            return json.loads(decrypted_data.decode())
            
        except Exception as e:
            self.logger.error(f"Failed to load encrypted secrets: {e}")
            return {}
    
    def _save_encrypted_secrets(self):
        """Save encrypted secrets"""
        try:
            secret_data = json.dumps(self.secrets).encode()
            encrypted_data = self.cipher_suite.encrypt(secret_data)
            
            with open(self.secrets_file, 'wb') as f:
                f.write(encrypted_data)
            
            # Set restrictive permissions
            os.chmod(self.secrets_file, 0o600)
            
        except Exception as e:
            self.logger.error(f"Failed to save encrypted secrets: {e}")
            raise
    
    def _save_configuration(self):
        """Save configuration to file"""
        try:
            # Create backup
            self._create_backup()
            
            # Save configuration
            with open(self.config_file, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False, indent=2)
            
            # Update metadata
            self.config_metadata['last_modified'] = datetime.now().isoformat()
            self.config_metadata['checksum'] = self._calculate_checksum()
            
            self.logger.info(f"Configuration saved to {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
            raise
    
    def _merge_environment_variables(self):
        """Merge environment variables into configuration"""
        def replace_env_vars(obj):
            if isinstance(obj, dict):
                return {k: replace_env_vars(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [replace_env_vars(item) for item in obj]
            elif isinstance(obj, str):
                if obj.startswith('${') and obj.endswith('}'):
                    env_var = obj[2:-1]
                    if env_var in os.environ:
                        return os.environ[env_var]
                    elif env_var in self.secrets:
                        return self.secrets[env_var]
                    else:
                        self.logger.warning(f"Environment variable {env_var} not found")
                        return obj
                return obj
            else:
                return obj
        
        self.config = replace_env_vars(self.config)
    
    def _validate_configuration(self):
        """Validate configuration"""
        required_sections = ['database', 'exchange', 'risk', 'trading', 'system']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Missing required configuration section: {section}")
        
        # Validate database configuration
        db_config = self.config['database']
        required_db_fields = ['host', 'port', 'database', 'user', 'password']
        for field in required_db_fields:
            if field not in db_config:
                raise ValueError(f"Missing required database field: {field}")
        
        # Validate exchange configuration
        exchange_config = self.config['exchange']
        required_exchange_fields = ['name', 'api_key', 'secret_key']
        for field in required_exchange_fields:
            if field not in exchange_config:
                raise ValueError(f"Missing required exchange field: {field}")
        
        # Validate risk configuration
        risk_config = self.config['risk']
        if risk_config.get('max_position_size', 0) <= 0:
            raise ValueError("max_position_size must be greater than 0")
        
        if risk_config.get('max_daily_loss', 0) <= 0:
            raise ValueError("max_daily_loss must be greater than 0")
        
        self.logger.info("Configuration validation passed")
    
    def _calculate_checksum(self) -> str:
        """Calculate configuration checksum"""
        config_str = json.dumps(self.config, sort_keys=True)
        return hashlib.sha256(config_str.encode()).hexdigest()
    
    def _create_backup(self):
        """Create configuration backup"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"config_{timestamp}.yaml"
            
            if self.config_file.exists():
                import shutil
                shutil.copy2(self.config_file, backup_file)
            
            # Clean old backups
            self._clean_old_backups()
            
        except Exception as e:
            self.logger.error(f"Failed to create backup: {e}")
    
    def _clean_old_backups(self):
        """Clean old backup files"""
        try:
            backup_files = list(self.backup_dir.glob("config_*.yaml"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # Keep only latest 10 backups
            for backup_file in backup_files[10:]:
                backup_file.unlink()
                
        except Exception as e:
            self.logger.error(f"Failed to clean old backups: {e}")
    
    def get_config(self, key: str = None) -> Any:
        """Get configuration value"""
        if key is None:
            return self.config
        
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return None
        
        return value
    
    def set_config(self, key: str, value: Any):
        """Set configuration value"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        
        # Save configuration
        self._save_configuration()
        
        self.logger.info(f"Configuration updated: {key} = {value}")
    
    def get_secret(self, key: str) -> str:
        """Get secret value"""
        return self.secrets.get(key)
    
    def set_secret(self, key: str, value: str):
        """Set secret value"""
        self.secrets[key] = value
        self._save_encrypted_secrets()
        self.logger.info(f"Secret updated: {key}")
    
    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration"""
        db_config = self.config['database']
        return DatabaseConfig(**db_config)
    
    def get_exchange_config(self) -> ExchangeConfig:
        """Get exchange configuration"""
        exchange_config = self.config['exchange']
        return ExchangeConfig(**exchange_config)
    
    def get_risk_config(self) -> RiskConfig:
        """Get risk configuration"""
        risk_config = self.config['risk']
        return RiskConfig(**risk_config)
    
    def get_trading_config(self) -> TradingConfig:
        """Get trading configuration"""
        trading_config = self.config['trading']
        return TradingConfig(**trading_config)
    
    def get_ml_config(self) -> MLConfig:
        """Get ML configuration"""
        ml_config = self.config['ml']
        return MLConfig(**ml_config)
    
    def get_system_config(self) -> SystemConfig:
        """Get system configuration"""
        system_config = self.config['system']
        return SystemConfig(**system_config)
    
    def get_security_config(self) -> SecurityConfig:
        """Get security configuration"""
        security_config = self.config['security']
        return SecurityConfig(**security_config)
    
    def reload_configuration(self):
        """Reload configuration from files"""
        self.logger.info("Reloading configuration")
        self._load_configuration()
    
    def create_template(self):
        """Create configuration template"""
        template = self._create_default_config()
        
        with open(self.template_file, 'w') as f:
            yaml.dump(template, f, default_flow_style=False, indent=2)
        
        self.logger.info(f"Configuration template created: {self.template_file}")
    
    def validate_changes(self, new_config: Dict[str, Any]) -> List[str]:
        """Validate configuration changes"""
        errors = []
        
        # Check required sections
        required_sections = ['database', 'exchange', 'risk', 'trading', 'system']
        for section in required_sections:
            if section not in new_config:
                errors.append(f"Missing required section: {section}")
        
        # Validate specific fields
        if 'risk' in new_config:
            risk_config = new_config['risk']
            if risk_config.get('max_position_size', 0) <= 0:
                errors.append("max_position_size must be greater than 0")
        
        return errors
    
    def get_status(self) -> Dict[str, Any]:
        """Get configuration status"""
        return {
            'environment': self.environment.value,
            'config_file': str(self.config_file),
            'secrets_file': str(self.secrets_file),
            'metadata': self.config_metadata,
            'sections': list(self.config.keys()),
            'secrets_count': len(self.secrets),
            'last_modified': self.config_metadata.get('last_modified'),
            'checksum': self.config_metadata.get('checksum')
        }


# Global configuration instance
config_manager = None


def get_config_manager(environment: Environment = Environment.DEVELOPMENT) -> ConfigManager:
    """Get global configuration manager"""
    global config_manager
    if config_manager is None:
        config_manager = ConfigManager(environment=environment)
    return config_manager


def initialize_config(environment: Environment = Environment.DEVELOPMENT):
    """Initialize configuration"""
    global config_manager
    config_manager = ConfigManager(environment=environment)
    return config_manager
