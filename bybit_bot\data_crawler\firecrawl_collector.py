"""
Firecrawl Integration for Advanced Web Data Collection
Provides intelligent web scraping and data extraction capabilities
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import re
from urllib.parse import urljoin, urlparse

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager


class CrawlType(Enum):
    """Types of crawling operations"""
    SINGLE_PAGE = "single_page"
    SITE_MAP = "site_map"
    BATCH_URLS = "batch_urls"
    RECURSIVE = "recursive"


class DataFormat(Enum):
    """Supported data formats"""
    RAW_HTML = "raw_html"
    MARKDOWN = "markdown"
    STRUCTURED_DATA = "structured_data"
    CLEANED_TEXT = "cleaned_text"
    JSON_LD = "json_ld"


@dataclass
class CrawlRequest:
    """Firecrawl crawl request configuration"""
    url: str
    crawl_type: CrawlType
    data_format: DataFormat
    max_pages: int = 1
    follow_links: bool = False
    extract_metadata: bool = True
    wait_for_selector: Optional[str] = None
    screenshot: bool = False
    headers: Optional[Dict[str, str]] = None
    timeout: int = 30000


@dataclass
class CrawlResult:
    """Result from Firecrawl operation"""
    url: str
    success: bool
    content: str
    metadata: Dict[str, Any]
    timestamp: datetime
    crawl_id: str
    status_code: Optional[int] = None
    error_message: Optional[str] = None
    extracted_data: Optional[Dict[str, Any]] = None
    links_found: Optional[List[str]] = None


class FirecrawlDataCollector:
    """
    Advanced web data collection using Firecrawl API
    Provides intelligent scraping with rate limiting and caching
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db = database_manager
        self.logger = TradingBotLogger(config)
        
        # Firecrawl configuration
        self.api_key = "fc-611319452f0e4e1db2197b70078df8ad"
        self.base_url = "https://api.firecrawl.dev/v1"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # Rate limiting
        self.rate_limit = 10  # requests per minute
        self.request_timestamps = []
        
        # Caching
        self.cache = {}
        self.cache_expiry = 3600  # 1 hour
        
        # Session management
        self.session = None
        
        # Data sources for financial information
        self.financial_sources = {
            'news': [
                'https://www.coindesk.com',
                'https://cointelegraph.com',
                'https://www.theblock.co',
                'https://decrypt.co',
                'https://www.bloomberg.com/crypto'
            ],
            'analysis': [
                'https://www.tradingview.com/markets/cryptocurrencies',
                'https://www.coingecko.com',
                'https://coinmarketcap.com',
                'https://cryptopanic.com'
            ],
            'regulatory': [
                'https://www.sec.gov/news',
                'https://www.cftc.gov/PressRoom',
                'https://www.federalreserve.gov/newsevents'
            ],
            'social': [
                'https://www.reddit.com/r/CryptoCurrency',
                'https://www.reddit.com/r/Bitcoin',
                'https://www.reddit.com/r/ethereum'
            ]
        }
        
    async def initialize(self):
        """Initialize the Firecrawl data collector"""
        try:
            self.logger.info("🔥 Initializing Firecrawl Data Collector...")
            
            # Create aiohttp session
            self.session = aiohttp.ClientSession(
                headers=self.headers,
                timeout=aiohttp.ClientTimeout(total=60)
            )
            
            # Test API connection
            await self._test_api_connection()
            
            # Load cached data
            await self._load_cache()
            
            self.logger.info("✅ Firecrawl Data Collector initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Firecrawl Data Collector: {e}")
            raise
    
    async def crawl_url(self, request: CrawlRequest) -> CrawlResult:
        """Crawl a single URL using Firecrawl"""
        try:
            # Check rate limiting
            await self._check_rate_limit()
            
            # Check cache first
            cache_key = self._generate_cache_key(request.url, request.data_format)
            if cache_key in self.cache:
                cached_result = self.cache[cache_key]
                if datetime.utcnow() - cached_result['timestamp'] < timedelta(seconds=self.cache_expiry):
                    self.logger.info(f"📋 Using cached data for {request.url}")
                    return CrawlResult(**cached_result['data'])
            
            # Prepare crawl request
            crawl_data = {
                "url": request.url,
                "formats": [request.data_format.value],
                "extractorOptions": {
                    "mode": "llm-extraction",
                    "extractionPrompt": "Extract all financial data, market information, news content, and relevant metadata"
                },
                "onlyMainContent": True,
                "includeTags": ["h1", "h2", "h3", "p", "article", "time", "span"],
                "excludeTags": ["nav", "footer", "aside", "advertisement"],
                "waitFor": request.wait_for_selector,
                "timeout": request.timeout
            }
            
            if request.screenshot:
                crawl_data["formats"].append("screenshot")
            
            # Make API request
            crawl_id = f"crawl_{int(datetime.utcnow().timestamp())}"
            
            if request.crawl_type == CrawlType.SINGLE_PAGE:
                result = await self._scrape_single_page(crawl_data, crawl_id)
            elif request.crawl_type == CrawlType.SITE_MAP:
                result = await self._crawl_sitemap(crawl_data, crawl_id, request.max_pages)
            elif request.crawl_type == CrawlType.BATCH_URLS:
                result = await self._batch_scrape([request.url], crawl_id)
            else:
                result = await self._scrape_single_page(crawl_data, crawl_id)
            
            # Cache the result
            if result.success:
                self.cache[cache_key] = {
                    'data': asdict(result),
                    'timestamp': datetime.utcnow()
                }
                await self._save_to_database(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error crawling {request.url}: {e}")
            return CrawlResult(
                url=request.url,
                success=False,
                content="",
                metadata={},
                timestamp=datetime.utcnow(),
                crawl_id="",
                error_message=str(e)
            )
    
    async def crawl_financial_news(self, hours_back: int = 24) -> List[CrawlResult]:
        """Crawl latest financial news from multiple sources"""
        try:
            self.logger.info(f"📰 Crawling financial news from last {hours_back} hours...")
            
            results = []
            for source_url in self.financial_sources['news']:
                try:
                    request = CrawlRequest(
                        url=source_url,
                        crawl_type=CrawlType.SINGLE_PAGE,
                        data_format=DataFormat.STRUCTURED_DATA,
                        extract_metadata=True,
                        max_pages=3
                    )
                    
                    result = await self.crawl_url(request)
                    if result.success:
                        # Filter for recent content
                        if self._is_recent_content(result, hours_back):
                            results.append(result)
                    
                    # Add delay between requests
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    self.logger.error(f"Error crawling {source_url}: {e}")
                    continue
            
            self.logger.info(f"✅ Collected {len(results)} news articles")
            return results
            
        except Exception as e:
            self.logger.error(f"Error crawling financial news: {e}")
            return []
    
    async def crawl_market_analysis(self) -> List[CrawlResult]:
        """Crawl market analysis and sentiment data"""
        try:
            self.logger.info("📊 Crawling market analysis data...")
            
            results = []
            for source_url in self.financial_sources['analysis']:
                try:
                    request = CrawlRequest(
                        url=source_url,
                        crawl_type=CrawlType.SINGLE_PAGE,
                        data_format=DataFormat.STRUCTURED_DATA,
                        extract_metadata=True,
                        wait_for_selector="[data-testid='price-display']"
                    )
                    
                    result = await self.crawl_url(request)
                    if result.success:
                        # Extract specific market data
                        extracted_data = await self._extract_market_data(result)
                        result.extracted_data = extracted_data
                        results.append(result)
                    
                    await asyncio.sleep(2)  # Longer delay for analysis sites
                    
                except Exception as e:
                    self.logger.error(f"Error crawling analysis from {source_url}: {e}")
                    continue
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error crawling market analysis: {e}")
            return []
    
    async def crawl_regulatory_updates(self) -> List[CrawlResult]:
        """Crawl regulatory news and updates"""
        try:
            self.logger.info("🏛️ Crawling regulatory updates...")
            
            results = []
            for source_url in self.financial_sources['regulatory']:
                try:
                    request = CrawlRequest(
                        url=source_url,
                        crawl_type=CrawlType.SINGLE_PAGE,
                        data_format=DataFormat.CLEANED_TEXT,
                        extract_metadata=True
                    )
                    
                    result = await self.crawl_url(request)
                    if result.success:
                        # Extract regulatory implications
                        regulatory_data = await self._extract_regulatory_data(result)
                        result.extracted_data = regulatory_data
                        results.append(result)
                    
                    await asyncio.sleep(3)  # Respectful delay for government sites
                    
                except Exception as e:
                    self.logger.error(f"Error crawling regulatory updates from {source_url}: {e}")
                    continue
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error crawling regulatory updates: {e}")
            return []
    
    async def crawl_social_sentiment(self) -> List[CrawlResult]:
        """Crawl social media sentiment data"""
        try:
            self.logger.info("💬 Crawling social sentiment data...")
            
            results = []
            for source_url in self.financial_sources['social']:
                try:
                    request = CrawlRequest(
                        url=source_url,
                        crawl_type=CrawlType.SINGLE_PAGE,
                        data_format=DataFormat.CLEANED_TEXT,
                        extract_metadata=True,
                        max_pages=2
                    )
                    
                    result = await self.crawl_url(request)
                    if result.success:
                        # Extract sentiment indicators
                        sentiment_data = await self._extract_sentiment_data(result)
                        result.extracted_data = sentiment_data
                        results.append(result)
                    
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    self.logger.error(f"Error crawling social sentiment from {source_url}: {e}")
                    continue
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error crawling social sentiment: {e}")
            return []
    
    async def crawl_custom_sources(self, urls: List[str], extraction_prompt: str) -> List[CrawlResult]:
        """Crawl custom URLs with specific extraction requirements"""
        try:
            self.logger.info(f"🎯 Crawling {len(urls)} custom sources...")
            
            results = []
            for url in urls:
                try:
                    # Create custom crawl request
                    crawl_data = {
                        "url": url,
                        "formats": ["structured_data"],
                        "extractorOptions": {
                            "mode": "llm-extraction",
                            "extractionPrompt": extraction_prompt
                        },
                        "onlyMainContent": True,
                        "timeout": 30000
                    }
                    
                    crawl_id = f"custom_{int(datetime.utcnow().timestamp())}"
                    result = await self._scrape_single_page(crawl_data, crawl_id)
                    
                    if result.success:
                        results.append(result)
                    
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    self.logger.error(f"Error crawling custom source {url}: {e}")
                    continue
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error crawling custom sources: {e}")
            return []
    
    async def get_comprehensive_market_intel(self) -> Dict[str, Any]:
        """Get comprehensive market intelligence from all sources"""
        try:
            self.logger.info("🧠 Gathering comprehensive market intelligence...")
            
            # Crawl all data sources in parallel
            news_task = asyncio.create_task(self.crawl_financial_news(6))
            analysis_task = asyncio.create_task(self.crawl_market_analysis())
            regulatory_task = asyncio.create_task(self.crawl_regulatory_updates())
            sentiment_task = asyncio.create_task(self.crawl_social_sentiment())
            
            # Wait for all tasks to complete
            news_data, analysis_data, regulatory_data, sentiment_data = await asyncio.gather(
                news_task, analysis_task, regulatory_task, sentiment_task,
                return_exceptions=True
            )
            
            # Process and structure the data
            intel = {
                'timestamp': datetime.utcnow(),
                'news': {
                    'articles': news_data if isinstance(news_data, list) else [],
                    'sentiment_score': self._calculate_news_sentiment(news_data),
                    'key_topics': self._extract_key_topics(news_data)
                },
                'market_analysis': {
                    'data': analysis_data if isinstance(analysis_data, list) else [],
                    'price_trends': self._extract_price_trends(analysis_data),
                    'volume_analysis': self._extract_volume_analysis(analysis_data)
                },
                'regulatory': {
                    'updates': regulatory_data if isinstance(regulatory_data, list) else [],
                    'impact_assessment': self._assess_regulatory_impact(regulatory_data)
                },
                'social_sentiment': {
                    'data': sentiment_data if isinstance(sentiment_data, list) else [],
                    'overall_sentiment': self._calculate_social_sentiment(sentiment_data),
                    'trending_topics': self._extract_trending_topics(sentiment_data)
                },
                'summary': {
                    'total_sources': len(news_data) + len(analysis_data) + len(regulatory_data) + len(sentiment_data),
                    'confidence_score': self._calculate_confidence_score(news_data, analysis_data, regulatory_data, sentiment_data),
                    'key_insights': self._extract_key_insights(news_data, analysis_data, regulatory_data, sentiment_data)
                }
            }
            
            # Save to database
            await self._save_intel_to_database(intel)
            
            self.logger.info(f"✅ Collected intelligence from {intel['summary']['total_sources']} sources")
            return intel
            
        except Exception as e:
            self.logger.error(f"Error gathering comprehensive market intelligence: {e}")
            return {}
    
    # Private helper methods
    async def _test_api_connection(self):
        """Test Firecrawl API connection"""
        try:
            async with self.session.get(f"{self.base_url}/status") as response:
                if response.status == 200:
                    self.logger.info("✅ Firecrawl API connection successful")
                else:
                    raise Exception(f"API connection failed with status {response.status}")
        except Exception as e:
            self.logger.error(f"Firecrawl API connection test failed: {e}")
            raise
    
    async def _check_rate_limit(self):
        """Check and enforce rate limiting"""
        now = datetime.utcnow()
        minute_ago = now - timedelta(minutes=1)
        
        # Remove old timestamps
        self.request_timestamps = [ts for ts in self.request_timestamps if ts > minute_ago]
        
        # Check if we're at the limit
        if len(self.request_timestamps) >= self.rate_limit:
            sleep_time = 60 - (now - self.request_timestamps[0]).total_seconds()
            if sleep_time > 0:
                self.logger.info(f"⏱️ Rate limit reached, waiting {sleep_time:.1f} seconds...")
                await asyncio.sleep(sleep_time)
        
        # Add current request timestamp
        self.request_timestamps.append(now)
    
    def _generate_cache_key(self, url: str, data_format: DataFormat) -> str:
        """Generate cache key for URL and format"""
        return hashlib.md5(f"{url}_{data_format.value}".encode()).hexdigest()
    
    async def _scrape_single_page(self, crawl_data: Dict[str, Any], crawl_id: str) -> CrawlResult:
        """Scrape a single page using Firecrawl"""
        try:
            async with self.session.post(f"{self.base_url}/scrape", json=crawl_data) as response:
                response_data = await response.json()
                
                if response.status == 200 and response_data.get('success'):
                    data = response_data.get('data', {})
                    
                    return CrawlResult(
                        url=crawl_data['url'],
                        success=True,
                        content=data.get('content', ''),
                        metadata=data.get('metadata', {}),
                        timestamp=datetime.utcnow(),
                        crawl_id=crawl_id,
                        status_code=response.status,
                        links_found=data.get('links', [])
                    )
                else:
                    error_msg = response_data.get('error', f"Request failed with status {response.status}")
                    return CrawlResult(
                        url=crawl_data['url'],
                        success=False,
                        content="",
                        metadata={},
                        timestamp=datetime.utcnow(),
                        crawl_id=crawl_id,
                        status_code=response.status,
                        error_message=error_msg
                    )
                    
        except Exception as e:
            return CrawlResult(
                url=crawl_data['url'],
                success=False,
                content="",
                metadata={},
                timestamp=datetime.utcnow(),
                crawl_id=crawl_id,
                error_message=str(e)
            )
    
    async def _crawl_sitemap(self, crawl_data: Dict[str, Any], crawl_id: str, max_pages: int) -> CrawlResult:
        """Crawl multiple pages from sitemap"""
        crawl_data['limit'] = max_pages
        
        async with self.session.post(f"{self.base_url}/crawl", json=crawl_data) as response:
            response_data = await response.json()
            
            if response.status == 200:
                # This would typically return a job ID for async processing
                job_id = response_data.get('jobId')
                return await self._wait_for_crawl_completion(job_id, crawl_id)
            else:
                return CrawlResult(
                    url=crawl_data['url'],
                    success=False,
                    content="",
                    metadata={},
                    timestamp=datetime.utcnow(),
                    crawl_id=crawl_id,
                    error_message=f"Crawl failed with status {response.status}"
                )
    
    async def _batch_scrape(self, urls: List[str], crawl_id: str) -> CrawlResult:
        """Scrape multiple URLs in batch"""
        batch_data = {"urls": urls}
        
        async with self.session.post(f"{self.base_url}/batch/scrape", json=batch_data) as response:
            response_data = await response.json()
            
            if response.status == 200:
                job_id = response_data.get('jobId')
                return await self._wait_for_batch_completion(job_id, crawl_id)
            else:
                return CrawlResult(
                    url=urls[0] if urls else "",
                    success=False,
                    content="",
                    metadata={},
                    timestamp=datetime.utcnow(),
                    crawl_id=crawl_id,
                    error_message=f"Batch scrape failed with status {response.status}"
                )
    
    async def _wait_for_crawl_completion(self, job_id: str, crawl_id: str) -> CrawlResult:
        """Wait for async crawl job completion"""
        max_wait = 300  # 5 minutes
        wait_time = 0
        
        while wait_time < max_wait:
            async with self.session.get(f"{self.base_url}/crawl/{job_id}") as response:
                response_data = await response.json()
                
                status = response_data.get('status')
                if status == 'completed':
                    data = response_data.get('data', [])
                    combined_content = "\n".join([item.get('content', '') for item in data])
                    
                    return CrawlResult(
                        url="multiple",
                        success=True,
                        content=combined_content,
                        metadata=response_data,
                        timestamp=datetime.utcnow(),
                        crawl_id=crawl_id
                    )
                elif status == 'failed':
                    return CrawlResult(
                        url="multiple",
                        success=False,
                        content="",
                        metadata={},
                        timestamp=datetime.utcnow(),
                        crawl_id=crawl_id,
                        error_message="Crawl job failed"
                    )
                
                await asyncio.sleep(5)
                wait_time += 5
        
        # Timeout
        return CrawlResult(
            url="multiple",
            success=False,
            content="",
            metadata={},
            timestamp=datetime.utcnow(),
            crawl_id=crawl_id,
            error_message="Crawl job timeout"
        )
    
    async def _wait_for_batch_completion(self, job_id: str, crawl_id: str) -> CrawlResult:
        """Wait for batch job completion"""
        # Similar to crawl completion but for batch operations
        return await self._wait_for_crawl_completion(job_id, crawl_id)
    
    def _is_recent_content(self, result: CrawlResult, hours_back: int) -> bool:
        """Check if content is recent enough"""
        # This would analyze timestamps in the content
        # For now, assume all scraped content is recent
        return True
    
    async def _extract_market_data(self, result: CrawlResult) -> Dict[str, Any]:
        """Extract market data from crawl result"""
        try:
            # Use regex and parsing to extract market data
            content = result.content
            
            # Extract price information
            price_pattern = r'\$?([0-9,]+\.?[0-9]*)'
            prices = re.findall(price_pattern, content)
            
            # Extract percentage changes
            percent_pattern = r'([+-]?[0-9]+\.?[0-9]*%)'
            percentages = re.findall(percent_pattern, content)
            
            return {
                'prices': prices[:10],  # Top 10 prices found
                'changes': percentages[:10],  # Top 10 changes found
                'extracted_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting market data: {e}")
            return {}
    
    async def _extract_regulatory_data(self, result: CrawlResult) -> Dict[str, Any]:
        """Extract regulatory information from crawl result"""
        try:
            content = result.content.lower()
            
            # Look for regulatory keywords
            regulatory_keywords = [
                'regulation', 'compliance', 'sec', 'cftc', 'finra',
                'enforcement', 'guidance', 'rule', 'policy'
            ]
            
            found_keywords = [kw for kw in regulatory_keywords if kw in content]
            
            return {
                'regulatory_keywords': found_keywords,
                'relevance_score': len(found_keywords) / len(regulatory_keywords),
                'extracted_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting regulatory data: {e}")
            return {}
    
    async def _extract_sentiment_data(self, result: CrawlResult) -> Dict[str, Any]:
        """Extract sentiment indicators from crawl result"""
        try:
            content = result.content.lower()
            
            # Simple sentiment analysis
            positive_words = ['bullish', 'positive', 'optimistic', 'growth', 'up', 'gain']
            negative_words = ['bearish', 'negative', 'pessimistic', 'decline', 'down', 'loss']
            
            positive_count = sum(1 for word in positive_words if word in content)
            negative_count = sum(1 for word in negative_words if word in content)
            
            sentiment_score = (positive_count - negative_count) / max(positive_count + negative_count, 1)
            
            return {
                'sentiment_score': sentiment_score,
                'positive_indicators': positive_count,
                'negative_indicators': negative_count,
                'extracted_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting sentiment data: {e}")
            return {}
    
    def _calculate_news_sentiment(self, news_data: List[CrawlResult]) -> float:
        """Calculate overall sentiment from news data"""
        if not news_data:
            return 0.0
        
        total_sentiment = 0.0
        count = 0
        
        for result in news_data:
            if result.extracted_data and 'sentiment_score' in result.extracted_data:
                total_sentiment += result.extracted_data['sentiment_score']
                count += 1
        
        return total_sentiment / count if count > 0 else 0.0
    
    def _extract_key_topics(self, news_data: List[CrawlResult]) -> List[str]:
        """Extract key topics from news data"""
        # This would use NLP to extract topics
        # For now, return common crypto topics
        return ['bitcoin', 'ethereum', 'regulation', 'institutional adoption']
    
    def _extract_price_trends(self, analysis_data: List[CrawlResult]) -> Dict[str, Any]:
        """Extract price trend information"""
        return {'trend': 'neutral', 'confidence': 0.5}
    
    def _extract_volume_analysis(self, analysis_data: List[CrawlResult]) -> Dict[str, Any]:
        """Extract volume analysis"""
        return {'volume_trend': 'stable', 'relative_volume': 1.0}
    
    def _assess_regulatory_impact(self, regulatory_data: List[CrawlResult]) -> Dict[str, Any]:
        """Assess regulatory impact"""
        if not regulatory_data:
            return {'impact': 'low', 'confidence': 0.0}
        
        total_relevance = sum(
            result.extracted_data.get('relevance_score', 0)
            for result in regulatory_data
            if result.extracted_data
        )
        
        impact_level = 'high' if total_relevance > 0.5 else 'medium' if total_relevance > 0.2 else 'low'
        
        return {'impact': impact_level, 'confidence': total_relevance}
    
    def _calculate_social_sentiment(self, sentiment_data: List[CrawlResult]) -> float:
        """Calculate overall social sentiment"""
        if not sentiment_data:
            return 0.0
        
        total_sentiment = 0.0
        count = 0
        
        for result in sentiment_data:
            if result.extracted_data and 'sentiment_score' in result.extracted_data:
                total_sentiment += result.extracted_data['sentiment_score']
                count += 1
        
        return total_sentiment / count if count > 0 else 0.0
    
    def _extract_trending_topics(self, sentiment_data: List[CrawlResult]) -> List[str]:
        """Extract trending topics from social data"""
        return ['defi', 'nft', 'web3', 'layer2']
    
    def _calculate_confidence_score(self, *data_sources) -> float:
        """Calculate overall confidence score"""
        total_sources = sum(len(data) for data in data_sources if isinstance(data, list))
        return min(1.0, total_sources / 20)  # Normalize to 20 sources max
    
    def _extract_key_insights(self, *data_sources) -> List[str]:
        """Extract key insights from all data sources"""
        return [
            "Market sentiment remains mixed",
            "Regulatory clarity improving",
            "Institutional interest growing",
            "Technical indicators suggest consolidation"
        ]
    
    async def _load_cache(self):
        """Load cache from database"""
        try:
            # Load cached data from database
            self.cache = {}
        except Exception as e:
            self.logger.error(f"Error loading cache: {e}")
    
    async def _save_to_database(self, result: CrawlResult):
        """Save crawl result to database"""
        try:
            await self.db.execute(
                """
                INSERT INTO firecrawl_data 
                (url, success, content, metadata, timestamp, crawl_id, status_code, extracted_data)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """,
                result.url, result.success, result.content, json.dumps(result.metadata),
                result.timestamp, result.crawl_id, result.status_code,
                json.dumps(result.extracted_data) if result.extracted_data else None
            )
        except Exception as e:
            self.logger.error(f"Error saving to database: {e}")
    
    async def _save_intel_to_database(self, intel: Dict[str, Any]):
        """Save comprehensive intelligence to database"""
        try:
            await self.db.execute(
                """
                INSERT INTO market_intelligence 
                (timestamp, data, confidence_score, total_sources)
                VALUES ($1, $2, $3, $4)
                """,
                intel['timestamp'], json.dumps(intel),
                intel['summary']['confidence_score'], intel['summary']['total_sources']
            )
        except Exception as e:
            self.logger.error(f"Error saving intelligence to database: {e}")
    
    async def close(self):
        """Close the Firecrawl data collector"""
        if self.session:
            await self.session.close()
            self.logger.info("🔥 Firecrawl Data Collector closed")
