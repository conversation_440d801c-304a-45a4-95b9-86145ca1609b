"""added_tools_related_models

Revision ID: 7a3e336c0fba
Revises: 516ecc1c723d
Create Date: 2023-06-18 11:05:35.801505

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7a3e336c0fba'
down_revision = '1d54db311055'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('toolkits',
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('show_toolkit', sa.<PERSON>(), nullable=True),
    sa.Column('organisation_id', sa.Integer(), nullable=True),
    sa.Column('tool_code_link', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )

    op.add_column('tool_configs', sa.Column('toolkit_id', sa.Integer(), nullable=True))
    op.drop_column('tool_configs', 'name')
    op.drop_column('tool_configs', 'agent_id')
    op.add_column('tools', sa.Column('description', sa.String(), nullable=True))
    op.add_column('tools', sa.Column('toolkit_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tools', 'toolkit_id')
    op.drop_column('tools', 'description')
    op.add_column('tool_configs', sa.Column('agent_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('tool_configs', sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('tool_configs', 'toolkit_id')
    op.drop_table('toolkits')
    # ### end Alembic commands ###
