"""
Agent Orchestrator - Central coordinator for multi-agent trading system
Manages communication, task distribution, and coordination between specialized agents
"""
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from concurrent.futures import Thread<PERSON><PERSON>Executor
import json

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager
from ..ai.memory_manager import PersistentMemoryManager
from .trading_agent import TradingAgent
from .research_agent import ResearchAgent
from .risk_agent import RiskAgent
from .learning_agent import LearningAgent


class AgentStatus(Enum):
    """Agent status states"""
    IDLE = "idle"
    BUSY = "busy" 
    ERROR = "error"
    OFFLINE = "offline"
    INITIALIZING = "initializing"


class TaskPriority(Enum):
    """Task priority levels"""
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4


class MessageType(Enum):
    """Inter-agent message types"""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    DATA_SHARE = "data_share"
    ALERT = "alert"
    STATUS_UPDATE = "status_update"
    COORDINATION = "coordination"


@dataclass
class AgentTask:
    """Task structure for agent coordination"""
    task_id: str
    agent_type: str
    task_type: str
    priority: TaskPriority
    data: Dict[str, Any]
    created_at: datetime
    deadline: Optional[datetime] = None
    dependencies: List[str] = None
    callback: Optional[str] = None


@dataclass
class AgentMessage:
    """Message structure for inter-agent communication"""
    message_id: str
    sender: str
    recipient: str
    message_type: MessageType
    data: Dict[str, Any]
    timestamp: datetime
    priority: TaskPriority = TaskPriority.MEDIUM


@dataclass
class AgentInfo:
    """Agent information structure"""
    agent_id: str
    agent_type: str
    status: AgentStatus
    capabilities: List[str]
    current_tasks: List[str]
    performance_metrics: Dict[str, float]
    last_heartbeat: datetime


class AgentOrchestrator:
    """
    Central orchestrator for multi-agent trading system
    
    Features:
    - Agent lifecycle management
    - Task distribution and scheduling
    - Inter-agent communication
    - Performance monitoring
    - Conflict resolution
    - Resource allocation
    - Emergency coordination
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db_manager = database_manager
        self.logger = TradingBotLogger("AgentOrchestrator")
        
        # Agent management
        self.agents: Dict[str, Any] = {}
        self.agent_info: Dict[str, AgentInfo] = {}
        self.task_queue: List[AgentTask] = []
        self.completed_tasks: Dict[str, AgentTask] = {}
        self.message_queue: List[AgentMessage] = []
        
        # Communication and coordination
        self.message_handlers: Dict[MessageType, callable] = {}
        self.task_handlers: Dict[str, callable] = {}
        self.shared_memory: Dict[str, Any] = {}
        
        # Performance tracking
        self.performance_metrics: Dict[str, Dict[str, float]] = {}
        self.coordination_history: List[Dict[str, Any]] = []
        
        # Control flags
        self.is_running = False
        self.emergency_mode = False
        self.coordination_interval = 5  # seconds
        
        # Thread pool for concurrent operations
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # Initialize message handlers
        self._setup_message_handlers()
        
    async def initialize(self):
        """Initialize the agent orchestrator and all agents"""
        try:
            self.logger.info("Initializing Agent Orchestrator...")
            
            # Initialize memory manager for shared state
            self.memory_manager = PersistentMemoryManager(self.config, self.db_manager)
            await self.memory_manager.initialize()
            
            # Create and initialize agents
            await self._create_agents()
            await self._initialize_agents()
            
            # Start coordination loop
            self.is_running = True
            asyncio.create_task(self._coordination_loop())
            asyncio.create_task(self._health_monitor_loop())
            asyncio.create_task(self._message_processing_loop())
            
            self.logger.info("Agent Orchestrator initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Agent Orchestrator: {e}")
            raise
    
    async def _create_agents(self):
        """Create all specialized agents"""
        try:
            # Trading Agent
            self.agents['trading'] = TradingAgent(
                agent_id='trading_001',
                config=self.config,
                database_manager=self.db_manager,
                orchestrator=self
            )
            
            # Research Agent  
            self.agents['research'] = ResearchAgent(
                agent_id='research_001',
                config=self.config,
                database_manager=self.db_manager,
                orchestrator=self
            )
            
            # Risk Agent
            self.agents['risk'] = RiskAgent(
                agent_id='risk_001',
                config=self.config,
                database_manager=self.db_manager,
                orchestrator=self
            )
            
            # Learning Agent
            self.agents['learning'] = LearningAgent(
                agent_id='learning_001',
                config=self.config,
                database_manager=self.db_manager,
                orchestrator=self
            )
            
            # Initialize agent info
            for agent_type, agent in self.agents.items():
                self.agent_info[agent_type] = AgentInfo(
                    agent_id=agent.agent_id,
                    agent_type=agent_type,
                    status=AgentStatus.INITIALIZING,
                    capabilities=agent.get_capabilities(),
                    current_tasks=[],
                    performance_metrics={},
                    last_heartbeat=datetime.now()
                )
                
        except Exception as e:
            self.logger.error(f"Failed to create agents: {e}")
            raise
    
    async def _initialize_agents(self):
        """Initialize all agents"""
        for agent_type, agent in self.agents.items():
            try:
                await agent.initialize()
                
                # Set orchestrator references for AI systems in learning agent
                if agent_type == 'learning' and hasattr(agent, 'meta_cognition_engine'):
                    if agent.meta_cognition_engine:
                        agent.meta_cognition_engine.orchestrator = self
                    if agent.code_evolution_system:
                        agent.code_evolution_system.orchestrator = self
                    if agent.recursive_improvement_system:
                        agent.recursive_improvement_system.orchestrator = self
                
                self.agent_info[agent_type].status = AgentStatus.IDLE
                self.logger.info(f"Agent {agent_type} initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize agent {agent_type}: {e}")
                self.agent_info[agent_type].status = AgentStatus.ERROR
    
    def _setup_message_handlers(self):
        """Setup message handlers for different message types"""
        self.message_handlers = {
            MessageType.TASK_REQUEST: self._handle_task_request,
            MessageType.TASK_RESPONSE: self._handle_task_response,
            MessageType.DATA_SHARE: self._handle_data_share,
            MessageType.ALERT: self._handle_alert,
            MessageType.STATUS_UPDATE: self._handle_status_update,
            MessageType.COORDINATION: self._handle_coordination
        }
    
    async def _coordination_loop(self):
        """Main coordination loop"""
        while self.is_running:
            try:
                # Process pending tasks
                await self._process_task_queue()
                
                # Update agent statuses
                await self._update_agent_statuses()
                
                # Optimize resource allocation
                await self._optimize_resource_allocation()
                
                # Handle emergency situations
                if self.emergency_mode:
                    await self._handle_emergency()
                
                # Sleep until next cycle
                await asyncio.sleep(self.coordination_interval)
                
            except Exception as e:
                self.logger.error(f"Error in coordination loop: {e}")
                await asyncio.sleep(1)
    
    async def _health_monitor_loop(self):
        """Monitor agent health and performance"""
        while self.is_running:
            try:
                for agent_type, agent in self.agents.items():
                    # Check agent heartbeat
                    if hasattr(agent, 'get_status'):
                        status = await agent.get_status()
                        self.agent_info[agent_type].last_heartbeat = datetime.now()
                        
                        # Update performance metrics
                        if hasattr(agent, 'get_performance_metrics'):
                            metrics = await agent.get_performance_metrics()
                            self.agent_info[agent_type].performance_metrics = metrics
                
                # Check for stale agents
                await self._check_stale_agents()
                
                await asyncio.sleep(30)  # Health check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in health monitor: {e}")
                await asyncio.sleep(5)
    
    async def _message_processing_loop(self):
        """Process inter-agent messages"""
        while self.is_running:
            try:
                if self.message_queue:
                    message = self.message_queue.pop(0)
                    await self._process_message(message)
                
                await asyncio.sleep(0.1)  # Process messages frequently
                
            except Exception as e:
                self.logger.error(f"Error processing messages: {e}")
                await asyncio.sleep(1)
    
    async def _process_task_queue(self):
        """Process pending tasks and assign to appropriate agents"""
        if not self.task_queue:
            return
        
        # Sort tasks by priority and deadline
        self.task_queue.sort(key=lambda t: (t.priority.value, t.created_at))
        
        for task in self.task_queue[:]:
            # Find capable and available agent
            agent_type = await self._find_best_agent_for_task(task)
            
            if agent_type:
                try:
                    # Assign task to agent
                    agent = self.agents[agent_type]
                    await agent.assign_task(task)
                    
                    # Update tracking
                    self.agent_info[agent_type].current_tasks.append(task.task_id)
                    self.task_queue.remove(task)
                    
                    self.logger.info(f"Assigned task {task.task_id} to agent {agent_type}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to assign task {task.task_id}: {e}")
    
    async def _find_best_agent_for_task(self, task: AgentTask) -> Optional[str]:
        """Find the best agent to handle a specific task"""
        suitable_agents = []
        
        for agent_type, info in self.agent_info.items():
            # Check if agent can handle this task type
            if task.task_type in info.capabilities:
                # Check if agent is available
                if info.status == AgentStatus.IDLE or len(info.current_tasks) < 3:
                    suitable_agents.append((agent_type, info))
        
        if not suitable_agents:
            return None
        
        # Select best agent based on performance and load
        best_agent = min(suitable_agents, key=lambda x: (
            len(x[1].current_tasks),  # Prefer less loaded agents
            -x[1].performance_metrics.get('success_rate', 0.5)  # Prefer better performing agents
        ))
        
        return best_agent[0]
    
    async def _process_message(self, message: AgentMessage):
        """Process a single inter-agent message"""
        try:
            handler = self.message_handlers.get(message.message_type)
            if handler:
                await handler(message)
            else:
                self.logger.warning(f"No handler for message type: {message.message_type}")
                
        except Exception as e:
            self.logger.error(f"Error processing message {message.message_id}: {e}")
    
    async def _handle_task_request(self, message: AgentMessage):
        """Handle task request from an agent"""
        task_data = message.data
        task = AgentTask(
            task_id=f"task_{int(time.time() * 1000)}",
            agent_type=task_data.get('target_agent', 'any'),
            task_type=task_data['task_type'],
            priority=TaskPriority(task_data.get('priority', 3)),
            data=task_data['data'],
            created_at=datetime.now(),
            deadline=task_data.get('deadline'),
            callback=message.sender
        )
        
        self.task_queue.append(task)
        self.logger.info(f"Received task request from {message.sender}: {task.task_type}")
    
    async def _handle_task_response(self, message: AgentMessage):
        """Handle task completion response"""
        task_id = message.data['task_id']
        result = message.data['result']
        
        # Update agent task list
        for agent_type, info in self.agent_info.items():
            if task_id in info.current_tasks:
                info.current_tasks.remove(task_id)
                break
        
        # Store completed task
        if task_id in [t.task_id for t in self.task_queue]:
            task = next(t for t in self.task_queue if t.task_id == task_id)
            self.completed_tasks[task_id] = task
        
        self.logger.info(f"Task {task_id} completed by {message.sender}")
    
    async def _handle_data_share(self, message: AgentMessage):
        """Handle data sharing between agents"""
        data_key = message.data['key']
        data_value = message.data['value']
        
        self.shared_memory[data_key] = {
            'value': data_value,
            'timestamp': datetime.now(),
            'source': message.sender
        }
        
        self.logger.debug(f"Data shared by {message.sender}: {data_key}")
    
    async def _handle_alert(self, message: AgentMessage):
        """Handle alerts from agents"""
        alert_type = message.data['type']
        severity = message.data['severity']
        details = message.data['details']
        
        self.logger.warning(f"Alert from {message.sender}: {alert_type} ({severity}) - {details}")
        
        # Handle critical alerts
        if severity == 'critical':
            await self._handle_critical_alert(message)
    
    async def _handle_status_update(self, message: AgentMessage):
        """Handle status updates from agents"""
        agent_type = message.sender.split('_')[0]  # Extract agent type from ID
        new_status = AgentStatus(message.data['status'])
        
        if agent_type in self.agent_info:
            self.agent_info[agent_type].status = new_status
            self.agent_info[agent_type].last_heartbeat = datetime.now()
    
    async def _handle_coordination(self, message: AgentMessage):
        """Handle coordination messages"""
        coordination_type = message.data['type']
        
        if coordination_type == 'strategy_consensus':
            await self._handle_strategy_consensus(message)
        elif coordination_type == 'risk_assessment':
            await self._handle_risk_coordination(message)
        elif coordination_type == 'market_analysis':
            await self._handle_market_analysis_coordination(message)
    
    async def _handle_critical_alert(self, message: AgentMessage):
        """Handle critical alerts that may require emergency measures"""
        alert_type = message.data['type']
        
        if alert_type == 'system_failure':
            self.emergency_mode = True
            await self._emergency_shutdown_sequence()
        elif alert_type == 'major_loss':
            await self._emergency_risk_reduction()
        elif alert_type == 'market_crash':
            await self._emergency_position_closure()
    
    async def _update_agent_statuses(self):
        """Update and validate agent statuses"""
        for agent_type, info in self.agent_info.items():
            # Check for stale heartbeat
            time_since_heartbeat = datetime.now() - info.last_heartbeat
            if time_since_heartbeat > timedelta(minutes=2):
                info.status = AgentStatus.OFFLINE
                self.logger.warning(f"Agent {agent_type} appears offline")
    
    async def _optimize_resource_allocation(self):
        """Optimize resource allocation across agents"""
        # Analyze current workload distribution
        workloads = {agent_type: len(info.current_tasks) 
                    for agent_type, info in self.agent_info.items()}
        
        # Rebalance if needed
        max_load = max(workloads.values()) if workloads.values() else 0
        min_load = min(workloads.values()) if workloads.values() else 0
        
        if max_load - min_load > 2:  # Significant imbalance
            await self._rebalance_workload()
    
    async def _rebalance_workload(self):
        """Rebalance workload across agents"""
        # Implementation for workload rebalancing
        self.logger.info("Rebalancing workload across agents")
    
    async def _check_stale_agents(self):
        """Check for and handle stale agents"""
        current_time = datetime.now()
        
        for agent_type, info in self.agent_info.items():
            time_since_heartbeat = current_time - info.last_heartbeat
            
            if time_since_heartbeat > timedelta(minutes=5):
                self.logger.error(f"Agent {agent_type} is stale, attempting restart")
                await self._restart_agent(agent_type)
    
    async def _restart_agent(self, agent_type: str):
        """Restart a stale or failed agent"""
        try:
            agent = self.agents[agent_type]
            await agent.shutdown()
            await agent.initialize()
            
            self.agent_info[agent_type].status = AgentStatus.IDLE
            self.agent_info[agent_type].last_heartbeat = datetime.now()
            
            self.logger.info(f"Successfully restarted agent {agent_type}")
            
        except Exception as e:
            self.logger.error(f"Failed to restart agent {agent_type}: {e}")
    
    # Public interface methods
    
    async def send_message(self, sender: str, recipient: str, message_type: MessageType, 
                          data: Dict[str, Any], priority: TaskPriority = TaskPriority.MEDIUM):
        """Send a message between agents"""
        message = AgentMessage(
            message_id=f"msg_{int(time.time() * 1000)}",
            sender=sender,
            recipient=recipient,
            message_type=message_type,
            data=data,
            timestamp=datetime.now(),
            priority=priority
        )
        
        self.message_queue.append(message)
    
    async def create_task(self, task_type: str, agent_type: str, data: Dict[str, Any],
                         priority: TaskPriority = TaskPriority.MEDIUM) -> str:
        """Create a new task for an agent"""
        task = AgentTask(
            task_id=f"task_{int(time.time() * 1000)}",
            agent_type=agent_type,
            task_type=task_type,
            priority=priority,
            data=data,
            created_at=datetime.now()
        )
        
        self.task_queue.append(task)
        return task.task_id
    
    def get_shared_data(self, key: str) -> Any:
        """Get shared data by key"""
        return self.shared_memory.get(key, {}).get('value')
    
    def set_shared_data(self, key: str, value: Any, source: str = 'orchestrator'):
        """Set shared data"""
        self.shared_memory[key] = {
            'value': value,
            'timestamp': datetime.now(),
            'source': source
        }
    
    def get_agent_status(self, agent_type: str) -> Optional[AgentStatus]:
        """Get status of specific agent"""
        info = self.agent_info.get(agent_type)
        return info.status if info else None
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        return {
            'orchestrator_running': self.is_running,
            'emergency_mode': self.emergency_mode,
            'total_agents': len(self.agents),
            'active_agents': sum(1 for info in self.agent_info.values() 
                               if info.status in [AgentStatus.IDLE, AgentStatus.BUSY]),
            'pending_tasks': len(self.task_queue),
            'completed_tasks': len(self.completed_tasks),
            'agent_statuses': {agent_type: info.status.value 
                             for agent_type, info in self.agent_info.items()}
        }
    
    async def shutdown(self):
        """Gracefully shutdown the orchestrator and all agents"""
        self.logger.info("Shutting down Agent Orchestrator...")
        
        self.is_running = False
        
        # Shutdown all agents
        for agent_type, agent in self.agents.items():
            try:
                await agent.shutdown()
                self.logger.info(f"Agent {agent_type} shutdown complete")
            except Exception as e:
                self.logger.error(f"Error shutting down agent {agent_type}: {e}")
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        self.logger.info("Agent Orchestrator shutdown complete")
