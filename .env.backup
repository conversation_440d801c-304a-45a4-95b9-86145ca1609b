# Autonomous Bybit Trading Bot Configuration - PRODUCTION
# REAL MONEY TRADING ENVIRONMENT - NO TESTNET
DEBUG=False
LOG_LEVEL=INFO
LOG_FILE=E:/bybit_bot_logs/trading_bot_production.log
ENVIRONMENT=production
TRADING_MODE=live
PAPER_TRADING=False
REAL_MONEY_TRADING=True

# Database Configuration - PRODUCTION REAL DATA (SQLite for now)
DB_TYPE=sqlite
DB_PATH=E:/bybit_bot_data/bybit_trading_bot_production.db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=bybit_trading_bot
DB_USERNAME=postgres
DB_PASSWORD=password
DB_URL=sqlite:///E:/bybit_bot_data/bybit_trading_bot_production.db
DB_SSL_MODE=prefer
DB_POOL_SIZE=30
DB_MAX_OVERFLOW=100

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Bybit API Configuration - PRODUCTION MAINNET REAL MONEY
BYBIT_API_KEY=WbQDRvmESPfUGgXQEj
BYBIT_API_SECRET=vdvi3Q34C7m65rHuzFw3I9kbGeyGr4oMFUga
BYBIT_TESTNET=False
BYBIT_RECV_WINDOW=5000
BYBIT_ENVIRONMENT=mainnet
BYBIT_BASE_URL=https://api.bybit.com
BYBIT_WEBSOCKET_URL=wss://stream.bybit.com/v5/public/linear
BYBIT_PRODUCTION_MODE=True

# External API Keys for Data Crawlers
NEWS_API_KEY=your_news_api_key_here
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
FRED_API_KEY=your_fred_api_key_here
TWITTER_BEARER_TOKEN=AAAAAAAAAAAAAAAAAAAAACPo1AEAAAAA%2FkHMV0BXzYlGb9U3N2MwD5gfnAU%3D56Ql9TOVDAQxhJVAUuYX6TOzBUwjok1niLnHRHnkpmqbnQu6pW
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
REDDIT_USER_AGENT=BybitBot/1.0
TRADING_ECONOMICS_API_KEY=your_trading_economics_key_here

# Security Settings
JWT_SECRET_KEY=bybit-trading-bot-super-secure-secret-key-2024
RATE_LIMIT_PER_MINUTE=60
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Notification Settings
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here
DISCORD_WEBHOOK_URL=your_discord_webhook_url_here

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=bybit_trading_bot
DB_USER=postgres
DB_PASSWORD=your_postgres_password
DB_URL=postgresql://postgres:your_postgres_password@localhost:5432/bybit_trading_bot

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Trading Configuration
TRADING_ENABLED=True
TRADING_CYCLE_INTERVAL=30
MAX_RISK_PERCENTAGE=2.0
INITIAL_BALANCE_USD=1000.0
MIN_ORDER_SIZE=10.0
MAX_OPEN_POSITIONS=5

# Strategy Configuration
DEFAULT_STRATEGY=momentum
# Strategy parameters are configured in config.yaml file

# Risk Management
STOP_LOSS_PERCENTAGE=1.5
TAKE_PROFIT_PERCENTAGE=3.0
MAX_DRAWDOWN_PERCENTAGE=10.0
POSITION_SIZE_STRATEGY=fixed_percentage

# Hardware Monitoring
HARDWARE_CHECK_INTERVAL=60
MAX_CPU_TEMPERATURE=80
MAX_RAM_USAGE_PERCENTAGE=85
MIN_DISK_SPACE_GB=5

# Performance Analytics
PERFORMANCE_ANALYSIS_INTERVAL=3600
STRATEGY_OPTIMIZATION_INTERVAL=86400
BACKTEST_DAYS=30

# Alerts and Notifications
ALERT_ON_TRADE=True
ALERT_ON_ERROR=True
ALERT_ON_HARDWARE_ISSUE=True

# Working Directory (E Drive)
WORKING_DIRECTORY=E:/bybit_bot_workspace
DATA_DIRECTORY=E:/bybit_bot_data
LOGS_DIRECTORY=E:/bybit_bot_logs
MODELS_DIRECTORY=E:/bybit_bot_models
