<p align="center">
  <a href="https://superagi.com//#gh-light-mode-only">
    <img src="https://superagi.com/wp-content/uploads/2023/05/Logo-dark.svg" width="318px" alt="SuperAGI logo" />
  </a>
  <a href="https://superagi.com//#gh-dark-mode-only">
    <img src="https://superagi.com/wp-content/uploads/2023/05/Logo-light.svg" width="318px" alt="SuperAGI logo" />
  </a>
</p>

## SuperAGI Stable Diffusion Toolkit

Introducing Stable Diffusion Integration with SuperAGI

You can now use SuperAGI to summon Stable Diffusion to create true-to-life images which opens up a whole new range of possibilities. 

# ⚙️ Installation

## 🛠️ Setting up SuperAGI

Set up SuperAGI by following the instruction given [here](https://github.com/TransformerOptimus/SuperAGI/blob/main/README.MD)

## 🔧Configuring API from DreamStudio

You can now get your API Key from Dream Studio to use Stable Diffusion by following the instructions below: 

1. Create an Account/Login with [DreamStudio.ai](http://DreamStudio.ai)

![SD_1](https://github.com/TransformerOptimus/SuperAGI/assets/*********/39d8ab86-a989-4dfb-a281-1da6a835007e)

2. Click on the Profile Icon at the top right which will take you to the settings page. Once you have reached the settings page, you can now get your API keys 

![SD_5](https://github.com/TransformerOptimus/SuperAGI/assets/*********/f02c5d3f-6201-42ec-9acb-************)

3. Copy the API Key and save it in a separate file

## 🛠️Configuring Stable Diffusion with SuperAGI

You can configure SuperAGI with Stable Diffusion using the following steps:

1. Navigate to the “****************Toolkit”**************** Page in SuperAGI’s Dashboard and select “****************Image Generation Toolkit”**************** 

![SD_4](https://github.com/TransformerOptimus/SuperAGI/assets/*********/cc9cc93a-08a2-4613-b010-c685079e0ed3)

2. Once you’ve clicked Image Generation Toolkit, it will open a page asking you for the API Key and the Model Engine. You can enter the generated API key from Dream Studio here

![SD_2](https://github.com/TransformerOptimus/SuperAGI/assets/*********/79c667a6-ee91-4405-9b05-37d076208172)

3. If you would like to go in-depth with the model of Stable Diffusion, you can choose between the following engine IDs: 

- 'stable-diffusion-v1'
- 'stable-diffusion-v1-5'
- 'stable-diffusion-512-v2-0'
- 'stable-diffusion-768-v2-0'
- 'stable-diffusion-512-v2-1'
- ’stable-diffusion-768-v2-1'
- 'stable-diffusion-xl-beta-v2-2-2’

You have now successfully configured Stable Diffusion with SuperAGI!
