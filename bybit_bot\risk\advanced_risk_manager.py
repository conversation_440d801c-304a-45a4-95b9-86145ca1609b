"""
Advanced Risk Management Module for Bybit Trading Bot
Implements comprehensive risk controls, position sizing, and portfolio management
"""
import asyncio
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum
import pandas as pd

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager


class RiskLevel(Enum):
    """Risk levels for different market conditions"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CRITICAL = "critical"


@dataclass
class RiskMetrics:
    """Risk metrics container"""
    current_drawdown: float
    peak_equity: float
    current_equity: float
    daily_pnl: float
    total_exposure: float
    open_positions: int
    risk_score: float
    risk_level: RiskLevel
    is_trading_allowed: bool
    max_position_size: float


@dataclass
class PositionRisk:
    """Position risk assessment"""
    symbol: str
    size: float
    risk_amount: float
    risk_percentage: float
    stop_loss: Optional[float]
    take_profit: Optional[float]
    expected_holding_time: Optional[int]
    confidence: float
    market_volatility: float


class AdvancedRiskManager:
    """
    Advanced risk management system that:
    - Monitors portfolio risk in real-time
    - Calculates position sizes based on risk
    - Implements emergency stop procedures
    - Adapts to market volatility
    - Tracks performance metrics
    - Uses Kelly Criterion for optimal sizing
    - Implements correlation-based risk adjustment
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager, bybit_client):
        self.config = config
        self.db = database_manager
        self.bybit_client = bybit_client
        self.logger = TradingBotLogger(config)
        
        # Core risk parameters
        self.max_risk_per_trade = getattr(config, 'max_risk_per_trade', 2.0) / 100
        self.max_drawdown = getattr(config, 'max_drawdown', 15.0) / 100
        self.max_open_positions = getattr(config, 'max_open_positions', 5)
        self.min_order_size = getattr(config, 'min_order_size', 10.0)
        self.max_daily_loss = getattr(config, 'max_daily_loss', 500.0)
        
        # Risk tracking state
        self.current_metrics = RiskMetrics(
            current_drawdown=0.0,
            peak_equity=1000.0,  # Starting equity
            current_equity=1000.0,
            daily_pnl=0.0,
            total_exposure=0.0,
            open_positions=0,
            risk_score=0.0,
            risk_level=RiskLevel.LOW,
            is_trading_allowed=True,
            max_position_size=0.0
        )
        
        # Position tracking
        self.position_risks = {}
        self.historical_volatility = {}
        self.correlation_matrix = {}
        
        # Risk events and alerts
        self.risk_events = []
        self.emergency_stop_active = False
        self.last_risk_check = None
        
        # Adaptive risk parameters
        self.volatility_adjustment = 1.0
        self.correlation_adjustment = 1.0
        self.sentiment_adjustment = 1.0
        
        # Risk monitoring task
        self.risk_monitoring_task = None
        self.running = False
        
    async def initialize(self):
        """Initialize risk manager"""
        try:
            self.logger.info("🛡️ Initializing Advanced Risk Manager...")
            
            # Load initial risk state
            await self._load_initial_risk_state()
            
            # Calculate initial metrics
            await self._update_risk_metrics()
            
            # Start risk monitoring
            await self.start_monitoring()
            
            self.logger.info("✅ Advanced Risk Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Risk Manager: {e}")
            raise
    
    async def start_monitoring(self):
        """Start continuous risk monitoring"""
        if self.running:
            return
            
        self.running = True
        self.risk_monitoring_task = asyncio.create_task(self._risk_monitoring_loop())
        self.logger.info("🔍 Risk monitoring started")
    
    async def stop_monitoring(self):
        """Stop risk monitoring"""
        self.running = False
        
        if self.risk_monitoring_task:
            self.risk_monitoring_task.cancel()
            try:
                await self.risk_monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("🛑 Risk monitoring stopped")
    
    async def _risk_monitoring_loop(self):
        """Continuous risk monitoring loop"""
        while self.running:
            try:
                # Update risk metrics
                await self._update_risk_metrics()
                
                # Check risk limits
                risk_violations = await self._check_risk_violations()
                
                # Handle risk violations
                if risk_violations:
                    await self._handle_risk_violations(risk_violations)
                
                # Update correlations
                await self._update_correlation_matrix()
                
                # Update volatility estimates
                await self._update_volatility_estimates()
                
                # Log risk status
                await self._log_risk_status()
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in risk monitoring loop: {e}")
                await asyncio.sleep(60)
    
    async def _load_initial_risk_state(self):
        """Load initial risk state from database"""
        try:
            # Get account balance
            balance_info = await self.bybit_client.get_wallet_balance()
            if balance_info:
                total_equity = balance_info.get('totalEquity', 1000.0)
                self.current_metrics.current_equity = total_equity
                self.current_metrics.peak_equity = total_equity
            
            # Load open positions
            positions = await self.bybit_client.get_positions()
            self.current_metrics.open_positions = len([p for p in positions if p.get('size', 0) > 0])
            
            # Calculate total exposure
            total_exposure = sum(abs(float(p.get('size', 0)) * float(p.get('markPrice', 0))) 
                               for p in positions)
            self.current_metrics.total_exposure = total_exposure
            
            self.logger.info(f"Initial equity: ${self.current_metrics.current_equity:.2f}")
            self.logger.info(f"Open positions: {self.current_metrics.open_positions}")
            self.logger.info(f"Total exposure: ${total_exposure:.2f}")
            
        except Exception as e:
            self.logger.error(f"Error loading initial risk state: {e}")
    
    async def _update_risk_metrics(self):
        """Update current risk metrics"""
        try:
            # Get current account info
            balance_info = await self.bybit_client.get_wallet_balance()
            if not balance_info:
                return
            
            current_equity = float(balance_info.get('totalEquity', self.current_metrics.current_equity))
            
            # Update peak equity
            if current_equity > self.current_metrics.peak_equity:
                self.current_metrics.peak_equity = current_equity
            
            # Calculate drawdown
            drawdown = (self.current_metrics.peak_equity - current_equity) / self.current_metrics.peak_equity
            self.current_metrics.current_drawdown = drawdown
            self.current_metrics.current_equity = current_equity
            
            # Calculate daily P&L
            await self._calculate_daily_pnl()
            
            # Calculate risk score
            risk_score = self._calculate_risk_score()
            self.current_metrics.risk_score = risk_score
            self.current_metrics.risk_level = self._get_risk_level(risk_score)
            
            # Determine if trading is allowed
            self.current_metrics.is_trading_allowed = self._is_trading_allowed()
            
            # Calculate max position size
            self.current_metrics.max_position_size = self._calculate_max_position_size()
            
            self.last_risk_check = datetime.utcnow()
            
        except Exception as e:
            self.logger.error(f"Error updating risk metrics: {e}")
    
    async def _calculate_daily_pnl(self):
        """Calculate daily P&L"""
        try:
            today = datetime.utcnow().date()
            
            # Get today's trades
            query = """
            SELECT SUM(realized_pnl) as daily_pnl
            FROM trades 
            WHERE DATE(executed_at) = $1
            """
            
            result = await self.db.fetch_one(query, today)
            self.current_metrics.daily_pnl = result['daily_pnl'] if result and result['daily_pnl'] else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating daily P&L: {e}")
            self.current_metrics.daily_pnl = 0.0
    
    def _calculate_risk_score(self) -> float:
        """Calculate overall risk score (0-10)"""
        score = 0.0
        
        # Drawdown component (0-3 points)
        drawdown_score = min(3.0, (self.current_metrics.current_drawdown / self.max_drawdown) * 3.0)
        score += drawdown_score
        
        # Daily loss component (0-2 points)
        if self.current_metrics.daily_pnl < 0:
            daily_loss_score = min(2.0, (abs(self.current_metrics.daily_pnl) / self.max_daily_loss) * 2.0)
            score += daily_loss_score
        
        # Position count component (0-2 points)
        position_score = min(2.0, (self.current_metrics.open_positions / self.max_open_positions) * 2.0)
        score += position_score
        
        # Exposure component (0-2 points)
        max_exposure = self.current_metrics.current_equity * 2.0  # 2x leverage max
        exposure_score = min(2.0, (self.current_metrics.total_exposure / max_exposure) * 2.0)
        score += exposure_score
        
        # Volatility adjustment (0-1 points)
        volatility_score = min(1.0, (self.volatility_adjustment - 1.0) * 2.0)
        score += volatility_score
        
        return min(10.0, score)
    
    def _get_risk_level(self, risk_score: float) -> RiskLevel:
        """Convert risk score to risk level"""
        if risk_score < 2.0:
            return RiskLevel.VERY_LOW
        elif risk_score < 4.0:
            return RiskLevel.LOW
        elif risk_score < 6.0:
            return RiskLevel.MEDIUM
        elif risk_score < 8.0:
            return RiskLevel.HIGH
        elif risk_score < 9.0:
            return RiskLevel.VERY_HIGH
        else:
            return RiskLevel.CRITICAL
    
    def _is_trading_allowed(self) -> bool:
        """Determine if trading is allowed based on risk metrics"""
        # Emergency stop conditions
        if self.emergency_stop_active:
            return False
        
        # Drawdown limit
        if self.current_metrics.current_drawdown >= self.max_drawdown:
            return False
        
        # Daily loss limit
        if self.current_metrics.daily_pnl <= -self.max_daily_loss:
            return False
        
        # Maximum positions
        if self.current_metrics.open_positions >= self.max_open_positions:
            return False
        
        # Critical risk level
        if self.current_metrics.risk_level == RiskLevel.CRITICAL:
            return False
        
        return True
    
    def _calculate_max_position_size(self) -> float:
        """Calculate maximum position size based on current risk"""
        base_size = self.current_metrics.current_equity * self.max_risk_per_trade
        
        # Adjust for risk level
        risk_multipliers = {
            RiskLevel.VERY_LOW: 1.2,
            RiskLevel.LOW: 1.0,
            RiskLevel.MEDIUM: 0.8,
            RiskLevel.HIGH: 0.5,
            RiskLevel.VERY_HIGH: 0.3,
            RiskLevel.CRITICAL: 0.1
        }
        
        multiplier = risk_multipliers.get(self.current_metrics.risk_level, 0.5)
        adjusted_size = base_size * multiplier
        
        # Apply volatility adjustment
        adjusted_size *= (1.0 / self.volatility_adjustment)
        
        # Apply correlation adjustment
        adjusted_size *= (1.0 / self.correlation_adjustment)
        
        return max(self.min_order_size, adjusted_size)
    
    async def calculate_position_size(self, symbol: str, entry_price: float, 
                                    stop_loss: float, confidence: float = 1.0,
                                    sentiment_score: float = 0.0) -> float:
        """
        Calculate optimal position size using advanced risk management
        
        Args:
            symbol: Trading symbol
            entry_price: Planned entry price
            stop_loss: Stop loss price
            confidence: Signal confidence (0-1)
            sentiment_score: Market sentiment (-1 to 1)
        
        Returns:
            Position size in USD
        """
        try:
            if not self.current_metrics.is_trading_allowed:
                self.logger.warning(f"Trading not allowed for {symbol} due to risk limits")
                return 0.0
            
            # Calculate base position size using risk per trade
            risk_amount = abs(entry_price - stop_loss) / entry_price
            if risk_amount == 0:
                return 0.0
            
            # Base position size
            base_size = (self.current_metrics.current_equity * self.max_risk_per_trade) / risk_amount
            
            # Apply Kelly Criterion if we have historical data
            kelly_multiplier = await self._calculate_kelly_multiplier(symbol)
            base_size *= kelly_multiplier
            
            # Apply confidence adjustment
            base_size *= confidence
            
            # Apply sentiment adjustment
            sentiment_multiplier = 1.0 + (sentiment_score * 0.2)  # ±20% based on sentiment
            base_size *= sentiment_multiplier
            
            # Apply volatility adjustment
            volatility = await self._get_symbol_volatility(symbol)
            volatility_multiplier = 1.0 / (1.0 + volatility)  # Reduce size for high volatility
            base_size *= volatility_multiplier
            
            # Apply correlation adjustment
            correlation_adjustment = await self._get_correlation_adjustment(symbol)
            base_size *= correlation_adjustment
            
            # Apply risk level adjustment
            risk_multipliers = {
                RiskLevel.VERY_LOW: 1.2,
                RiskLevel.LOW: 1.0,
                RiskLevel.MEDIUM: 0.8,
                RiskLevel.HIGH: 0.6,
                RiskLevel.VERY_HIGH: 0.4,
                RiskLevel.CRITICAL: 0.2
            }
            
            risk_multiplier = risk_multipliers.get(self.current_metrics.risk_level, 0.5)
            base_size *= risk_multiplier
            
            # Ensure minimum and maximum limits
            final_size = max(self.min_order_size, min(base_size, self.current_metrics.max_position_size))
            
            # Store position risk
            position_risk = PositionRisk(
                symbol=symbol,
                size=final_size,
                risk_amount=final_size * risk_amount,
                risk_percentage=(final_size * risk_amount) / self.current_metrics.current_equity * 100,
                stop_loss=stop_loss,
                take_profit=None,
                expected_holding_time=None,
                confidence=confidence,
                market_volatility=volatility
            )
            
            self.position_risks[symbol] = position_risk
            
            self.logger.info(f"Position size for {symbol}: ${final_size:.2f} "
                           f"(Risk: {position_risk.risk_percentage:.2f}%)")
            
            return final_size
            
        except Exception as e:
            self.logger.error(f"Error calculating position size for {symbol}: {e}")
            return 0.0
    
    async def _calculate_kelly_multiplier(self, symbol: str) -> float:
        """Calculate Kelly Criterion multiplier based on historical performance"""
        try:
            # Get recent trades for this symbol
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            query = """
            SELECT realized_pnl, entry_price, exit_price
            FROM trades 
            WHERE symbol = $1 AND executed_at > $2 AND realized_pnl IS NOT NULL
            ORDER BY executed_at DESC
            LIMIT 50
            """
            
            trades = await self.db.fetch_all(query, symbol, cutoff_date)
            
            if len(trades) < 10:
                return 0.5  # Conservative default
            
            # Calculate win rate and average win/loss
            wins = [t for t in trades if t['realized_pnl'] > 0]
            losses = [t for t in trades if t['realized_pnl'] < 0]
            
            if not wins or not losses:
                return 0.5
            
            win_rate = len(wins) / len(trades)
            avg_win = sum(w['realized_pnl'] for w in wins) / len(wins)
            avg_loss = abs(sum(l['realized_pnl'] for l in losses) / len(losses))
            
            # Kelly Criterion: f = (bp - q) / b
            # where b = avg_win/avg_loss, p = win_rate, q = 1-p
            if avg_loss > 0:
                b = avg_win / avg_loss
                kelly_fraction = (b * win_rate - (1 - win_rate)) / b
                
                # Apply conservative multiplier (quarter Kelly)
                kelly_multiplier = max(0.1, min(1.0, kelly_fraction * 0.25))
                return kelly_multiplier
            
            return 0.5
            
        except Exception as e:
            self.logger.error(f"Error calculating Kelly multiplier for {symbol}: {e}")
            return 0.5
    
    async def _get_symbol_volatility(self, symbol: str) -> float:
        """Get historical volatility for symbol"""
        try:
            if symbol in self.historical_volatility:
                return self.historical_volatility[symbol]
            
            # Calculate from recent price data
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            query = """
            SELECT close_price, timestamp
            FROM market_data 
            WHERE symbol = $1 AND timestamp > $2 
            ORDER BY timestamp ASC
            """
            
            price_data = await self.db.fetch_all(query, symbol, cutoff_date)
            
            if len(price_data) < 20:
                return 0.3  # Default volatility
            
            prices = [float(p['close_price']) for p in price_data]
            returns = np.diff(np.log(prices))
            volatility = float(np.std(returns) * np.sqrt(24 * 365))  # Annualized
            
            self.historical_volatility[symbol] = volatility
            return volatility
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility for {symbol}: {e}")
            return 0.3
    
    async def _get_correlation_adjustment(self, symbol: str) -> float:
        """Get correlation adjustment for position sizing"""
        try:
            # Get correlations with existing positions
            open_positions = await self.bybit_client.get_positions()
            position_symbols = [p['symbol'] for p in open_positions if float(p.get('size', 0)) > 0]
            
            if not position_symbols:
                return 1.0  # No correlation adjustment needed
            
            # Calculate correlation with existing positions
            max_correlation = 0.0
            
            for existing_symbol in position_symbols:
                if existing_symbol != symbol:
                    correlation = await self._calculate_symbol_correlation(symbol, existing_symbol)
                    max_correlation = max(max_correlation, abs(correlation))
            
            # Reduce position size based on correlation
            correlation_adjustment = 1.0 - (max_correlation * 0.5)  # Up to 50% reduction
            return max(0.3, correlation_adjustment)  # Minimum 30%
            
        except Exception as e:
            self.logger.error(f"Error calculating correlation adjustment for {symbol}: {e}")
            return 1.0
    
    async def _calculate_symbol_correlation(self, symbol1: str, symbol2: str) -> float:
        """Calculate correlation between two symbols"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            # Get price data for both symbols
            query = """
            SELECT symbol, close_price, timestamp
            FROM market_data 
            WHERE symbol IN ($1, $2) AND timestamp > $3 
            ORDER BY timestamp ASC
            """
            
            price_data = await self.db.fetch_all(query, symbol1, symbol2, cutoff_date)
            
            # Create DataFrame
            df = pd.DataFrame(price_data)
            if len(df) < 20:
                return 0.0
            
            # Pivot and calculate returns
            pivot_df = df.pivot(index='timestamp', columns='symbol', values='close_price')
            returns = pivot_df.pct_change().dropna()
            
            if len(returns) < 10 or symbol1 not in returns.columns or symbol2 not in returns.columns:
                return 0.0
            
            correlation = returns[symbol1].corr(returns[symbol2])
            return correlation if not pd.isna(correlation) else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating correlation between {symbol1} and {symbol2}: {e}")
            return 0.0
    
    async def _check_risk_violations(self) -> List[Dict]:
        """Check for risk violations"""
        violations = []
        
        # Drawdown violation
        if self.current_metrics.current_drawdown >= self.max_drawdown:
            violations.append({
                'type': 'drawdown',
                'severity': 'critical',
                'message': f"Drawdown {self.current_metrics.current_drawdown:.2%} exceeds limit {self.max_drawdown:.2%}",
                'action_required': 'close_all_positions'
            })
        
        # Daily loss violation
        if self.current_metrics.daily_pnl <= -self.max_daily_loss:
            violations.append({
                'type': 'daily_loss',
                'severity': 'critical',
                'message': f"Daily loss ${abs(self.current_metrics.daily_pnl):.2f} exceeds limit ${self.max_daily_loss:.2f}",
                'action_required': 'stop_trading'
            })
        
        # Position count violation
        if self.current_metrics.open_positions > self.max_open_positions:
            violations.append({
                'type': 'position_count',
                'severity': 'high',
                'message': f"Open positions {self.current_metrics.open_positions} exceeds limit {self.max_open_positions}",
                'action_required': 'close_excess_positions'
            })
        
        # Risk score violation
        if self.current_metrics.risk_score >= 9.0:
            violations.append({
                'type': 'risk_score',
                'severity': 'critical',
                'message': f"Risk score {self.current_metrics.risk_score:.1f} is critical",
                'action_required': 'reduce_exposure'
            })
        
        return violations
    
    async def _handle_risk_violations(self, violations: List[Dict]):
        """Handle risk violations"""
        for violation in violations:
            self.logger.warning(f"Risk violation: {violation['message']}")
            
            # Store risk event
            risk_event = {
                'timestamp': datetime.utcnow(),
                'type': violation['type'],
                'severity': violation['severity'],
                'message': violation['message'],
                'action_taken': violation['action_required']
            }
            self.risk_events.append(risk_event)
            
            # Take action based on violation
            if violation['action_required'] == 'close_all_positions':
                await self._emergency_close_all_positions()
            elif violation['action_required'] == 'stop_trading':
                await self._activate_emergency_stop()
            elif violation['action_required'] == 'close_excess_positions':
                await self._close_excess_positions()
            elif violation['action_required'] == 'reduce_exposure':
                await self._reduce_exposure()
    
    async def _emergency_close_all_positions(self):
        """Emergency close all positions"""
        try:
            self.logger.critical("🚨 EMERGENCY: Closing all positions due to risk violation")
            
            positions = await self.bybit_client.get_positions()
            for position in positions:
                if float(position.get('size', 0)) > 0:
                    symbol = position['symbol']
                    size = float(position['size'])
                    side = 'Sell' if position['side'] == 'Buy' else 'Buy'
                    
                    # Close position
                    await self.bybit_client.place_order(
                        symbol=symbol,
                        side=side,
                        order_type='Market',
                        qty=size,
                        reduce_only=True
                    )
                    
                    self.logger.critical(f"Emergency closed position: {symbol}")
            
            self.emergency_stop_active = True
            
        except Exception as e:
            self.logger.error(f"Error in emergency close all positions: {e}")
    
    async def _activate_emergency_stop(self):
        """Activate emergency stop"""
        self.emergency_stop_active = True
        self.logger.critical("🚨 EMERGENCY STOP ACTIVATED - All trading halted")
    
    async def _close_excess_positions(self):
        """Close excess positions"""
        try:
            positions = await self.bybit_client.get_positions()
            open_positions = [p for p in positions if float(p.get('size', 0)) > 0]
            
            # Sort by unrealized PnL (close worst performing first)
            open_positions.sort(key=lambda x: float(x.get('unrealisedPnl', 0)))
            
            excess_count = len(open_positions) - self.max_open_positions
            
            for i in range(excess_count):
                position = open_positions[i]
                symbol = position['symbol']
                size = float(position['size'])
                side = 'Sell' if position['side'] == 'Buy' else 'Buy'
                
                # Close position
                await self.bybit_client.place_order(
                    symbol=symbol,
                    side=side,
                    order_type='Market',
                    qty=size,
                    reduce_only=True
                )
                
                self.logger.warning(f"Closed excess position: {symbol}")
                
        except Exception as e:
            self.logger.error(f"Error closing excess positions: {e}")
    
    async def _reduce_exposure(self):
        """Reduce overall exposure"""
        try:
            positions = await self.bybit_client.get_positions()
            open_positions = [p for p in positions if float(p.get('size', 0)) > 0]
            
            # Reduce each position by 25%
            for position in open_positions:
                symbol = position['symbol']
                current_size = float(position['size'])
                reduction_size = current_size * 0.25
                side = 'Sell' if position['side'] == 'Buy' else 'Buy'
                
                if reduction_size >= self.min_order_size:
                    await self.bybit_client.place_order(
                        symbol=symbol,
                        side=side,
                        order_type='Market',
                        qty=reduction_size,
                        reduce_only=True
                    )
                    
                    self.logger.warning(f"Reduced position size for {symbol} by 25%")
                    
        except Exception as e:
            self.logger.error(f"Error reducing exposure: {e}")
    
    async def _update_correlation_matrix(self):
        """Update correlation matrix for all trading pairs"""
        try:
            symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT", "ADAUSDT", "DOTUSDT"]
            correlations = {}
            
            for i, symbol1 in enumerate(symbols):
                for j, symbol2 in enumerate(symbols[i+1:], i+1):
                    correlation = await self._calculate_symbol_correlation(symbol1, symbol2)
                    correlations[f"{symbol1}_{symbol2}"] = correlation
            
            self.correlation_matrix = correlations
            
        except Exception as e:
            self.logger.error(f"Error updating correlation matrix: {e}")
    
    async def _update_volatility_estimates(self):
        """Update volatility estimates for all symbols"""
        try:
            symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT", "ADAUSDT", "DOTUSDT"]
            
            for symbol in symbols:
                volatility = await self._get_symbol_volatility(symbol)
                self.historical_volatility[symbol] = volatility
            
            # Calculate average volatility adjustment
            avg_volatility = np.mean(list(self.historical_volatility.values()))
            self.volatility_adjustment = 1.0 + (avg_volatility - 0.3) / 0.3  # Adjust from baseline
            
        except Exception as e:
            self.logger.error(f"Error updating volatility estimates: {e}")
    
    async def _log_risk_status(self):
        """Log current risk status"""
        if self.last_risk_check and datetime.utcnow() - self.last_risk_check < timedelta(minutes=5):
            return  # Don't log too frequently
        
        self.logger.info(
            f"Risk Status | "
            f"Level: {self.current_metrics.risk_level.value} | "
            f"Score: {self.current_metrics.risk_score:.1f}/10 | "
            f"Drawdown: {self.current_metrics.current_drawdown:.2%} | "
            f"Daily P&L: ${self.current_metrics.daily_pnl:.2f} | "
            f"Positions: {self.current_metrics.open_positions}/{self.max_open_positions} | "
            f"Trading: {'✅' if self.current_metrics.is_trading_allowed else '❌'}"
        )
    
    async def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk summary"""
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'risk_metrics': {
                'risk_level': self.current_metrics.risk_level.value,
                'risk_score': self.current_metrics.risk_score,
                'current_drawdown': self.current_metrics.current_drawdown,
                'daily_pnl': self.current_metrics.daily_pnl,
                'open_positions': self.current_metrics.open_positions,
                'total_exposure': self.current_metrics.total_exposure,
                'is_trading_allowed': self.current_metrics.is_trading_allowed,
                'emergency_stop_active': self.emergency_stop_active
            },
            'risk_limits': {
                'max_risk_per_trade': self.max_risk_per_trade,
                'max_drawdown': self.max_drawdown,
                'max_open_positions': self.max_open_positions,
                'max_daily_loss': self.max_daily_loss
            },
            'adjustments': {
                'volatility_adjustment': self.volatility_adjustment,
                'correlation_adjustment': self.correlation_adjustment,
                'sentiment_adjustment': self.sentiment_adjustment
            },
            'recent_events': self.risk_events[-10:] if self.risk_events else []
        }
    
    def reset_emergency_stop(self):
        """Reset emergency stop (manual intervention required)"""
        self.emergency_stop_active = False
        self.logger.info("Emergency stop reset - Trading can resume")
    
    async def shutdown(self):
        """Shutdown risk manager"""
        await self.stop_monitoring()
        self.logger.info("🛡️ Risk Manager shutdown complete")
