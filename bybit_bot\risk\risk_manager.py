"""
Risk Management Module for Bybit Trading Bot
Implements comprehensive risk controls and position sizing
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger
from bybit_bot.database.connection import DatabaseManager

logger = logging.getLogger("bybit_trading_bot.risk_manager")


class RiskManager:
    """
    Comprehensive risk management system for trading bot
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager, bybit_client):
        self.config = config
        self.db = database_manager
        self.bybit_client = bybit_client
        self.logger = TradingBotLogger(config)
        
        # Risk parameters
        self.max_risk_per_trade = config.max_risk_percentage / 100  # Convert percentage to decimal
        self.max_drawdown = config.max_drawdown_percentage / 100
        self.max_open_positions = config.max_open_positions
        self.min_order_size = config.min_order_size
        
        # Dynamic risk tracking
        self.current_drawdown = 0.0
        self.peak_equity = 0.0
        self.current_equity = 0.0
        self.daily_loss_limit = 0.0
        self.total_risk_exposure = 0.0
        
        # Position tracking
        self.position_sizes = {}
        self.position_risks = {}
        
        # Risk events
        self.risk_events = []
        
    async def initialize(self):
        """Initialize risk manager"""
        try:
            self.logger.info("🛡️ Initializing Risk Manager...")
            
            # Load initial equity and risk state
            await self._load_initial_risk_state()
            
            # Calculate daily loss limit
            self._calculate_daily_loss_limit()
            
            self.logger.info("✅ Risk Manager initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Risk Manager: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown risk manager"""
        try:
            # Save risk events to database
            await self._save_risk_events()
            self.logger.info("✅ Risk Manager shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error shutting down Risk Manager: {e}")
    
    async def _load_initial_risk_state(self):
        """Load initial risk state from account and database"""
        try:
            # Get current account balance
            balance = await self.bybit_client.get_account_balance()
            self.current_equity = balance.get("total_equity", 0.0)
            self.peak_equity = self.current_equity
            
            # Load historical peak equity from database
            recent_performance = await self.db.get_performance_data(days=30)
            if recent_performance:
                # Find historical peak
                for perf in recent_performance:
                    if perf.total_pnl > 0:
                        estimated_equity = self.current_equity - perf.total_pnl
                        self.peak_equity = max(self.peak_equity, estimated_equity + perf.total_pnl)
            
            # Calculate current drawdown
            if self.peak_equity > 0:
                self.current_drawdown = (self.peak_equity - self.current_equity) / self.peak_equity
            
            self.logger.info(f"💰 Initial equity: ${self.current_equity:.2f}, Peak: ${self.peak_equity:.2f}, Drawdown: {self.current_drawdown:.2%}")
            
        except Exception as e:
            self.logger.error(f"Failed to load initial risk state: {e}")
    
    def _calculate_daily_loss_limit(self):
        """Calculate daily loss limit based on account size"""
        try:
            # Daily loss limit as percentage of account
            daily_loss_percentage = 0.05  # 5% daily loss limit
            self.daily_loss_limit = self.current_equity * daily_loss_percentage
            
            self.logger.info(f"📉 Daily loss limit: ${self.daily_loss_limit:.2f}")
            
        except Exception as e:
            self.logger.error(f"Error calculating daily loss limit: {e}")
    
    async def check_risk_limits(self) -> Dict[str, Any]:
        """
        Check all risk limits and return trading permission
        """
        try:
            # Update current state
            await self._update_risk_state()
            
            risk_checks = []
            
            # 1. Maximum drawdown check
            drawdown_check = await self._check_drawdown_limit()
            risk_checks.append(drawdown_check)
            
            # 2. Daily loss limit check
            daily_loss_check = await self._check_daily_loss_limit()
            risk_checks.append(daily_loss_check)
            
            # 3. Position count limit
            position_count_check = await self._check_position_count_limit()
            risk_checks.append(position_count_check)
            
            # 4. Total risk exposure check
            exposure_check = await self._check_total_exposure()
            risk_checks.append(exposure_check)
            
            # 5. Account balance check
            balance_check = await self._check_minimum_balance()
            risk_checks.append(balance_check)
            
            # Determine overall permission
            failed_checks = [check for check in risk_checks if not check["passed"]]
            
            if failed_checks:
                reasons = [check["reason"] for check in failed_checks]
                
                # Log risk event
                self._log_risk_event(
                    event_type="TRADING_HALTED",
                    description=f"Risk limits exceeded: {', '.join(reasons)}",
                    severity="ERROR"
                )
                
                return {
                    "allowed": False,
                    "reason": f"Risk limits exceeded: {', '.join(reasons)}",
                    "failed_checks": failed_checks,
                    "all_checks": risk_checks
                }
            
            return {
                "allowed": True,
                "reason": "All risk checks passed",
                "all_checks": risk_checks
            }
            
        except Exception as e:
            self.logger.error(f"Error checking risk limits: {e}")
            return {
                "allowed": False,
                "reason": f"Risk check error: {e}",
                "error": True
            }
    
    async def _update_risk_state(self):
        """Update current risk state"""
        try:
            # Update current equity
            balance = await self.bybit_client.get_account_balance()
            new_equity = balance.get("total_equity", 0.0)
            
            # Update peak equity if we have a new high
            if new_equity > self.peak_equity:
                self.peak_equity = new_equity
            
            self.current_equity = new_equity
            
            # Update current drawdown
            if self.peak_equity > 0:
                self.current_drawdown = (self.peak_equity - self.current_equity) / self.peak_equity
            
            # Update total risk exposure
            await self._calculate_total_exposure()
            
        except Exception as e:
            self.logger.error(f"Error updating risk state: {e}")
    
    async def _check_drawdown_limit(self) -> Dict[str, Any]:
        """Check maximum drawdown limit"""
        try:
            if self.current_drawdown > self.max_drawdown:
                return {
                    "check": "max_drawdown",
                    "passed": False,
                    "reason": f"Max drawdown exceeded: {self.current_drawdown:.2%} > {self.max_drawdown:.2%}",
                    "current_value": self.current_drawdown,
                    "limit": self.max_drawdown
                }
            
            return {
                "check": "max_drawdown",
                "passed": True,
                "reason": "Drawdown within limits",
                "current_value": self.current_drawdown,
                "limit": self.max_drawdown
            }
            
        except Exception as e:
            self.logger.error(f"Error checking drawdown limit: {e}")
            return {"check": "max_drawdown", "passed": False, "reason": f"Error: {e}"}
    
    async def _check_daily_loss_limit(self) -> Dict[str, Any]:
        """Check daily loss limit"""
        try:
            # Get today's trades
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            today_trades = await self.db.get_trades(
                start_date=today_start,
                end_date=datetime.utcnow()
            )
            
            # Calculate today's P&L
            today_pnl = sum(trade.profit_loss for trade in today_trades if trade.profit_loss)
            
            if today_pnl < -self.daily_loss_limit:
                return {
                    "check": "daily_loss_limit",
                    "passed": False,
                    "reason": f"Daily loss limit exceeded: ${today_pnl:.2f} < ${-self.daily_loss_limit:.2f}",
                    "current_value": today_pnl,
                    "limit": -self.daily_loss_limit
                }
            
            return {
                "check": "daily_loss_limit",
                "passed": True,
                "reason": "Daily loss within limits",
                "current_value": today_pnl,
                "limit": -self.daily_loss_limit
            }
            
        except Exception as e:
            self.logger.error(f"Error checking daily loss limit: {e}")
            return {"check": "daily_loss_limit", "passed": False, "reason": f"Error: {e}"}
    
    async def _check_position_count_limit(self) -> Dict[str, Any]:
        """Check maximum number of open positions"""
        try:
            positions = await self.bybit_client.get_positions()
            position_count = len(positions)
            
            if position_count >= self.max_open_positions:
                return {
                    "check": "position_count",
                    "passed": False,
                    "reason": f"Max positions reached: {position_count} >= {self.max_open_positions}",
                    "current_value": position_count,
                    "limit": self.max_open_positions
                }
            
            return {
                "check": "position_count",
                "passed": True,
                "reason": "Position count within limits",
                "current_value": position_count,
                "limit": self.max_open_positions
            }
            
        except Exception as e:
            self.logger.error(f"Error checking position count: {e}")
            return {"check": "position_count", "passed": False, "reason": f"Error: {e}"}
    
    async def _check_total_exposure(self) -> Dict[str, Any]:
        """Check total risk exposure across all positions"""
        try:
            max_total_exposure = 0.8  # 80% of account
            
            if self.total_risk_exposure > max_total_exposure:
                return {
                    "check": "total_exposure",
                    "passed": False,
                    "reason": f"Total exposure too high: {self.total_risk_exposure:.2%} > {max_total_exposure:.2%}",
                    "current_value": self.total_risk_exposure,
                    "limit": max_total_exposure
                }
            
            return {
                "check": "total_exposure",
                "passed": True,
                "reason": "Total exposure within limits",
                "current_value": self.total_risk_exposure,
                "limit": max_total_exposure
            }
            
        except Exception as e:
            self.logger.error(f"Error checking total exposure: {e}")
            return {"check": "total_exposure", "passed": False, "reason": f"Error: {e}"}
    
    async def _check_minimum_balance(self) -> Dict[str, Any]:
        """Check minimum account balance"""
        try:
            min_balance = 100.0  # Minimum $100 balance
            
            if self.current_equity < min_balance:
                return {
                    "check": "minimum_balance",
                    "passed": False,
                    "reason": f"Account balance too low: ${self.current_equity:.2f} < ${min_balance:.2f}",
                    "current_value": self.current_equity,
                    "limit": min_balance
                }
            
            return {
                "check": "minimum_balance",
                "passed": True,
                "reason": "Account balance sufficient",
                "current_value": self.current_equity,
                "limit": min_balance
            }
            
        except Exception as e:
            self.logger.error(f"Error checking minimum balance: {e}")
            return {"check": "minimum_balance", "passed": False, "reason": f"Error: {e}"}
    
    async def _calculate_total_exposure(self):
        """Calculate total risk exposure across all positions"""
        try:
            positions = await self.bybit_client.get_positions()
            total_exposure = 0.0
            
            for position in positions:
                position_value = position["size"] * position["current_price"]
                total_exposure += position_value
            
            # Calculate as percentage of account
            if self.current_equity > 0:
                self.total_risk_exposure = total_exposure / self.current_equity
            else:
                self.total_risk_exposure = 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating total exposure: {e}")
            self.total_risk_exposure = 0.0
    
    async def calculate_position_size(self, symbol: str, price: float, confidence: float) -> float:
        """
        Calculate optimal position size based on risk parameters
        """
        try:
            # Base position size calculation
            risk_amount = self.current_equity * self.max_risk_per_trade
            
            # Adjust based on confidence
            confidence_multiplier = min(confidence * 2, 1.0)  # Max 1.0
            adjusted_risk = risk_amount * confidence_multiplier
            
            # Calculate position size based on stop loss
            stop_loss_distance = price * (self.config.stop_loss_percentage / 100)
            position_size = adjusted_risk / stop_loss_distance
            
            # Convert to position value
            position_value = position_size * price
            
            # Apply minimum order size
            if position_value < self.min_order_size:
                return 0.0
            
            # Check if this position would exceed risk limits
            proposed_exposure = position_value / self.current_equity
            if self.total_risk_exposure + proposed_exposure > 0.8:  # 80% max exposure
                # Reduce position size to fit within limits
                max_additional_exposure = 0.8 - self.total_risk_exposure
                position_value = max_additional_exposure * self.current_equity
                position_size = position_value / price
            
            # Final validation
            if position_value < self.min_order_size:
                return 0.0
            
            self.logger.info(
                f"📏 Position size for {symbol}: {position_size:.4f} "
                f"(value: ${position_value:.2f}, risk: ${adjusted_risk:.2f})"
            )
            
            return position_size
            
        except Exception as e:
            self.logger.error(f"Error calculating position size for {symbol}: {e}")
            return 0.0
    
    def _log_risk_event(self, event_type: str, description: str, severity: str = "INFO"):
        """Log risk management event"""
        try:
            risk_event = {
                "timestamp": datetime.utcnow(),
                "event_type": event_type,
                "description": description,
                "severity": severity,
                "current_equity": self.current_equity,
                "current_drawdown": self.current_drawdown,
                "total_exposure": self.total_risk_exposure
            }
            
            self.risk_events.append(risk_event)
            
            # Log using the logger
            self.logger.log_risk_event(event_type, description, severity)
            
        except Exception as e:
            self.logger.error(f"Error logging risk event: {e}")
    
    async def _save_risk_events(self):
        """Save risk events to database"""
        try:
            for event in self.risk_events:
                await self.db.save_system_log({
                    "level": event["severity"],
                    "component": "risk_manager",
                    "message": f"{event['event_type']}: {event['description']}",
                    "details": event,
                    "created_at": event["timestamp"]
                })
            
            # Clear events after saving
            self.risk_events.clear()
            
        except Exception as e:
            self.logger.error(f"Error saving risk events: {e}")
    
    def get_risk_status(self) -> Dict[str, Any]:
        """Get current risk status"""
        try:
            return {
                "current_equity": self.current_equity,
                "peak_equity": self.peak_equity,
                "current_drawdown": self.current_drawdown,
                "max_drawdown_limit": self.max_drawdown,
                "total_risk_exposure": self.total_risk_exposure,
                "daily_loss_limit": self.daily_loss_limit,
                "max_risk_per_trade": self.max_risk_per_trade,
                "max_open_positions": self.max_open_positions,
                "recent_risk_events": self.risk_events[-10:]  # Last 10 events
            }
            
        except Exception as e:
            self.logger.error(f"Error getting risk status: {e}")
            return {"error": str(e)}
