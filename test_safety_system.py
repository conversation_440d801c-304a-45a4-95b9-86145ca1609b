#!/usr/bin/env python3
"""
Test Production Safety System
"""

import asyncio
import logging
import sqlite3
from datetime import datetime
import psutil
import json

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('SAFETY_TEST')

async def test_safety_system():
    """Test the production safety system"""
    logger.info('🛡️ TESTING PRODUCTION SAFETY SYSTEM...')
    
    try:
        # Test database connection
        db_path = 'E:/bybit_bot_data/bybit_trading_bot_production.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create safety tables if not exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS safety_alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                level VARCHAR(20) NOT NULL,
                component VARCHAR(100) NOT NULL,
                message TEXT NOT NULL,
                details TEXT,
                action_taken TEXT,
                resolved BOOLEAN DEFAULT FALSE
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_health (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                cpu_usage DECIMAL(5,2),
                memory_usage DECIMAL(5,2),
                disk_usage DECIMAL(5,2),
                network_status VARCHAR(20),
                trading_status VARCHAR(20)
            )
        ''')
        
        # Test system monitoring
        cpu_usage = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('E:/')
        disk_usage = (disk.used / disk.total) * 100
        
        logger.info(f'📊 System Status: CPU {cpu_usage}%, Memory {memory.percent}%, Disk {disk_usage:.1f}%')
        
        # Insert test safety record
        cursor.execute('''
            INSERT INTO safety_alerts (level, component, message, action_taken, details)
            VALUES (?, ?, ?, ?, ?)
        ''', ('INFO', 'system', 'Safety system initialized and tested', 'MONITORING', 
              json.dumps({"cpu": cpu_usage, "memory": memory.percent, "disk": disk_usage})))
        
        # Insert system health record
        cursor.execute('''
            INSERT INTO system_health (cpu_usage, memory_usage, disk_usage, network_status, trading_status)
            VALUES (?, ?, ?, ?, ?)
        ''', (cpu_usage, memory.percent, disk_usage, 'CONNECTED', 'READY'))
        
        conn.commit()
        
        # Test safety thresholds
        safety_checks = {
            "CPU Usage": cpu_usage < 90,
            "Memory Usage": memory.percent < 85,
            "Disk Usage": disk_usage < 95,
            "Database Connection": True
        }
        
        logger.info('🔍 Safety Checks:')
        for check, passed in safety_checks.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            logger.info(f'  {check}: {status}')
        
        # Test circuit breaker simulation
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS circuit_breaker_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                breaker_name VARCHAR(50) NOT NULL,
                event_type VARCHAR(20) NOT NULL,
                failure_count INTEGER,
                details TEXT
            )
        ''')
        
        # Insert test circuit breaker event
        cursor.execute('''
            INSERT INTO circuit_breaker_events (breaker_name, event_type, failure_count, details)
            VALUES (?, ?, ?, ?)
        ''', ('trading', 'TEST', 0, json.dumps({"test": True, "status": "healthy"})))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info('✅ SAFETY SYSTEM TEST COMPLETE!')
        logger.info('🛡️ All safety mechanisms operational')
        logger.info('📊 Monitoring systems active')
        logger.info('🔄 Circuit breakers ready')
        
        return True
        
    except Exception as e:
        logger.error(f'❌ Safety system test failed: {e}')
        return False

if __name__ == "__main__":
    result = asyncio.run(test_safety_system())
    print(f'Safety test result: {result}')
