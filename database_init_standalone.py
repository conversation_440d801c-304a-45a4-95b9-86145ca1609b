"""
Standalone Database Setup Script for Super-GPT Trading Bot
Creates all required tables without dependency imports
"""
import sys
import logging
import yaml
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("DatabaseInit")

def load_config():
    """Load configuration from config_trading.yaml"""
    config_path = Path("config_trading.yaml")
    if config_path.exists():
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    else:
        logger.warning("config_trading.yaml not found, using default production configuration")
        return {
            'database': {
                'host': 'localhost',
                'port': 5432,
                'name': 'bybit_trading_bot',
                'user': 'postgres',
                'password': 'postgres'  # Standard postgres password
            }
        }

def create_database_schema():
    """Create the basic database schema"""
    try:
        import psycopg2
        from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
        
        config = load_config()
        db_config = config.get('database', {})
        
        # Connect to PostgreSQL server (not specific database)
        conn = psycopg2.connect(
            host=db_config.get('host', 'localhost'),
            port=db_config.get('port', 5432),
            user=db_config.get('user', 'postgres'),
            password=db_config.get('password', 'password'),
            database='postgres'  # Connect to default database first
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create database if it doesn't exist
        db_name = db_config.get('name', 'bybit_trading_bot')
        try:
            cursor.execute(f"CREATE DATABASE {db_name}")
            logger.info(f"Database {db_name} created successfully")
        except psycopg2.errors.DuplicateDatabase:
            logger.info(f"Database {db_name} already exists")
        
        cursor.close()
        conn.close()
        
        # Connect to the trading bot database
        conn = psycopg2.connect(
            host=db_config.get('host', 'localhost'),
            port=db_config.get('port', 5432),
            user=db_config.get('user', 'postgres'),
            password=db_config.get('password', 'password'),
            database=db_name
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create basic tables
        tables_sql = """
        -- Trading data tables
        CREATE TABLE IF NOT EXISTS trades (
            id SERIAL PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            side VARCHAR(10) NOT NULL,
            size DECIMAL(18,8) NOT NULL,
            price DECIMAL(18,8) NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            strategy VARCHAR(50),
            profit_loss DECIMAL(18,8),
            fees DECIMAL(18,8)
        );
        
        -- System health and errors
        CREATE TABLE IF NOT EXISTS system_health (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            cpu_usage DECIMAL(5,2),
            memory_usage DECIMAL(5,2),
            disk_usage DECIMAL(5,2),
            error_rate DECIMAL(5,2),
            component_status JSONB
        );
        
        CREATE TABLE IF NOT EXISTS error_events (
            id SERIAL PRIMARY KEY,
            event_id VARCHAR(100) UNIQUE NOT NULL,
            component VARCHAR(50) NOT NULL,
            error_type VARCHAR(100) NOT NULL,
            error_message TEXT,
            severity VARCHAR(20) NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            stack_trace TEXT,
            system_state JSONB,
            recovery_attempts INTEGER DEFAULT 0,
            resolved BOOLEAN DEFAULT FALSE
        );
        
        -- Configuration and settings
        CREATE TABLE IF NOT EXISTS bot_config (
            id SERIAL PRIMARY KEY,
            config_key VARCHAR(100) UNIQUE NOT NULL,
            config_value JSONB,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Performance metrics
        CREATE TABLE IF NOT EXISTS performance_metrics (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_trades INTEGER,
            profit_loss DECIMAL(18,8),
            win_rate DECIMAL(5,2),
            sharpe_ratio DECIMAL(10,4),
            max_drawdown DECIMAL(5,2),
            strategy_performance JSONB
        );
        """
        
        # Execute table creation
        cursor.execute(tables_sql)
        
        # Create indexes
        indexes_sql = """
        CREATE INDEX IF NOT EXISTS idx_trades_symbol ON trades(symbol);
        CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp);
        CREATE INDEX IF NOT EXISTS idx_error_events_timestamp ON error_events(timestamp);
        CREATE INDEX IF NOT EXISTS idx_error_events_component ON error_events(component);
        CREATE INDEX IF NOT EXISTS idx_system_health_timestamp ON system_health(timestamp);
        """
        cursor.execute(indexes_sql)
        
        # Insert initial configuration
        initial_config_sql = """
        INSERT INTO bot_config (config_key, config_value) VALUES
        ('system_initialized', '{"value": true, "timestamp": "' || CURRENT_TIMESTAMP || '"}'),
        ('auto_healing_enabled', '{"value": true}'),
        ('risk_limits', '{"max_position_size": 1000, "max_daily_loss": 500}')
        ON CONFLICT (config_key) DO NOTHING;
        """
        cursor.execute(initial_config_sql)
        
        cursor.close()
        conn.close()
        
        logger.info("✅ Database schema created successfully!")
        return True
        
    except ImportError:
        logger.error("❌ psycopg2 not installed. Run: pip install psycopg2-binary")
        return False
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 Starting database initialization...")
    success = create_database_schema()
    if success:
        logger.info("✅ Database initialization completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ Database initialization failed!")
        sys.exit(1)
