#!/usr/bin/env python3
"""
UNIFIED SYSTEM VALIDATOR AND LAUNCHER
Validates all components before starting the unified system
"""

import asyncio
import sys
import os
import importlib
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
import json

# Color codes for terminal output
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'


class SystemValidator:
    """Comprehensive system validator"""
    
    def __init__(self):
        self.validation_results = {}
        self.critical_errors = []
        self.warnings = []
        
    def print_header(self):
        """Print validation header"""
        print(f"\n{Colors.CYAN}{'=' * 100}{Colors.END}")
        print(f"{Colors.BOLD}{Colors.WHITE}🚀 UNIFIED AUTONOMOUS TRADING SYSTEM VALIDATOR{Colors.END}")
        print(f"{Colors.CYAN}{'=' * 100}{Colors.END}")
        print(f"{Colors.YELLOW}🔍 Validating all components before startup...{Colors.END}\n")
    
    def validate_python_environment(self) -> bool:
        """Validate Python environment"""
        print(f"{Colors.BLUE}📍 Validating Python Environment...{Colors.END}")
        
        try:
            # Check Python version
            python_version = sys.version_info
            if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
                self.critical_errors.append(f"Python 3.8+ required, found {python_version.major}.{python_version.minor}")
                print(f"  {Colors.RED}❌ Python version: {python_version.major}.{python_version.minor} (requires 3.8+){Colors.END}")
                return False
            
            print(f"  {Colors.GREEN}✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}{Colors.END}")
            
            # Check conda environment
            conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'None')
            if conda_env in ['bybit-trader', 'autogpt-trader']:
                print(f"  {Colors.GREEN}✅ Conda environment: {conda_env}{Colors.END}")
            else:
                self.warnings.append(f"Not in expected conda environment (found: {conda_env})")
                print(f"  {Colors.YELLOW}⚠️ Conda environment: {conda_env} (expected bybit-trader or autogpt-trader){Colors.END}")
            
            return True
            
        except Exception as e:
            self.critical_errors.append(f"Python environment validation failed: {e}")
            print(f"  {Colors.RED}❌ Error: {e}{Colors.END}")
            return False
    
    def validate_dependencies(self) -> bool:
        """Validate all required dependencies"""
        print(f"\n{Colors.BLUE}📦 Validating Dependencies...{Colors.END}")
        
        required_packages = {
            # Core dependencies
            'fastapi': 'FastAPI framework',
            'uvicorn': 'ASGI server',
            'asyncio': 'Async programming',
            'aiohttp': 'Async HTTP client',
            'websockets': 'WebSocket support',
            
            # Database
            'asyncpg': 'PostgreSQL async driver',
            'sqlalchemy': 'ORM',
            'alembic': 'Database migrations',
            
            # AI/ML
            'torch': 'PyTorch',
            'transformers': 'Hugging Face transformers',
            'scikit-learn': 'Machine learning',
            'numpy': 'Numerical computing',
            'pandas': 'Data manipulation',
            
            # Trading
            'ccxt': 'Crypto exchange library',
            'ta': 'Technical analysis',
            'pybit': 'Bybit API client',
            
            # Utilities
            'pydantic': 'Data validation',
            'python-dotenv': 'Environment variables',
            'requests': 'HTTP library',
            'redis': 'Redis client',
            'celery': 'Task queue',
            'psutil': 'System monitoring'
        }
        
        missing_packages = []
        
        for package, description in required_packages.items():
            try:
                importlib.import_module(package.replace('-', '_'))
                print(f"  {Colors.GREEN}✅ {package}: {description}{Colors.END}")
            except ImportError:
                missing_packages.append(package)
                print(f"  {Colors.RED}❌ {package}: Missing - {description}{Colors.END}")
        
        if missing_packages:
            self.critical_errors.append(f"Missing packages: {', '.join(missing_packages)}")
            return False
        
        return True
    
    def validate_core_modules(self) -> bool:
        """Validate core system modules"""
        print(f"\n{Colors.BLUE}🧩 Validating Core Modules...{Colors.END}")
        
        core_modules = {
            'bybit_bot.core.config': 'Bot configuration',
            'bybit_bot.core.logger': 'Logging system',
            'bybit_bot.core.bot_manager': 'Bot manager',
            'bybit_bot.database.connection': 'Database connection',
            'bybit_bot.exchange.enhanced_bybit_client': 'Enhanced Bybit client',
            'bybit_bot.agents.agent_orchestrator': 'Agent orchestrator',
        }
        
        missing_modules = []
        
        for module, description in core_modules.items():
            try:
                importlib.import_module(module)
                print(f"  {Colors.GREEN}✅ {module}: {description}{Colors.END}")
            except ImportError as e:
                missing_modules.append(module)
                print(f"  {Colors.RED}❌ {module}: Missing - {description} ({e}){Colors.END}")
        
        if missing_modules:
            self.critical_errors.append(f"Missing core modules: {', '.join(missing_modules)}")
            return False
        
        return True
    
    def validate_ai_systems(self) -> bool:
        """Validate AI system modules"""
        print(f"\n{Colors.BLUE}🧠 Validating AI Systems...{Colors.END}")
        
        ai_modules = {
            'bybit_bot.ai.memory_manager': 'Memory manager',
            'bybit_bot.ai.meta_cognition_engine': 'Meta-cognition engine',
            'bybit_bot.ai.self_correcting_code_evolution': 'Code evolution system',
            'bybit_bot.ai.recursive_improvement_system': 'Recursive improvement',
        }
        
        missing_modules = []
        
        for module, description in ai_modules.items():
            try:
                importlib.import_module(module)
                print(f"  {Colors.GREEN}✅ {module}: {description}{Colors.END}")
            except ImportError as e:
                missing_modules.append(module)
                print(f"  {Colors.RED}❌ {module}: Missing - {description} ({e}){Colors.END}")
        
        if missing_modules:
            self.critical_errors.append(f"Missing AI modules: {', '.join(missing_modules)}")
            return False
        
        return True
    
    def validate_profit_systems(self) -> bool:
        """Validate profit generation systems"""
        print(f"\n{Colors.BLUE}💰 Validating Profit Systems...{Colors.END}")
        
        profit_modules = {
            'bybit_bot.profit_maximization.advanced_profit_engine': 'Advanced profit engine',
            'bybit_bot.profit_maximization.hyper_profit_engine': 'Hyper profit engine',
        }
        
        missing_modules = []
        
        for module, description in profit_modules.items():
            try:
                importlib.import_module(module)
                print(f"  {Colors.GREEN}✅ {module}: {description}{Colors.END}")
            except ImportError as e:
                missing_modules.append(module)
                print(f"  {Colors.RED}❌ {module}: Missing - {description} ({e}){Colors.END}")
        
        if missing_modules:
            self.critical_errors.append(f"Missing profit modules: {', '.join(missing_modules)}")
            return False
        
        return True
    
    def validate_configuration(self) -> bool:
        """Validate configuration files"""
        print(f"\n{Colors.BLUE}⚙️ Validating Configuration...{Colors.END}")
        
        config_files = [
            'config.yaml',
            'config_trading.yaml',
            'requirements.txt'
        ]
        
        missing_files = []
        
        for config_file in config_files:
            if Path(config_file).exists():
                print(f"  {Colors.GREEN}✅ {config_file}: Found{Colors.END}")
            else:
                missing_files.append(config_file)
                print(f"  {Colors.YELLOW}⚠️ {config_file}: Missing (will use defaults){Colors.END}")
        
        # Check environment variables
        required_env_vars = ['BYBIT_API_KEY', 'BYBIT_API_SECRET']
        missing_env_vars = []
        
        for env_var in required_env_vars:
            if os.environ.get(env_var):
                print(f"  {Colors.GREEN}✅ {env_var}: Set{Colors.END}")
            else:
                missing_env_vars.append(env_var)
                print(f"  {Colors.YELLOW}⚠️ {env_var}: Not set (paper trading mode){Colors.END}")
        
        if missing_env_vars:
            self.warnings.append(f"Missing environment variables: {', '.join(missing_env_vars)}")
        
        return True
    
    def validate_directories(self) -> bool:
        """Validate required directories"""
        print(f"\n{Colors.BLUE}📁 Validating Directories...{Colors.END}")
        
        required_dirs = [
            'bybit_bot',
            'bybit_bot/ai',
            'bybit_bot/agents', 
            'bybit_bot/core',
            'bybit_bot/database',
            'bybit_bot/exchange',
            'bybit_bot/profit_maximization'
        ]
        
        missing_dirs = []
        
        for directory in required_dirs:
            if Path(directory).exists():
                print(f"  {Colors.GREEN}✅ {directory}: Exists{Colors.END}")
            else:
                missing_dirs.append(directory)
                print(f"  {Colors.RED}❌ {directory}: Missing{Colors.END}")
        
        if missing_dirs:
            self.critical_errors.append(f"Missing directories: {', '.join(missing_dirs)}")
            return False
        
        return True
    
    def validate_database_connectivity(self) -> bool:
        """Validate database connectivity (basic check)"""
        print(f"\n{Colors.BLUE}🗄️ Validating Database Setup...{Colors.END}")
        
        try:
            # Try to import database modules
            from bybit_bot.database.connection import DatabaseManager
            from bybit_bot.core.config import BotConfig
            
            print(f"  {Colors.GREEN}✅ Database modules: Available{Colors.END}")
            return True
            
        except ImportError as e:
            self.critical_errors.append(f"Database modules missing: {e}")
            print(f"  {Colors.RED}❌ Database modules: {e}{Colors.END}")
            return False
    
    def check_system_resources(self) -> bool:
        """Check system resources"""
        print(f"\n{Colors.BLUE}🖥️ Checking System Resources...{Colors.END}")
        
        try:
            import psutil
            
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            print(f"  {Colors.GREEN}✅ CPU usage: {cpu_percent}%{Colors.END}")
            
            # Check memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            print(f"  {Colors.GREEN}✅ Memory usage: {memory_percent}%{Colors.END}")
            
            # Check disk space
            disk = psutil.disk_usage('.')
            disk_percent = (disk.used / disk.total) * 100
            print(f"  {Colors.GREEN}✅ Disk usage: {disk_percent:.1f}%{Colors.END}")
            
            # Warnings for high usage
            if cpu_percent > 90:
                self.warnings.append(f"High CPU usage: {cpu_percent}%")
            if memory_percent > 90:
                self.warnings.append(f"High memory usage: {memory_percent}%")
            if disk_percent > 90:
                self.warnings.append(f"High disk usage: {disk_percent:.1f}%")
            
            return True
            
        except Exception as e:
            self.warnings.append(f"System resource check failed: {e}")
            print(f"  {Colors.YELLOW}⚠️ System resources: {e}{Colors.END}")
            return True  # Non-critical
    
    def run_comprehensive_validation(self) -> bool:
        """Run all validation checks"""
        self.print_header()
        
        validation_steps = [
            ("Python Environment", self.validate_python_environment),
            ("Dependencies", self.validate_dependencies),
            ("Core Modules", self.validate_core_modules),
            ("AI Systems", self.validate_ai_systems),
            ("Profit Systems", self.validate_profit_systems),
            ("Configuration", self.validate_configuration),
            ("Directories", self.validate_directories),
            ("Database Setup", self.validate_database_connectivity),
            ("System Resources", self.check_system_resources)
        ]
        
        all_passed = True
        
        for step_name, validation_func in validation_steps:
            try:
                result = validation_func()
                if not result:
                    all_passed = False
            except Exception as e:
                self.critical_errors.append(f"{step_name} validation failed: {e}")
                print(f"  {Colors.RED}❌ {step_name}: Exception - {e}{Colors.END}")
                all_passed = False
        
        return all_passed
    
    def print_summary(self, validation_passed: bool):
        """Print validation summary"""
        print(f"\n{Colors.CYAN}{'=' * 100}{Colors.END}")
        print(f"{Colors.BOLD}{Colors.WHITE}📋 VALIDATION SUMMARY{Colors.END}")
        print(f"{Colors.CYAN}{'=' * 100}{Colors.END}")
        
        if validation_passed:
            print(f"{Colors.GREEN}{Colors.BOLD}✅ ALL CRITICAL VALIDATIONS PASSED{Colors.END}")
        else:
            print(f"{Colors.RED}{Colors.BOLD}❌ CRITICAL VALIDATION FAILURES DETECTED{Colors.END}")
        
        if self.critical_errors:
            print(f"\n{Colors.RED}🚨 CRITICAL ERRORS:{Colors.END}")
            for error in self.critical_errors:
                print(f"  {Colors.RED}• {error}{Colors.END}")
        
        if self.warnings:
            print(f"\n{Colors.YELLOW}⚠️ WARNINGS:{Colors.END}")
            for warning in self.warnings:
                print(f"  {Colors.YELLOW}• {warning}{Colors.END}")
        
        print(f"\n{Colors.CYAN}{'=' * 100}{Colors.END}")


async def launch_unified_system():
    """Launch the unified system"""
    print(f"\n{Colors.MAGENTA}🚀 LAUNCHING UNIFIED AUTONOMOUS TRADING SYSTEM...{Colors.END}")
    
    try:
        # Import and run the unified system
        from main_unified_system import main
        
        print(f"{Colors.GREEN}✅ Starting unified system...{Colors.END}")
        main()
        
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}⚠️ Shutdown requested by user{Colors.END}")
    except Exception as e:
        print(f"\n{Colors.RED}❌ Failed to start unified system: {e}{Colors.END}")
        sys.exit(1)


def main():
    """Main entry point"""
    validator = SystemValidator()
    
    # Run comprehensive validation
    validation_passed = validator.run_comprehensive_validation()
    validator.print_summary(validation_passed)
    
    if not validation_passed:
        print(f"\n{Colors.RED}❌ CANNOT START SYSTEM - CRITICAL VALIDATION FAILURES{Colors.END}")
        print(f"{Colors.YELLOW}💡 Please fix the critical errors and try again{Colors.END}")
        sys.exit(1)
    
    # Ask user if they want to proceed
    print(f"\n{Colors.CYAN}🤔 Ready to launch the Unified Autonomous Trading System?{Colors.END}")
    
    if validator.warnings:
        print(f"{Colors.YELLOW}⚠️ There are {len(validator.warnings)} warnings. Continue anyway? (y/N): {Colors.END}", end="")
    else:
        print(f"{Colors.GREEN}✅ All validations passed. Launch system? (Y/n): {Colors.END}", end="")
    
    response = input().strip().lower()
    
    if response in ['y', 'yes', ''] and not validator.warnings:
        # Launch the system
        asyncio.run(launch_unified_system())
    elif response in ['y', 'yes'] and validator.warnings:
        # Launch despite warnings
        print(f"{Colors.YELLOW}⚠️ Launching with warnings...{Colors.END}")
        asyncio.run(launch_unified_system())
    else:
        print(f"{Colors.BLUE}👋 Launch cancelled by user{Colors.END}")
        sys.exit(0)


if __name__ == "__main__":
    main()
