@echo off
REM ============================================================================
REM UNIFIED AUTONOMOUS TRADING SYSTEM - MASTER STARTUP SCRIPT
REM ============================================================================
REM This is the SINGLE entry point for the complete autonomous trading system
REM Includes: SuperGPT, AI Systems, Profit Engines, Multi-Agent Orchestration
REM ============================================================================

title Unified Autonomous Trading System - Master Launcher

echo.
echo ================================================================================
echo 🚀 UNIFIED AUTONOMOUS TRADING SYSTEM - MASTER LAUNCHER
echo ================================================================================
echo 🤖 Version: 4.0.0 - Complete AI Integration
echo 🧠 Features: SuperGPT + Meta-Cognition + Self-Evolution + Hyper Profit
echo 📊 Operation: Fully Autonomous with Zero Manual Intervention
echo ================================================================================
echo.

REM Change to script directory
cd /d "%~dp0"

REM Check if conda is available
where conda >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ ERROR: Conda not found in PATH
    echo 💡 Please install Anaconda/Miniconda and add it to PATH
    pause
    exit /b 1
)

REM Check if we're in the correct directory
if not exist "main_unified_system.py" (
    echo ❌ ERROR: main_unified_system.py not found
    echo 💡 Please run this script from the BOT directory
    pause
    exit /b 1
)

REM Activate conda environment
echo 🔧 Activating conda environment...
call conda activate bybit-trader
if %errorlevel% neq 0 (
    echo ⚠️ WARNING: Failed to activate bybit-trader environment
    echo 💡 Trying autogpt-trader environment...
    call conda activate autogpt-trader
    if %errorlevel% neq 0 (
        echo ❌ ERROR: Neither bybit-trader nor autogpt-trader environment found
        echo 💡 Please create conda environment first
        pause
        exit /b 1
    )
)

echo ✅ Conda environment activated: %CONDA_DEFAULT_ENV%

REM Check Python version
echo 🐍 Checking Python version...
python --version
if %errorlevel% neq 0 (
    echo ❌ ERROR: Python not available
    pause
    exit /b 1
)

REM Install/update dependencies if needed
echo 📦 Checking dependencies...
if exist "requirements.txt" (
    echo 🔄 Installing/updating dependencies...
    pip install -r requirements.txt --quiet
    if %errorlevel% neq 0 (
        echo ⚠️ WARNING: Some dependencies may have failed to install
    ) else (
        echo ✅ Dependencies updated
    )
) else (
    echo ⚠️ WARNING: requirements.txt not found
)

REM Create necessary directories
echo 📁 Creating directories...
if not exist "logs" mkdir logs
if not exist "logs\system" mkdir logs\system
if not exist "logs\trading" mkdir logs\trading
if not exist "logs\ai_systems" mkdir logs\ai_systems
if not exist "logs\agents" mkdir logs\agents
if not exist "logs\performance" mkdir logs\performance
if not exist "logs\errors" mkdir logs\errors
if not exist "models" mkdir models
if not exist "data" mkdir data
if not exist "backups" mkdir backups
echo ✅ Directories created

REM Run system validation and launch
echo.
echo ================================================================================
echo 🔍 RUNNING COMPREHENSIVE SYSTEM VALIDATION
echo ================================================================================
echo.

python launch_unified_system.py

REM Check if validation and launch was successful
if %errorlevel% neq 0 (
    echo.
    echo ❌ System validation failed or launch was cancelled
    echo 💡 Please check the error messages above and fix any issues
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo.
echo ================================================================================
echo 🎉 UNIFIED SYSTEM LAUNCH SEQUENCE COMPLETED
echo ================================================================================
echo.
echo Press any key to exit...
pause >nul
