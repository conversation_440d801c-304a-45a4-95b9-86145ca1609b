"""
Advanced Time Management System for Bybit Trading Bot
Implements precise time awareness, market session detection, and time-based analytics
"""

import asyncio
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
from zoneinfo import ZoneInfo
import holidays

from .config import BotConfig
from .logger import TradingBotLogger
from ..database.connection import DatabaseManager


class MarketSession(Enum):
    """Market trading sessions"""
    ASIAN = "asian"
    EUROPEAN = "european"
    US = "us"
    OVERLAP_ASIAN_EUROPEAN = "asian_european"
    OVERLAP_EUROPEAN_US = "european_us"
    QUIET = "quiet"


class TimeOfDay(Enum):
    """Time periods for market activity"""
    EARLY_MORNING = "early_morning"
    MORNING = "morning"
    MIDDAY = "midday"
    AFTERNOON = "afternoon"
    EVENING = "evening"
    NIGHT = "night"
    LATE_NIGHT = "late_night"


@dataclass
class MarketActivityProfile:
    """Market activity profile for different time periods"""
    session: MarketSession
    time_of_day: TimeOfDay
    volatility_multiplier: float
    volume_multiplier: float
    spread_multiplier: float
    optimal_for_trading: bool
    recommended_strategies: List[str]


@dataclass
class TimeAwareMetrics:
    """Time-aware trading metrics"""
    current_time_utc: datetime
    current_time_precise: float  # Unix timestamp with microseconds
    market_session: MarketSession
    time_of_day: TimeOfDay
    is_market_active: bool
    is_bank_holiday: bool
    session_start_time: Optional[datetime]
    session_end_time: Optional[datetime]
    time_until_next_session: Optional[timedelta]
    activity_profile: MarketActivityProfile
    position_holding_duration: Dict[str, float]  # Symbol -> hours held


class AdvancedTimeManager:
    """
    Advanced time management system that provides:
    - Microsecond precision timing
    - Market session awareness
    - Bank holiday detection
    - Time-based strategy optimization
    - Position duration tracking
    - Market activity pattern analysis
    """
    
    def __init__(self, config: BotConfig, database_manager: DatabaseManager):
        self.config = config
        self.db = database_manager
        self.logger = TradingBotLogger(config)
        
        # Time zones
        self.utc_tz = timezone.utc
        self.tokyo_tz = ZoneInfo("Asia/Tokyo")
        self.london_tz = ZoneInfo("Europe/London")
        self.ny_tz = ZoneInfo("America/New_York")
        
        # Market session times (UTC)
        self.session_times = {
            MarketSession.ASIAN: (0, 9),  # 00:00 - 09:00 UTC
            MarketSession.EUROPEAN: (7, 16),  # 07:00 - 16:00 UTC
            MarketSession.US: (13, 22),  # 13:00 - 22:00 UTC
        }
        
        # Holiday calendars
        self.holidays = {
            'US': holidays.UnitedStates(),
            'UK': holidays.UnitedKingdom(),
            'JP': holidays.Japan(),
            'EU': holidays.Germany()  # Using Germany as EU proxy
        }
        
        # Activity profiles
        self.activity_profiles = self._initialize_activity_profiles()
        
        # Position tracking
        self.position_start_times = {}
        self.historical_patterns = {}
        
        # Performance tracking
        self.time_based_performance = {}
        
    def _initialize_activity_profiles(self) -> Dict[Tuple[MarketSession, TimeOfDay], MarketActivityProfile]:
        """Initialize market activity profiles"""
        profiles = {}
        
        # Asian session profiles
        profiles[(MarketSession.ASIAN, TimeOfDay.EARLY_MORNING)] = MarketActivityProfile(
            session=MarketSession.ASIAN,
            time_of_day=TimeOfDay.EARLY_MORNING,
            volatility_multiplier=0.8,
            volume_multiplier=0.6,
            spread_multiplier=1.2,
            optimal_for_trading=False,
            recommended_strategies=["mean_reversion"]
        )
        
        profiles[(MarketSession.ASIAN, TimeOfDay.MORNING)] = MarketActivityProfile(
            session=MarketSession.ASIAN,
            time_of_day=TimeOfDay.MORNING,
            volatility_multiplier=1.1,
            volume_multiplier=1.0,
            spread_multiplier=1.0,
            optimal_for_trading=True,
            recommended_strategies=["momentum", "trend_following"]
        )
        
        # European session profiles
        profiles[(MarketSession.EUROPEAN, TimeOfDay.MORNING)] = MarketActivityProfile(
            session=MarketSession.EUROPEAN,
            time_of_day=TimeOfDay.MORNING,
            volatility_multiplier=1.3,
            volume_multiplier=1.2,
            spread_multiplier=0.9,
            optimal_for_trading=True,
            recommended_strategies=["momentum", "trend_following"]
        )
        
        profiles[(MarketSession.EUROPEAN, TimeOfDay.MIDDAY)] = MarketActivityProfile(
            session=MarketSession.EUROPEAN,
            time_of_day=TimeOfDay.MIDDAY,
            volatility_multiplier=1.0,
            volume_multiplier=0.9,
            spread_multiplier=1.0,
            optimal_for_trading=True,
            recommended_strategies=["mean_reversion", "momentum"]
        )
        
        # US session profiles
        profiles[(MarketSession.US, TimeOfDay.AFTERNOON)] = MarketActivityProfile(
            session=MarketSession.US,
            time_of_day=TimeOfDay.AFTERNOON,
            volatility_multiplier=1.4,
            volume_multiplier=1.3,
            spread_multiplier=0.8,
            optimal_for_trading=True,
            recommended_strategies=["momentum", "trend_following"]
        )
        
        profiles[(MarketSession.US, TimeOfDay.EVENING)] = MarketActivityProfile(
            session=MarketSession.US,
            time_of_day=TimeOfDay.EVENING,
            volatility_multiplier=1.2,
            volume_multiplier=1.1,
            spread_multiplier=0.9,
            optimal_for_trading=True,
            recommended_strategies=["momentum", "mean_reversion"]
        )
        
        # Overlap sessions (highest activity)
        profiles[(MarketSession.OVERLAP_ASIAN_EUROPEAN, TimeOfDay.MORNING)] = MarketActivityProfile(
            session=MarketSession.OVERLAP_ASIAN_EUROPEAN,
            time_of_day=TimeOfDay.MORNING,
            volatility_multiplier=1.5,
            volume_multiplier=1.4,
            spread_multiplier=0.7,
            optimal_for_trading=True,
            recommended_strategies=["momentum", "trend_following"]
        )
        
        profiles[(MarketSession.OVERLAP_EUROPEAN_US, TimeOfDay.AFTERNOON)] = MarketActivityProfile(
            session=MarketSession.OVERLAP_EUROPEAN_US,
            time_of_day=TimeOfDay.AFTERNOON,
            volatility_multiplier=1.6,
            volume_multiplier=1.5,
            spread_multiplier=0.6,
            optimal_for_trading=True,
            recommended_strategies=["momentum", "trend_following"]
        )
        
        # Quiet periods
        profiles[(MarketSession.QUIET, TimeOfDay.NIGHT)] = MarketActivityProfile(
            session=MarketSession.QUIET,
            time_of_day=TimeOfDay.NIGHT,
            volatility_multiplier=0.6,
            volume_multiplier=0.4,
            spread_multiplier=1.5,
            optimal_for_trading=False,
            recommended_strategies=["mean_reversion"]
        )
        
        return profiles
    
    async def initialize(self):
        """Initialize the time manager"""
        try:
            self.logger.info("Initializing Advanced Time Manager...")
            
            # Load historical time-based patterns
            await self._load_historical_patterns()
            
            # Initialize position tracking
            await self._initialize_position_tracking()
            
            self.logger.info("Advanced Time Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Time Manager: {e}")
            raise
    
    def get_current_time_precise(self) -> float:
        """Get current time with microsecond precision"""
        return time.time()
    
    def get_current_datetime_utc(self) -> datetime:
        """Get current datetime in UTC"""
        return datetime.now(self.utc_tz)
    
    def get_market_session(self, dt: Optional[datetime] = None) -> MarketSession:
        """Determine current market session"""
        if dt is None:
            dt = self.get_current_datetime_utc()
        
        hour = dt.hour
        
        # Check for overlaps first
        if 7 <= hour < 9:  # Asian-European overlap
            return MarketSession.OVERLAP_ASIAN_EUROPEAN
        elif 13 <= hour < 16:  # European-US overlap
            return MarketSession.OVERLAP_EUROPEAN_US
        
        # Individual sessions
        elif 0 <= hour < 9:
            return MarketSession.ASIAN
        elif 7 <= hour < 16:
            return MarketSession.EUROPEAN
        elif 13 <= hour < 22:
            return MarketSession.US
        else:
            return MarketSession.QUIET

    def get_time_of_day(self, dt: Optional[datetime] = None) -> TimeOfDay:
        """Determine time of day category"""
        if dt is None:
            dt = self.get_current_datetime_utc()

        hour = dt.hour

        if 0 <= hour < 4:
            return TimeOfDay.LATE_NIGHT
        elif 4 <= hour < 8:
            return TimeOfDay.EARLY_MORNING
        elif 8 <= hour < 12:
            return TimeOfDay.MORNING
        elif 12 <= hour < 16:
            return TimeOfDay.MIDDAY
        elif 16 <= hour < 20:
            return TimeOfDay.AFTERNOON
        elif 20 <= hour < 24:
            return TimeOfDay.EVENING
        else:
            return TimeOfDay.NIGHT

    def is_market_active(self, dt: Optional[datetime] = None) -> bool:
        """Check if market is currently active"""
        session = self.get_market_session(dt)
        return session != MarketSession.QUIET

    def is_bank_holiday(self, dt: Optional[datetime] = None) -> bool:
        """Check if current date is a bank holiday in major markets"""
        if dt is None:
            dt = self.get_current_datetime_utc()

        date = dt.date()

        # Check major market holidays
        for country, holiday_calendar in self.holidays.items():
            if date in holiday_calendar:
                return True

        return False

    def get_session_times(self, session: MarketSession, dt: Optional[datetime] = None) -> Tuple[datetime, datetime]:
        """Get start and end times for a market session"""
        if dt is None:
            dt = self.get_current_datetime_utc()

        date = dt.date()

        if session in self.session_times:
            start_hour, end_hour = self.session_times[session]
            start_time = datetime.combine(date, datetime.min.time().replace(hour=start_hour), self.utc_tz)
            end_time = datetime.combine(date, datetime.min.time().replace(hour=end_hour), self.utc_tz)

            # Handle sessions that cross midnight
            if end_hour < start_hour:
                end_time += timedelta(days=1)

            return start_time, end_time

        return dt, dt

    def get_time_until_next_session(self, dt: Optional[datetime] = None) -> Optional[timedelta]:
        """Get time until next active market session"""
        if dt is None:
            dt = self.get_current_datetime_utc()

        if self.is_market_active(dt):
            return timedelta(0)

        # Find next session
        for session in [MarketSession.ASIAN, MarketSession.EUROPEAN, MarketSession.US]:
            start_time, _ = self.get_session_times(session, dt)

            if start_time > dt:
                return start_time - dt

            # Check next day
            next_day = dt + timedelta(days=1)
            start_time, _ = self.get_session_times(session, next_day)
            return start_time - dt

        return None

    def get_activity_profile(self, dt: Optional[datetime] = None) -> MarketActivityProfile:
        """Get market activity profile for current time"""
        if dt is None:
            dt = self.get_current_datetime_utc()

        session = self.get_market_session(dt)
        time_of_day = self.get_time_of_day(dt)

        # Try exact match first
        profile_key = (session, time_of_day)
        if profile_key in self.activity_profiles:
            return self.activity_profiles[profile_key]

        # Fallback to session-based profile
        for key, profile in self.activity_profiles.items():
            if key[0] == session:
                return profile

        # Default quiet profile
        return MarketActivityProfile(
            session=MarketSession.QUIET,
            time_of_day=time_of_day,
            volatility_multiplier=0.8,
            volume_multiplier=0.6,
            spread_multiplier=1.2,
            optimal_for_trading=False,
            recommended_strategies=["mean_reversion"]
        )

    def track_position_start(self, symbol: str, dt: Optional[datetime] = None):
        """Track when a position was opened"""
        if dt is None:
            dt = self.get_current_datetime_utc()

        self.position_start_times[symbol] = dt

    def track_position_end(self, symbol: str, dt: Optional[datetime] = None) -> Optional[float]:
        """Track when a position was closed and return holding duration in hours"""
        if dt is None:
            dt = self.get_current_datetime_utc()

        if symbol in self.position_start_times:
            start_time = self.position_start_times[symbol]
            duration = (dt - start_time).total_seconds() / 3600  # Convert to hours
            del self.position_start_times[symbol]
            return duration

        return None

    def get_position_holding_duration(self, symbol: str, dt: Optional[datetime] = None) -> Optional[float]:
        """Get current holding duration for a position in hours"""
        if dt is None:
            dt = self.get_current_datetime_utc()

        if symbol in self.position_start_times:
            start_time = self.position_start_times[symbol]
            return (dt - start_time).total_seconds() / 3600

        return None

    def get_optimal_trading_hours(self) -> List[Tuple[int, int]]:
        """Get list of optimal trading hour ranges (UTC)"""
        optimal_hours = []

        for (session, time_of_day), profile in self.activity_profiles.items():
            if profile.optimal_for_trading:
                start_hour, end_hour = self.session_times.get(session, (0, 24))
                optimal_hours.append((start_hour, end_hour))

        return optimal_hours

    def get_market_volatility_by_time(self, dt: Optional[datetime] = None) -> float:
        """Get expected market volatility multiplier for current time"""
        profile = self.get_activity_profile(dt)
        return profile.volatility_multiplier

    def get_recommended_strategies(self, dt: Optional[datetime] = None) -> List[str]:
        """Get recommended strategies for current time"""
        profile = self.get_activity_profile(dt)
        return profile.recommended_strategies

    async def get_time_aware_metrics(self) -> TimeAwareMetrics:
        """Get comprehensive time-aware metrics"""
        try:
            current_time = self.get_current_datetime_utc()
            current_time_precise = self.get_current_time_precise()
            session = self.get_market_session(current_time)
            time_of_day = self.get_time_of_day(current_time)
            is_active = self.is_market_active(current_time)
            is_holiday = self.is_bank_holiday(current_time)

            session_start, session_end = self.get_session_times(session, current_time)
            time_until_next = self.get_time_until_next_session(current_time)
            activity_profile = self.get_activity_profile(current_time)

            # Get position holding durations
            position_durations = {}
            for symbol in self.position_start_times:
                duration = self.get_position_holding_duration(symbol, current_time)
                if duration is not None:
                    position_durations[symbol] = duration

            return TimeAwareMetrics(
                current_time_utc=current_time,
                current_time_precise=current_time_precise,
                market_session=session,
                time_of_day=time_of_day,
                is_market_active=is_active,
                is_bank_holiday=is_holiday,
                session_start_time=session_start,
                session_end_time=session_end,
                time_until_next_session=time_until_next,
                activity_profile=activity_profile,
                position_holding_duration=position_durations
            )

        except Exception as e:
            self.logger.error(f"Error getting time-aware metrics: {e}")
            raise

    async def _load_historical_patterns(self):
        """Load historical time-based trading patterns"""
        try:
            # Load from database if available
            query = """
            SELECT
                EXTRACT(hour FROM executed_at) as hour,
                EXTRACT(dow FROM executed_at) as day_of_week,
                AVG(realized_pnl) as avg_pnl,
                COUNT(*) as trade_count,
                STDDEV(realized_pnl) as pnl_std
            FROM trades
            WHERE executed_at > NOW() - INTERVAL '90 days'
            GROUP BY EXTRACT(hour FROM executed_at), EXTRACT(dow FROM executed_at)
            """

            results = await self.db.fetch_all(query)

            for row in results:
                hour = int(row['hour'])
                day_of_week = int(row['day_of_week'])

                key = (hour, day_of_week)
                self.historical_patterns[key] = {
                    'avg_pnl': float(row['avg_pnl']) if row['avg_pnl'] else 0.0,
                    'trade_count': row['trade_count'],
                    'pnl_std': float(row['pnl_std']) if row['pnl_std'] else 0.0
                }

        except Exception as e:
            self.logger.error(f"Error loading historical patterns: {e}")

    async def _initialize_position_tracking(self):
        """Initialize position tracking from current open positions"""
        try:
            # This would be called from the main bot to sync current positions
            pass

        except Exception as e:
            self.logger.error(f"Error initializing position tracking: {e}")

    def get_time_based_performance(self, hour: int, day_of_week: int) -> Optional[Dict]:
        """Get historical performance for specific time"""
        return self.historical_patterns.get((hour, day_of_week))

    def should_trade_now(self, dt: Optional[datetime] = None) -> bool:
        """Determine if current time is optimal for trading"""
        if dt is None:
            dt = self.get_current_datetime_utc()

        # Check if market is active
        if not self.is_market_active(dt):
            return False

        # Check if it's a bank holiday
        if self.is_bank_holiday(dt):
            return False

        # Check activity profile
        profile = self.get_activity_profile(dt)
        return profile.optimal_for_trading

    def get_position_size_multiplier(self, dt: Optional[datetime] = None) -> float:
        """Get position size multiplier based on current time"""
        profile = self.get_activity_profile(dt)

        # Higher multiplier during high activity periods
        base_multiplier = 1.0

        if profile.optimal_for_trading:
            base_multiplier *= profile.volatility_multiplier
            base_multiplier *= profile.volume_multiplier
        else:
            base_multiplier *= 0.5  # Reduce position size during quiet periods

        return min(base_multiplier, 2.0)  # Cap at 2x
