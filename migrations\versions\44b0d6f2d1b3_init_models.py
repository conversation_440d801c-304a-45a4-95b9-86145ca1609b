"""init models

Revision ID: 44b0d6f2d1b3
Revises: 
Create Date: 2023-06-01 11:55:35.195423

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '44b0d6f2d1b3'
down_revision = None
branch_labels = None
depends_on = None

from sqlalchemy.engine.reflection import Inspector

conn = op.get_bind()
inspector = Inspector.from_engine(conn)
tables = inspector.get_table_names()

def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    if 'agent_configurations' not in tables:
        op.create_table('agent_configurations',
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('agent_id', sa.Integer(), nullable=True),
        sa.Column('key', sa.String(), nullable=True),
        sa.Column('value', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id')
        )
    if 'agent_execution_feeds' not in tables:
        op.create_table('agent_execution_feeds',
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('agent_execution_id', sa.Integer(), nullable=True),
        sa.Column('agent_id', sa.Integer(), nullable=True),
        sa.Column('feed', sa.Text(), nullable=True),
        sa.Column('role', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('id')
        )
    if 'agent_executions' not in tables:
        op.create_table('agent_executions',
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('agent_id', sa.Integer(), nullable=True),
        sa.Column('last_execution_time', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
        )
    if 'agents' not in tables:
        op.create_table('agents',
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('project_id', sa.Integer(), nullable=True),
        sa.Column('description', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('id')
        )
    if 'budgets' not in tables:
        op.create_table('budgets',
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('budget', sa.Float(), nullable=True),
        sa.Column('cycle', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('id')
        )
    if 'organisations' not in tables:
        op.create_table('organisations',
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('description', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('id')
        )
    if 'projects' not in tables:
        op.create_table('projects',
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('organisation_id', sa.Integer(), nullable=True),
        sa.Column('description', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('id')
        )
    if 'tool_configs' not in tables:
        op.create_table('tool_configs',
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('key', sa.String(), nullable=True),
        sa.Column('value', sa.String(), nullable=True),
        sa.Column('agent_id', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id')
        )
    if 'tools' not in tables:
        op.create_table('tools',
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('folder_name', sa.String(), nullable=True),
        sa.Column('class_name', sa.String(), nullable=True),
        sa.Column('file_name', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('id')
        )
    if 'users' not in tables:
        op.create_table('users',
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('email', sa.String(), nullable=True),
        sa.Column('password', sa.String(), nullable=True),
        sa.Column('organisation_id', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('email')
        )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('users')
    op.drop_table('tools')
    op.drop_table('tool_configs')
    op.drop_table('projects')
    op.drop_table('organisations')
    op.drop_table('budgets')
    op.drop_table('agents')
    op.drop_table('agent_executions')
    op.drop_table('agent_execution_feeds')
    op.drop_table('agent_configurations')
    # ### end Alembic commands ###
