#!/usr/bin/env python3
"""
AI Systems Integration Test
Tests the integration of meta-cognition, self-correcting code evolution, and recursive improvement systems
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from bybit_bot.core.config import BotConfig
from bybit_bot.core.logger import TradingBotLogger
from bybit_bot.database.connection import DatabaseManager
from bybit_bot.ai.meta_cognition_engine import MetaCognitionEngine
from bybit_bot.ai.self_correcting_code_evolution import SelfCorrectingCodeEvolution
from bybit_bot.ai.recursive_improvement_system import RecursiveImprovementSystem
from bybit_bot.ai.memory_manager import PersistentMemoryManager

class AISystemsIntegrationTest:
    """Comprehensive integration test for AI systems"""
    
    def __init__(self):
        self.config = BotConfig()
        self.logger = TradingBotLogger("AISystemsTest")
        self.db_manager = None
        self.memory_manager = None
        self.meta_cognition = None
        self.code_evolution = None
        self.recursive_improvement = None
        
        # Test results
        self.test_results = {}
        
    async def setup(self):
        """Setup test environment"""
        try:
            self.logger.info("Setting up AI Systems Integration Test...")
            
            # Initialize database (in-memory for testing)
            self.db_manager = DatabaseManager(self.config)
            await self.db_manager.initialize()
            
            # Initialize memory manager
            self.memory_manager = PersistentMemoryManager(self.config, self.db_manager)
            await self.memory_manager.initialize()
            
            # Initialize meta-cognition engine
            self.meta_cognition = MetaCognitionEngine(
                orchestrator=None,  # Mock orchestrator for testing
                memory_manager=self.memory_manager,
                database=self.db_manager
            )
            await self.meta_cognition.initialize()
            
            # Initialize code evolution system
            self.code_evolution = SelfCorrectingCodeEvolution(
                base_path=str(Path(__file__).parent),
                orchestrator=None,  # Mock orchestrator for testing
                meta_cognition=self.meta_cognition
            )
            await self.code_evolution.initialize()
            
            # Initialize recursive improvement system
            self.recursive_improvement = RecursiveImprovementSystem(
                orchestrator=None,  # Mock orchestrator for testing
                meta_cognition=self.meta_cognition,
                code_evolution=self.code_evolution
            )
            await self.recursive_improvement.initialize()
            
            self.logger.info("✅ AI Systems setup complete")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to setup AI systems: {e}")
            raise
    
    async def test_meta_cognition_engine(self):
        """Test meta-cognition engine functionality"""
        try:
            self.logger.info("🧠 Testing Meta-Cognition Engine...")
            
            # Test system metrics collection
            metrics = await self.meta_cognition._collect_system_metrics()
            assert isinstance(metrics, dict), "System metrics should be a dictionary"
            
            # Test awareness level assessment
            awareness = await self.meta_cognition._assess_awareness_level()
            assert 0 <= awareness <= 1, "Awareness level should be between 0 and 1"
            
            # Test cognitive state update
            await self.meta_cognition._update_cognitive_state()
            
            # Test self-reflection
            reflection = await self.meta_cognition._perform_self_reflection()
            assert isinstance(reflection, dict), "Self-reflection should return a dictionary"
            
            self.test_results['meta_cognition'] = {
                'status': 'PASSED',
                'metrics_collected': len(metrics),
                'awareness_level': awareness,
                'reflection_insights': len(reflection.get('insights', []))
            }
            
            self.logger.info("✅ Meta-Cognition Engine tests passed")
            
        except Exception as e:
            self.logger.error(f"❌ Meta-Cognition Engine test failed: {e}")
            self.test_results['meta_cognition'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def test_code_evolution_system(self):
        """Test self-correcting code evolution system"""
        try:
            self.logger.info("🔧 Testing Code Evolution System...")
            
            # Test code analysis
            analysis = await self.code_evolution._analyze_codebase()
            assert isinstance(analysis, dict), "Code analysis should return a dictionary"
            
            # Test issue detection
            issues = await self.code_evolution._detect_code_issues()
            assert isinstance(issues, list), "Issues should be a list"
            
            # Test improvement identification
            improvements = await self.code_evolution._identify_improvements()
            assert isinstance(improvements, list), "Improvements should be a list"
            
            # Test performance profiling
            profile = await self.code_evolution._profile_performance()
            assert isinstance(profile, dict), "Performance profile should be a dictionary"
            
            self.test_results['code_evolution'] = {
                'status': 'PASSED',
                'analysis_modules': len(analysis.get('modules', [])),
                'issues_detected': len(issues),
                'improvements_identified': len(improvements),
                'performance_metrics': len(profile.get('metrics', {}))
            }
            
            self.logger.info("✅ Code Evolution System tests passed")
            
        except Exception as e:
            self.logger.error(f"❌ Code Evolution System test failed: {e}")
            self.test_results['code_evolution'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def test_recursive_improvement_system(self):
        """Test recursive improvement system"""
        try:
            self.logger.info("🔄 Testing Recursive Improvement System...")
            
            # Test optimization strategy creation
            strategies = await self.recursive_improvement._create_optimization_strategies()
            assert isinstance(strategies, list), "Optimization strategies should be a list"
            
            # Test recursive loop execution
            loop_result = await self.recursive_improvement._execute_recursive_loop()
            assert isinstance(loop_result, dict), "Recursive loop should return a dictionary"
            
            # Test convergence detection
            convergence = await self.recursive_improvement._check_convergence()
            assert isinstance(convergence, bool), "Convergence should be a boolean"
            
            # Test meta-optimization
            meta_opt = await self.recursive_improvement._perform_meta_optimization()
            assert isinstance(meta_opt, dict), "Meta-optimization should return a dictionary"
            
            self.test_results['recursive_improvement'] = {
                'status': 'PASSED',
                'optimization_strategies': len(strategies),
                'loop_iterations': loop_result.get('iterations', 0),
                'convergence_achieved': convergence,
                'meta_optimizations': len(meta_opt.get('optimizations', []))
            }
            
            self.logger.info("✅ Recursive Improvement System tests passed")
            
        except Exception as e:
            self.logger.error(f"❌ Recursive Improvement System test failed: {e}")
            self.test_results['recursive_improvement'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def test_system_integration(self):
        """Test integration between all AI systems"""
        try:
            self.logger.info("🔗 Testing System Integration...")
            
            # Test cross-system communication
            integration_data = {
                'timestamp': datetime.now(),
                'source': 'integration_test',
                'data': {'test_metric': 0.85}
            }
            
            # Meta-cognition processes data
            await self.meta_cognition._process_feedback(integration_data)
            
            # Code evolution analyzes for improvements
            improvements = await self.code_evolution._analyze_system_performance(integration_data)
            
            # Recursive improvement optimizes the process
            optimization = await self.recursive_improvement._optimize_system_interactions(integration_data)
            
            self.test_results['integration'] = {
                'status': 'PASSED',
                'feedback_processed': True,
                'improvements_found': len(improvements) if improvements else 0,
                'optimization_applied': optimization.get('success', False)
            }
            
            self.logger.info("✅ System Integration tests passed")
            
        except Exception as e:
            self.logger.error(f"❌ System Integration test failed: {e}")
            self.test_results['integration'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def test_concurrent_operations(self):
        """Test concurrent operation of all AI systems"""
        try:
            self.logger.info("⚡ Testing Concurrent Operations...")
            
            # Start all systems concurrently
            tasks = [
                self.meta_cognition.start_monitoring(),
                self.code_evolution.start_monitoring(), 
                self.recursive_improvement.start_improvement_loops()
            ]
            
            # Run for a short duration
            await asyncio.sleep(2)
            
            # Check all systems are running
            meta_running = hasattr(self.meta_cognition, 'is_monitoring') and self.meta_cognition.is_monitoring
            code_running = hasattr(self.code_evolution, 'is_monitoring') and self.code_evolution.is_monitoring
            recursive_running = hasattr(self.recursive_improvement, 'is_running') and self.recursive_improvement.is_running
            
            self.test_results['concurrent_operations'] = {
                'status': 'PASSED',
                'meta_cognition_running': meta_running,
                'code_evolution_running': code_running,
                'recursive_improvement_running': recursive_running,
                'all_systems_active': meta_running and code_running and recursive_running
            }
            
            self.logger.info("✅ Concurrent Operations tests passed")
            
        except Exception as e:
            self.logger.error(f"❌ Concurrent Operations test failed: {e}")
            self.test_results['concurrent_operations'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def cleanup(self):
        """Cleanup test environment"""
        try:
            self.logger.info("🧹 Cleaning up test environment...")
            
            # Shutdown AI systems
            if self.recursive_improvement:
                await self.recursive_improvement.shutdown()
            
            if self.code_evolution:
                await self.code_evolution.shutdown()
            
            if self.meta_cognition:
                await self.meta_cognition.shutdown()
            
            # Cleanup memory manager
            if self.memory_manager:
                await self.memory_manager.shutdown()
            
            # Cleanup database
            if self.db_manager:
                await self.db_manager.close()
            
            self.logger.info("✅ Cleanup complete")
            
        except Exception as e:
            self.logger.error(f"❌ Cleanup failed: {e}")
    
    async def run_all_tests(self):
        """Run all integration tests"""
        try:
            self.logger.info("🚀 Starting AI Systems Integration Tests")
            
            await self.setup()
            
            # Run individual system tests
            await self.test_meta_cognition_engine()
            await self.test_code_evolution_system()
            await self.test_recursive_improvement_system()
            
            # Run integration tests
            await self.test_system_integration()
            await self.test_concurrent_operations()
            
            await self.cleanup()
            
            # Report results
            self._report_results()
            
        except Exception as e:
            self.logger.error(f"❌ Integration tests failed: {e}")
            await self.cleanup()
    
    def _report_results(self):
        """Report test results"""
        self.logger.info("=" * 60)
        self.logger.info("📊 AI SYSTEMS INTEGRATION TEST RESULTS")
        self.logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASSED')
        
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result['status'] == 'PASSED' else "❌"
            self.logger.info(f"{status_icon} {test_name.upper()}: {result['status']}")
            
            if result['status'] == 'PASSED':
                for key, value in result.items():
                    if key != 'status':
                        self.logger.info(f"   - {key}: {value}")
            else:
                self.logger.error(f"   - Error: {result.get('error', 'Unknown error')}")
        
        self.logger.info("=" * 60)
        self.logger.info(f"📈 SUMMARY: {passed_tests}/{total_tests} tests passed")
        self.logger.info("=" * 60)
        
        if passed_tests == total_tests:
            self.logger.info("🎉 ALL TESTS PASSED - AI Systems are ready for deployment!")
        else:
            self.logger.warning("⚠️ Some tests failed - Please review and fix issues before deployment")


async def main():
    """Main test execution function"""
    test_runner = AISystemsIntegrationTest()
    await test_runner.run_all_tests()


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the tests
    asyncio.run(main())
