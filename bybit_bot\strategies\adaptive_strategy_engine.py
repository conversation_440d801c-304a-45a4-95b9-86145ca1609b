"""
Adaptive Strategy Engine for Bybit Trading Bot
Implements continuous learning and strategy adaptation based on market conditions and performance
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger
from ..database.connection import DatabaseManager


class AdaptationTrigger(Enum):
    """Triggers for strategy adaptation"""
    PERFORMANCE_DECLINE = "performance_decline"
    MARKET_REGIME_CHANGE = "market_regime_change"
    VOLATILITY_CHANGE = "volatility_change"
    CORRELATION_CHANGE = "correlation_change"
    SCHEDULED_REVIEW = "scheduled_review"
    MANUAL_TRIGGER = "manual_trigger"


@dataclass
class StrategyPerformance:
    """Strategy performance metrics"""
    strategy_name: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    total_pnl: float
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    sharpe_ratio: float
    max_drawdown: float
    avg_holding_time: float
    last_updated: datetime


@dataclass
class AdaptationResult:
    """Result of strategy adaptation"""
    strategy_name: str
    adaptation_type: str
    old_parameters: Dict[str, Any]
    new_parameters: Dict[str, Any]
    expected_improvement: float
    confidence: float
    timestamp: datetime
    reason: str


class AdaptiveStrategyEngine:
    """
    Advanced adaptive strategy engine that:
    - Continuously monitors strategy performance
    - Adapts strategy parameters based on market conditions
    - Implements backtesting for parameter optimization
    - Uses machine learning for strategy selection
    - Manages strategy lifecycle and evolution
    """

    def __init__(self, config: BotConfig, database_manager: DatabaseManager, market_predictor):
        self.config = config
        self.db = database_manager
        self.market_predictor = market_predictor
        self.logger = TradingBotLogger(config)

        # Strategy tracking
        self.strategy_performances = {}
        self.strategy_parameters = {}
        self.adaptation_history = []

        # Adaptation settings
        self.min_trades_for_adaptation = getattr(config, 'min_trades_for_adaptation', 20)
        self.adaptation_threshold = getattr(config, 'adaptation_threshold', 0.1)
        self.max_parameter_change = getattr(config, 'max_parameter_change', 0.3)
        self.backtest_period_days = getattr(config, 'backtest_period_days', 30)

        # Market condition tracking
        self.current_market_conditions = {}
        self.market_condition_history = []

        # Adaptation engine state
        self.running = False
        self.adaptation_task = None
        self.last_adaptation_check = None

        # Default strategy parameters
        self.default_parameters = {
            'momentum': {
                'rsi_overbought': 70,
                'rsi_oversold': 30,
                'macd_threshold': 0.001,
                'volume_threshold': 1.5,
                'min_signal_strength': 0.6
            },
            'mean_reversion': {
                'bb_std_dev': 2.0,
                'rsi_extreme_upper': 75,
                'rsi_extreme_lower': 25,
                'price_deviation_threshold': 0.02,
                'min_signal_strength': 0.7
            },
            'trend_following': {
                'ema_fast': 20,
                'ema_slow': 50,
                'trend_strength_threshold': 0.6,
                'volume_confirmation': 1.2,
                'min_signal_strength': 0.6
            }
        }

        # Initialize strategy parameters
        self.strategy_parameters = self.default_parameters.copy()

    async def initialize(self):
        """Initialize the adaptive strategy engine"""
        try:
            self.logger.info("🧠 Initializing Adaptive Strategy Engine...")

            # Load historical performance data
            await self._load_strategy_performance_history()

            # Load current strategy parameters
            await self._load_strategy_parameters()

            # Initialize market condition tracking
            await self._initialize_market_condition_tracking()

            # Start adaptation monitoring
            await self.start_adaptation_monitoring()

            self.logger.info("✅ Adaptive Strategy Engine initialized successfully")

        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Adaptive Strategy Engine: {e}")
            raise

    async def start_adaptation_monitoring(self):
        """Start continuous adaptation monitoring"""
        if self.running:
            return

        self.running = True
        self.adaptation_task = asyncio.create_task(self._adaptation_monitoring_loop())
        self.logger.info("🔄 Adaptation monitoring started")

    async def stop_adaptation_monitoring(self):
        """Stop adaptation monitoring"""
        self.running = False

        if self.adaptation_task:
            self.adaptation_task.cancel()
            try:
                await self.adaptation_task
            except asyncio.CancelledError:
                pass

        self.logger.info("🛑 Adaptation monitoring stopped")

    async def _adaptation_monitoring_loop(self):
        """Main adaptation monitoring loop"""
        while self.running:
            try:
                # Check for adaptation triggers
                triggers = await self._check_adaptation_triggers()

                if triggers:
                    self.logger.info(f"Adaptation triggers detected: {[t.value for t in triggers]}")

                    # Perform adaptations
                    for trigger in triggers:
                        await self._perform_adaptation(trigger)

                # Update market conditions
                await self._update_market_conditions()

                # Periodic strategy evaluation
                await self._evaluate_strategy_performance()

                self.last_adaptation_check = datetime.utcnow()

                # Wait before next check
                await asyncio.sleep(300)  # Check every 5 minutes

            except Exception as e:
                self.logger.error(f"Error in adaptation monitoring loop: {e}")
                await asyncio.sleep(600)  # Wait longer on error

    async def _check_adaptation_triggers(self) -> List[AdaptationTrigger]:
        """Check for conditions that trigger strategy adaptation"""
        triggers = []

        try:
            # Check performance decline
            if await self._check_performance_decline():
                triggers.append(AdaptationTrigger.PERFORMANCE_DECLINE)

            # Check market regime change
            if await self._check_market_regime_change():
                triggers.append(AdaptationTrigger.MARKET_REGIME_CHANGE)

            # Check volatility change
            if await self._check_volatility_change():
                triggers.append(AdaptationTrigger.VOLATILITY_CHANGE)

            # Check scheduled review
            if await self._check_scheduled_review():
                triggers.append(AdaptationTrigger.SCHEDULED_REVIEW)

        except Exception as e:
            self.logger.error(f"Error checking adaptation triggers: {e}")

        return triggers

    async def _check_performance_decline(self) -> bool:
        """Check if any strategy is showing performance decline"""
        try:
            for strategy_name, performance in self.strategy_performances.items():
                if performance.total_trades >= self.min_trades_for_adaptation:
                    # Check recent performance vs historical
                    recent_performance = await self._get_recent_strategy_performance(strategy_name, days=7)
                    historical_performance = await self._get_recent_strategy_performance(strategy_name, days=30)

                    if recent_performance and historical_performance:
                        recent_win_rate = recent_performance.get('win_rate', 0)
                        historical_win_rate = historical_performance.get('win_rate', 0)

                        # Significant decline in win rate
                        if historical_win_rate - recent_win_rate > self.adaptation_threshold:
                            self.logger.warning(f"Performance decline detected for {strategy_name}: "
                                              f"Recent: {recent_win_rate:.2%}, Historical: {historical_win_rate:.2%}")
                            return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking performance decline: {e}")
            return False

    async def _check_market_regime_change(self) -> bool:
        """Check if market regime has changed significantly"""
        try:
            if len(self.market_condition_history) < 2:
                return False

            current_conditions = self.current_market_conditions
            previous_conditions = self.market_condition_history[-2] if len(self.market_condition_history) >= 2 else {}

            # Check for significant changes in key metrics
            volatility_change = abs(current_conditions.get('volatility', 0.5) -
                                  previous_conditions.get('volatility', 0.5))

            trend_change = abs(current_conditions.get('trend_strength', 0) -
                             previous_conditions.get('trend_strength', 0))

            if volatility_change > 0.3 or trend_change > 0.4:
                self.logger.info(f"Market regime change detected: volatility_change={volatility_change:.3f}, "
                               f"trend_change={trend_change:.3f}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking market regime change: {e}")
            return False

    async def _check_volatility_change(self) -> bool:
        """Check for significant volatility changes"""
        try:
            if len(self.market_condition_history) < 5:
                return False

            recent_volatilities = [conditions.get('volatility', 0.5)
                                 for conditions in self.market_condition_history[-5:]]

            volatility_std = np.std(recent_volatilities)

            # High volatility in recent conditions suggests adaptation needed
            if volatility_std > 0.2:
                self.logger.info(f"High volatility change detected: std={volatility_std:.3f}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking volatility change: {e}")
            return False

    async def _check_scheduled_review(self) -> bool:
        """Check if scheduled review is due"""
        try:
            if not self.last_adaptation_check:
                return True

            # Schedule review every 24 hours
            time_since_last = datetime.utcnow() - self.last_adaptation_check
            return time_since_last > timedelta(hours=24)

        except Exception as e:
            self.logger.error(f"Error checking scheduled review: {e}")
            return False

    async def _perform_adaptation(self, trigger: AdaptationTrigger):
        """Perform strategy adaptation based on trigger"""
        try:
            self.logger.info(f"Performing adaptation for trigger: {trigger.value}")

            if trigger == AdaptationTrigger.PERFORMANCE_DECLINE:
                await self._adapt_for_performance_decline()
            elif trigger == AdaptationTrigger.MARKET_REGIME_CHANGE:
                await self._adapt_for_market_regime_change()
            elif trigger == AdaptationTrigger.VOLATILITY_CHANGE:
                await self._adapt_for_volatility_change()
            elif trigger == AdaptationTrigger.SCHEDULED_REVIEW:
                await self._perform_scheduled_adaptation()

        except Exception as e:
            self.logger.error(f"Error performing adaptation for {trigger.value}: {e}")

    async def _adapt_for_performance_decline(self):
        """Adapt strategies that are showing performance decline"""
        try:
            for strategy_name, performance in self.strategy_performances.items():
                if performance.total_trades >= self.min_trades_for_adaptation:
                    recent_performance = await self._get_recent_strategy_performance(strategy_name, days=7)

                    if recent_performance and recent_performance.get('win_rate', 0) < 0.4:
                        # Strategy is performing poorly, adapt parameters
                        await self._optimize_strategy_parameters(strategy_name)

        except Exception as e:
            self.logger.error(f"Error adapting for performance decline: {e}")

    async def _adapt_for_market_regime_change(self):
        """Adapt strategies for market regime changes"""
        try:
            current_volatility = self.current_market_conditions.get('volatility', 0.5)
            current_trend = self.current_market_conditions.get('trend_strength', 0)

            # Adjust parameters based on market conditions
            for strategy_name in self.strategy_parameters:
                if strategy_name == 'momentum':
                    # In high volatility, use tighter RSI levels
                    if current_volatility > 0.7:
                        self.strategy_parameters[strategy_name]['rsi_overbought'] = 65
                        self.strategy_parameters[strategy_name]['rsi_oversold'] = 35
                    else:
                        self.strategy_parameters[strategy_name]['rsi_overbought'] = 70
                        self.strategy_parameters[strategy_name]['rsi_oversold'] = 30

                elif strategy_name == 'trend_following':
                    # In strong trends, use faster EMAs
                    if abs(current_trend) > 0.7:
                        self.strategy_parameters[strategy_name]['ema_fast'] = 15
                        self.strategy_parameters[strategy_name]['ema_slow'] = 40
                    else:
                        self.strategy_parameters[strategy_name]['ema_fast'] = 20
                        self.strategy_parameters[strategy_name]['ema_slow'] = 50

            await self._save_strategy_parameters()

        except Exception as e:
            self.logger.error(f"Error adapting for market regime change: {e}")

    async def _adapt_for_volatility_change(self):
        """Adapt strategies for volatility changes"""
        try:
            current_volatility = self.current_market_conditions.get('volatility', 0.5)

            # Adjust signal strength requirements based on volatility
            for strategy_name in self.strategy_parameters:
                if current_volatility > 0.8:
                    # High volatility - require stronger signals
                    self.strategy_parameters[strategy_name]['min_signal_strength'] = 0.8
                elif current_volatility < 0.3:
                    # Low volatility - allow weaker signals
                    self.strategy_parameters[strategy_name]['min_signal_strength'] = 0.5
                else:
                    # Normal volatility - default requirements
                    self.strategy_parameters[strategy_name]['min_signal_strength'] = 0.6

            await self._save_strategy_parameters()

        except Exception as e:
            self.logger.error(f"Error adapting for volatility change: {e}")

    async def _perform_scheduled_adaptation(self):
        """Perform comprehensive scheduled adaptation"""
        try:
            # Evaluate all strategies
            await self._evaluate_strategy_performance()

            # Optimize parameters for underperforming strategies
            for strategy_name, performance in self.strategy_performances.items():
                if performance.total_trades >= self.min_trades_for_adaptation:
                    if performance.win_rate < 0.5 or performance.profit_factor < 1.2:
                        await self._optimize_strategy_parameters(strategy_name)

            # Update strategy weights based on performance
            await self._update_strategy_weights()

        except Exception as e:
            self.logger.error(f"Error in scheduled adaptation: {e}")

    async def _optimize_strategy_parameters(self, strategy_name: str):
        """Optimize parameters for a specific strategy using backtesting"""
        try:
            self.logger.info(f"Optimizing parameters for {strategy_name}")

            current_params = self.strategy_parameters.get(strategy_name, {})
            best_params = current_params.copy()
            best_score = 0.0

            # Define parameter ranges for optimization
            param_ranges = self._get_parameter_ranges(strategy_name)

            # Test different parameter combinations
            for param_name, param_range in param_ranges.items():
                if param_name in current_params:
                    current_value = current_params[param_name]

                    # Test values around current parameter
                    test_values = self._generate_test_values(current_value, param_range)

                    for test_value in test_values:
                        # Create test parameters
                        test_params = current_params.copy()
                        test_params[param_name] = test_value

                        # Backtest with new parameters
                        score = await self._backtest_strategy_variant(strategy_name, test_params)

                        if score > best_score:
                            best_score = score
                            best_params = test_params.copy()

            # Apply best parameters if improvement is significant
            if best_score > 0 and best_params != current_params:
                improvement = (best_score - await self._get_current_strategy_score(strategy_name)) / max(0.01, await self._get_current_strategy_score(strategy_name))

                if improvement > 0.1:  # 10% improvement threshold
                    self.strategy_parameters[strategy_name] = best_params
                    await self._save_strategy_parameters()

                    # Record adaptation
                    adaptation_result = AdaptationResult(
                        strategy_name=strategy_name,
                        adaptation_type="parameter_optimization",
                        old_parameters=current_params,
                        new_parameters=best_params,
                        expected_improvement=improvement,
                        confidence=0.8,
                        timestamp=datetime.utcnow(),
                        reason=f"Backtesting optimization improved score by {improvement:.2%}"
                    )

                    self.adaptation_history.append(adaptation_result)
                    self.logger.info(f"Optimized {strategy_name}: improvement={improvement:.2%}")

        except Exception as e:
            self.logger.error(f"Error optimizing parameters for {strategy_name}: {e}")

    async def _backtest_strategy_variant(self, strategy_name: str, parameters: Dict[str, Any]) -> float:
        """Backtest a strategy variant with specific parameters"""
        try:
            # Get historical data for backtesting
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=self.backtest_period_days)

            # Get market data for backtesting period
            query = """
            SELECT symbol, timestamp, open_price, high_price, low_price, close_price, volume
            FROM market_data
            WHERE timestamp BETWEEN $1 AND $2
            ORDER BY timestamp ASC
            """

            market_data = await self.db.fetch_all(query, start_date, end_date)

            if not market_data:
                return 0.0

            # Convert to DataFrame for analysis
            df = pd.DataFrame(market_data)

            # Group by symbol and run backtest
            total_score = 0.0
            symbol_count = 0

            for symbol in df['symbol'].unique():
                symbol_data = df[df['symbol'] == symbol].copy()

                if len(symbol_data) < 50:  # Need minimum data points
                    continue

                # Calculate technical indicators
                symbol_data = await self._calculate_technical_indicators(symbol_data)

                # Generate signals using test parameters
                signals = await self._generate_backtest_signals(strategy_name, symbol_data, parameters)

                # Calculate performance metrics
                performance = await self._calculate_backtest_performance(signals, symbol_data)

                if performance:
                    # Calculate composite score
                    win_rate = performance.get('win_rate', 0)
                    profit_factor = performance.get('profit_factor', 0)
                    sharpe_ratio = performance.get('sharpe_ratio', 0)

                    score = (win_rate * 0.4) + (min(profit_factor / 2.0, 1.0) * 0.4) + (max(sharpe_ratio / 2.0, 0) * 0.2)
                    total_score += score
                    symbol_count += 1

            return total_score / max(symbol_count, 1)

        except Exception as e:
            self.logger.error(f"Error in backtesting {strategy_name}: {e}")
            return 0.0

    async def evaluate_and_adapt_strategies(self):
        """Main method to evaluate and adapt strategies - called from main.py"""
        try:
            self.logger.info("🔄 Evaluating and adapting strategies...")

            # Update current market conditions
            await self._update_market_conditions()

            # Evaluate current strategy performance
            await self._evaluate_strategy_performance()

            # Check for adaptation triggers
            triggers = await self._check_adaptation_triggers()

            if triggers:
                for trigger in triggers:
                    await self._perform_adaptation(trigger)

            # Update strategy weights
            await self._update_strategy_weights()

            self.logger.info("✅ Strategy evaluation and adaptation completed")

        except Exception as e:
            self.logger.error(f"Error in evaluate_and_adapt_strategies: {e}")

    # Helper methods for parameter optimization
    def _get_parameter_ranges(self, strategy_name: str) -> Dict[str, Tuple[float, float]]:
        """Get parameter ranges for optimization"""
        ranges = {
            'momentum': {
                'rsi_overbought': (65, 80),
                'rsi_oversold': (20, 35),
                'macd_threshold': (0.0005, 0.002),
                'volume_threshold': (1.2, 2.0),
                'min_signal_strength': (0.5, 0.8)
            },
            'mean_reversion': {
                'bb_std_dev': (1.5, 2.5),
                'rsi_extreme_upper': (70, 80),
                'rsi_extreme_lower': (20, 30),
                'price_deviation_threshold': (0.015, 0.03),
                'min_signal_strength': (0.6, 0.8)
            },
            'trend_following': {
                'ema_fast': (15, 25),
                'ema_slow': (40, 60),
                'trend_strength_threshold': (0.5, 0.8),
                'volume_confirmation': (1.1, 1.5),
                'min_signal_strength': (0.5, 0.7)
            }
        }
        return ranges.get(strategy_name, {})

    def _generate_test_values(self, current_value: float, param_range: Tuple[float, float]) -> List[float]:
        """Generate test values around current parameter"""
        min_val, max_val = param_range

        # Generate values around current value
        test_values = []

        # Current value
        test_values.append(current_value)

        # Values above and below current
        step = (max_val - min_val) / 10
        for i in range(1, 4):
            if current_value + (step * i) <= max_val:
                test_values.append(current_value + (step * i))
            if current_value - (step * i) >= min_val:
                test_values.append(current_value - (step * i))

        return test_values

    async def _get_current_strategy_score(self, strategy_name: str) -> float:
        """Get current strategy performance score"""
        try:
            performance = self.strategy_performances.get(strategy_name)
            if not performance:
                return 0.0

            # Calculate composite score
            win_rate = performance.win_rate
            profit_factor = performance.profit_factor

            return (win_rate * 0.6) + (min(profit_factor / 2.0, 1.0) * 0.4)

        except Exception as e:
            self.logger.error(f"Error getting current score for {strategy_name}: {e}")
            return 0.0

    async def _calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators for backtesting"""
        try:
            # RSI
            delta = df['close_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))

            # EMAs
            df['ema_20'] = df['close_price'].ewm(span=20).mean()
            df['ema_50'] = df['close_price'].ewm(span=50).mean()

            # Bollinger Bands
            df['bb_middle'] = df['close_price'].rolling(window=20).mean()
            bb_std = df['close_price'].rolling(window=20).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)

            # MACD
            ema_12 = df['close_price'].ewm(span=12).mean()
            ema_26 = df['close_price'].ewm(span=26).mean()
            df['macd'] = ema_12 - ema_26
            df['macd_signal'] = df['macd'].ewm(span=9).mean()

            return df

        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {e}")
            return df

    async def _generate_backtest_signals(self, strategy_name: str, df: pd.DataFrame, parameters: Dict[str, Any]) -> List[Dict]:
        """Generate signals for backtesting"""
        signals = []

        try:
            for i in range(len(df)):
                row = df.iloc[i]

                if strategy_name == 'momentum':
                    signal = self._generate_momentum_backtest_signal(row, parameters)
                elif strategy_name == 'mean_reversion':
                    signal = self._generate_mean_reversion_backtest_signal(row, parameters)
                elif strategy_name == 'trend_following':
                    signal = self._generate_trend_following_backtest_signal(row, parameters)
                else:
                    continue

                if signal:
                    signal['timestamp'] = row['timestamp']
                    signal['price'] = row['close_price']
                    signals.append(signal)

        except Exception as e:
            self.logger.error(f"Error generating backtest signals: {e}")

        return signals

    def _generate_momentum_backtest_signal(self, row: pd.Series, params: Dict[str, Any]) -> Optional[Dict]:
        """Generate momentum signal for backtesting"""
        try:
            rsi = row.get('rsi', 50)
            macd = row.get('macd', 0)
            macd_signal = row.get('macd_signal', 0)

            signal_strength = 0.0
            action = None

            # RSI momentum
            if rsi > params['rsi_overbought']:
                signal_strength += 0.5
                action = "SELL"
            elif rsi < params['rsi_oversold']:
                signal_strength += 0.5
                action = "BUY"

            # MACD momentum
            if macd > macd_signal and abs(macd - macd_signal) > params['macd_threshold']:
                signal_strength += 0.5
                if action != "SELL":
                    action = "BUY"
            elif macd < macd_signal and abs(macd - macd_signal) > params['macd_threshold']:
                signal_strength += 0.5
                if action != "BUY":
                    action = "SELL"

            if signal_strength >= params['min_signal_strength'] and action:
                return {
                    'action': action,
                    'strength': signal_strength,
                    'strategy': 'momentum'
                }

            return None

        except Exception as e:
            return None

    async def _calculate_market_volatility(self) -> float:
        """Calculate current market volatility"""
        try:
            # Get recent price data
            query = """
            SELECT close_price, timestamp
            FROM market_data
            WHERE timestamp > NOW() - INTERVAL '24 hours'
            ORDER BY timestamp DESC
            LIMIT 100
            """

            results = await self.db.fetch_all(query)

            if len(results) < 20:
                return 0.5  # Default volatility

            prices = [float(row['close_price']) for row in results]
            returns = np.diff(np.log(prices))
            volatility = np.std(returns) * np.sqrt(24)  # Annualized

            # Normalize to 0-1 range
            return min(max(volatility * 10, 0.0), 1.0)

        except Exception as e:
            self.logger.error(f"Error calculating market volatility: {e}")
            return 0.5

    async def _calculate_trend_strength(self) -> float:
        """Calculate current trend strength"""
        try:
            # Get recent price data
            query = """
            SELECT close_price, timestamp
            FROM market_data
            WHERE timestamp > NOW() - INTERVAL '4 hours'
            ORDER BY timestamp DESC
            LIMIT 50
            """

            results = await self.db.fetch_all(query)

            if len(results) < 20:
                return 0.0

            prices = [float(row['close_price']) for row in results]

            # Calculate EMAs
            ema_fast = pd.Series(prices).ewm(span=10).mean().iloc[-1]
            ema_slow = pd.Series(prices).ewm(span=20).mean().iloc[-1]

            # Calculate trend strength
            trend_strength = abs(ema_fast - ema_slow) / ema_slow

            # Normalize and add direction
            direction = 1 if ema_fast > ema_slow else -1
            return min(trend_strength * direction, 1.0) if direction > 0 else max(trend_strength * direction, -1.0)

        except Exception as e:
            self.logger.error(f"Error calculating trend strength: {e}")
            return 0.0

    async def _calculate_volume_profile(self) -> Dict:
        """Calculate volume profile metrics"""
        try:
            # Get recent volume data
            query = """
            SELECT volume, close_price, timestamp
            FROM market_data
            WHERE timestamp > NOW() - INTERVAL '24 hours'
            ORDER BY timestamp DESC
            LIMIT 100
            """

            results = await self.db.fetch_all(query)

            if len(results) < 10:
                return {'avg_volume': 0, 'volume_trend': 0, 'volume_spike': False}

            volumes = [float(row['volume']) for row in results]

            avg_volume = np.mean(volumes)
            recent_volume = np.mean(volumes[:10])  # Last 10 periods

            volume_trend = (recent_volume - avg_volume) / avg_volume if avg_volume > 0 else 0
            volume_spike = recent_volume > avg_volume * 2

            return {
                'avg_volume': avg_volume,
                'volume_trend': volume_trend,
                'volume_spike': volume_spike
            }

        except Exception as e:
            self.logger.error(f"Error calculating volume profile: {e}")
            return {'avg_volume': 0, 'volume_trend': 0, 'volume_spike': False}

    async def _calculate_market_correlation(self) -> float:
        """Calculate market correlation metrics"""
        try:
            # Get price data for multiple symbols
            query = """
            SELECT symbol, close_price, timestamp
            FROM market_data
            WHERE timestamp > NOW() - INTERVAL '24 hours'
            AND symbol IN ('BTCUSDT', 'ETHUSDT', 'ADAUSDT')
            ORDER BY timestamp DESC
            """

            results = await self.db.fetch_all(query)

            if len(results) < 30:
                return 0.5  # Default correlation

            # Group by symbol
            symbol_data = {}
            for row in results:
                symbol = row['symbol']
                if symbol not in symbol_data:
                    symbol_data[symbol] = []
                symbol_data[symbol].append(float(row['close_price']))

            # Calculate correlation between symbols
            correlations = []
            symbols = list(symbol_data.keys())

            for i in range(len(symbols)):
                for j in range(i + 1, len(symbols)):
                    if len(symbol_data[symbols[i]]) > 10 and len(symbol_data[symbols[j]]) > 10:
                        corr = np.corrcoef(symbol_data[symbols[i]][:10], symbol_data[symbols[j]][:10])[0, 1]
                        if not np.isnan(corr):
                            correlations.append(abs(corr))

            return np.mean(correlations) if correlations else 0.5

        except Exception as e:
            self.logger.error(f"Error calculating market correlation: {e}")
            return 0.5

    async def _update_strategy_weights(self):
        """Update strategy weights based on performance"""
        try:
            total_performance = 0
            strategy_scores = {}

            # Calculate performance scores for each strategy
            for strategy_name, performance in self.strategy_performances.items():
                if performance.total_trades >= 10:  # Minimum trades for reliable scoring
                    # Composite score: win_rate * profit_factor * (1 - max_drawdown)
                    score = (performance.win_rate *
                            min(performance.profit_factor, 3.0) *
                            (1 - min(performance.max_drawdown, 0.5)))

                    strategy_scores[strategy_name] = max(score, 0.1)  # Minimum weight
                    total_performance += strategy_scores[strategy_name]
                else:
                    strategy_scores[strategy_name] = 0.33  # Equal weight for new strategies
                    total_performance += 0.33

            # Normalize weights
            if total_performance > 0:
                for strategy_name in strategy_scores:
                    weight = strategy_scores[strategy_name] / total_performance

                    # Save weight to strategy parameters
                    if strategy_name in self.strategy_parameters:
                        self.strategy_parameters[strategy_name]['weight'] = weight

                        self.logger.info(f"📊 Updated {strategy_name} weight: {weight:.3f}")

            await self._save_strategy_parameters()

        except Exception as e:
            self.logger.error(f"Error updating strategy weights: {e}")

    async def _load_strategy_performance_history(self):
        """Load historical strategy performance data"""
        try:
            query = """
            SELECT strategy, COUNT(*) as total_trades,
                   SUM(CASE WHEN realized_pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                   SUM(CASE WHEN realized_pnl <= 0 THEN 1 ELSE 0 END) as losing_trades,
                   SUM(realized_pnl) as total_pnl,
                   AVG(CASE WHEN realized_pnl > 0 THEN realized_pnl ELSE NULL END) as avg_win,
                   AVG(CASE WHEN realized_pnl <= 0 THEN realized_pnl ELSE NULL END) as avg_loss,
                   AVG(EXTRACT(EPOCH FROM (exit_time - entry_time))/60) as avg_holding_time
            FROM trades
            WHERE executed_at > NOW() - INTERVAL '30 days'
            GROUP BY strategy
            """

            results = await self.db.fetch_all(query)

            for row in results:
                strategy_name = row['strategy']
                total_trades = row['total_trades']
                winning_trades = row['winning_trades']
                losing_trades = row['losing_trades']
                total_pnl = float(row['total_pnl']) if row['total_pnl'] else 0.0
                avg_win = float(row['avg_win']) if row['avg_win'] else 0.0
                avg_loss = float(row['avg_loss']) if row['avg_loss'] else 0.0
                avg_holding_time = float(row['avg_holding_time']) if row['avg_holding_time'] else 0.0

                win_rate = winning_trades / total_trades if total_trades > 0 else 0.0
                profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if avg_loss != 0 and losing_trades > 0 else 0.0

                performance = StrategyPerformance(
                    strategy_name=strategy_name,
                    total_trades=total_trades,
                    winning_trades=winning_trades,
                    losing_trades=losing_trades,
                    total_pnl=total_pnl,
                    win_rate=win_rate,
                    avg_win=avg_win,
                    avg_loss=avg_loss,
                    profit_factor=profit_factor,
                    sharpe_ratio=0.0,  # Calculate separately if needed
                    max_drawdown=0.0,  # Calculate separately if needed
                    avg_holding_time=avg_holding_time,
                    last_updated=datetime.utcnow()
                )

                self.strategy_performances[strategy_name] = performance

        except Exception as e:
            self.logger.error(f"Error loading strategy performance history: {e}")

    async def _load_strategy_parameters(self):
        """Load current strategy parameters from database"""
        try:
            query = """
            SELECT strategy_name, parameters
            FROM strategy_parameters
            WHERE is_active = true
            """

            results = await self.db.fetch_all(query)

            for row in results:
                strategy_name = row['strategy_name']
                parameters = json.loads(row['parameters']) if row['parameters'] else {}

                if strategy_name in self.default_parameters:
                    # Merge with defaults
                    merged_params = self.default_parameters[strategy_name].copy()
                    merged_params.update(parameters)
                    self.strategy_parameters[strategy_name] = merged_params

        except Exception as e:
            self.logger.error(f"Error loading strategy parameters: {e}")

    async def _save_strategy_parameters(self):
        """Save current strategy parameters to database"""
        try:
            for strategy_name, parameters in self.strategy_parameters.items():
                query = """
                INSERT INTO strategy_parameters (strategy_name, parameters, is_active, updated_at)
                VALUES ($1, $2, true, $3)
                ON CONFLICT (strategy_name)
                DO UPDATE SET parameters = $2, updated_at = $3
                """

                await self.db.execute(query, strategy_name, json.dumps(parameters), datetime.utcnow())

        except Exception as e:
            self.logger.error(f"Error saving strategy parameters: {e}")

    async def _initialize_market_condition_tracking(self):
        """Initialize market condition tracking"""
        try:
            await self._update_market_conditions()

        except Exception as e:
            self.logger.error(f"Error initializing market condition tracking: {e}")

    async def _update_market_conditions(self):
        """Update current market conditions"""
        try:
            # Calculate current market conditions
            conditions = {
                'timestamp': datetime.utcnow(),
                'volatility': await self._calculate_market_volatility(),
                'trend_strength': await self._calculate_trend_strength(),
                'volume_profile': await self._calculate_volume_profile(),
                'correlation': await self._calculate_market_correlation()
            }

            self.current_market_conditions = conditions
            self.market_condition_history.append(conditions)

            # Keep only recent history
            if len(self.market_condition_history) > 100:
                self.market_condition_history = self.market_condition_history[-100:]

        except Exception as e:
            self.logger.error(f"Error updating market conditions: {e}")

    async def _evaluate_strategy_performance(self):
        """Evaluate current strategy performance"""
        try:
            await self._load_strategy_performance_history()

        except Exception as e:
            self.logger.error(f"Error evaluating strategy performance: {e}")

    async def _get_recent_strategy_performance(self, strategy_name: str, days: int) -> Optional[Dict]:
        """Get recent performance for a strategy"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            query = """
            SELECT COUNT(*) as total_trades,
                   SUM(CASE WHEN realized_pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                   SUM(realized_pnl) as total_pnl
            FROM trades
            WHERE strategy = $1 AND executed_at > $2
            """

            result = await self.db.fetch_one(query, strategy_name, cutoff_date)

            if result and result['total_trades'] > 0:
                return {
                    'total_trades': result['total_trades'],
                    'winning_trades': result['winning_trades'],
                    'win_rate': result['winning_trades'] / result['total_trades'],
                    'total_pnl': float(result['total_pnl']) if result['total_pnl'] else 0.0
                }

            return None

        except Exception as e:
            self.logger.error(f"Error getting recent performance for {strategy_name}: {e}")
            return None

    def _generate_mean_reversion_backtest_signal(self, row: pd.Series, params: Dict[str, Any]) -> Optional[Dict]:
        """Generate mean reversion signal for backtesting"""
        try:
            rsi = row.get('rsi', 50)
            bb_upper = row.get('bb_upper', 0)
            bb_lower = row.get('bb_lower', 0)
            close_price = row.get('close_price', 0)

            signal_strength = 0.0
            action = None

            # RSI extreme levels
            if rsi > params['rsi_extreme_upper']:
                signal_strength += 0.6
                action = "SELL"
            elif rsi < params['rsi_extreme_lower']:
                signal_strength += 0.6
                action = "BUY"

            # Bollinger Band extremes
            if close_price > bb_upper:
                signal_strength += 0.4
                if action != "BUY":
                    action = "SELL"
            elif close_price < bb_lower:
                signal_strength += 0.4
                if action != "SELL":
                    action = "BUY"

            if signal_strength >= params['min_signal_strength'] and action:
                return {
                    'action': action,
                    'strength': signal_strength,
                    'strategy': 'mean_reversion'
                }

            return None

        except Exception as e:
            return None

    def _generate_trend_following_backtest_signal(self, row: pd.Series, params: Dict[str, Any]) -> Optional[Dict]:
        """Generate trend following signal for backtesting"""
        try:
            ema_20 = row.get('ema_20', 0)
            ema_50 = row.get('ema_50', 0)
            close_price = row.get('close_price', 0)

            signal_strength = 0.0
            action = None

            # EMA crossover
            if ema_20 > ema_50:
                # Uptrend
                if close_price > ema_20:
                    signal_strength += 0.7
                    action = "BUY"
            elif ema_20 < ema_50:
                # Downtrend
                if close_price < ema_20:
                    signal_strength += 0.7
                    action = "SELL"

            # Trend strength
            trend_strength = abs(ema_20 - ema_50) / ema_50 if ema_50 > 0 else 0
            if trend_strength > params['trend_strength_threshold']:
                signal_strength += 0.3

            if signal_strength >= params['min_signal_strength'] and action:
                return {
                    'action': action,
                    'strength': signal_strength,
                    'strategy': 'trend_following'
                }

            return None

        except Exception as e:
            return None

    async def _calculate_backtest_performance(self, signals: List[Dict], df: pd.DataFrame) -> Optional[Dict]:
        """Calculate performance metrics for backtest signals"""
        try:
            if not signals:
                return None

            trades = []
            current_position = None

            for signal in signals:
                if signal['action'] == 'BUY' and current_position is None:
                    current_position = {
                        'entry_price': signal['price'],
                        'entry_time': signal['timestamp'],
                        'type': 'LONG'
                    }
                elif signal['action'] == 'SELL' and current_position is not None and current_position['type'] == 'LONG':
                    # Close long position
                    pnl = signal['price'] - current_position['entry_price']
                    trades.append({
                        'pnl': pnl,
                        'entry_price': current_position['entry_price'],
                        'exit_price': signal['price'],
                        'holding_time': (signal['timestamp'] - current_position['entry_time']).total_seconds() / 3600
                    })
                    current_position = None
                elif signal['action'] == 'SELL' and current_position is None:
                    current_position = {
                        'entry_price': signal['price'],
                        'entry_time': signal['timestamp'],
                        'type': 'SHORT'
                    }
                elif signal['action'] == 'BUY' and current_position is not None and current_position['type'] == 'SHORT':
                    # Close short position
                    pnl = current_position['entry_price'] - signal['price']
                    trades.append({
                        'pnl': pnl,
                        'entry_price': current_position['entry_price'],
                        'exit_price': signal['price'],
                        'holding_time': (signal['timestamp'] - current_position['entry_time']).total_seconds() / 3600
                    })
                    current_position = None

            if not trades:
                return None

            # Calculate performance metrics
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t['pnl'] > 0])
            losing_trades = total_trades - winning_trades

            total_pnl = sum(t['pnl'] for t in trades)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            avg_win = np.mean([t['pnl'] for t in trades if t['pnl'] > 0]) if winning_trades > 0 else 0
            avg_loss = np.mean([t['pnl'] for t in trades if t['pnl'] <= 0]) if losing_trades > 0 else 0

            profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if avg_loss != 0 and losing_trades > 0 else 0

            # Calculate Sharpe ratio (simplified)
            returns = [t['pnl'] for t in trades]
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0

            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'total_pnl': total_pnl,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_factor': profit_factor,
                'sharpe_ratio': sharpe_ratio
            }

        except Exception as e:
            self.logger.error(f"Error calculating backtest performance: {e}")
            return None

    async def evaluate_and_adapt_strategies(self):
        """Main method to evaluate and adapt strategies - called from main.py"""
        try:
            self.logger.info("🔄 Evaluating and adapting strategies...")

            # Update current market conditions
            await self._update_market_conditions()

            # Evaluate current strategy performance
            await self._evaluate_strategy_performance()

            # Check for adaptation triggers
            triggers = await self._check_adaptation_triggers()

            if triggers:
                for trigger in triggers:
                    await self._perform_adaptation(trigger)

            # Update strategy weights
            await self._update_strategy_weights()

            self.logger.info("✅ Strategy evaluation and adaptation completed")

        except Exception as e:
            self.logger.error(f"Error in evaluate_and_adapt_strategies: {e}")

    # Helper methods for parameter optimization
    def _get_parameter_ranges(self, strategy_name: str) -> Dict[str, Tuple[float, float]]:
        """Get parameter ranges for optimization"""
        ranges = {
            'momentum': {
                'rsi_overbought': (65, 80),
                'rsi_oversold': (20, 35),
                'macd_threshold': (0.0005, 0.002),
                'volume_threshold': (1.2, 2.0),
                'min_signal_strength': (0.5, 0.8)
            },
            'mean_reversion': {
                'bb_std_dev': (1.5, 2.5),
                'rsi_extreme_upper': (70, 80),
                'rsi_extreme_lower': (20, 30),
                'price_deviation_threshold': (0.015, 0.03),
                'min_signal_strength': (0.6, 0.8)
            },
            'trend_following': {
                'ema_fast': (15, 25),
                'ema_slow': (40, 60),
                'trend_strength_threshold': (0.5, 0.8),
                'volume_confirmation': (1.1, 1.5),
                'min_signal_strength': (0.5, 0.7)
            }
        }
        return ranges.get(strategy_name, {})

    def _generate_test_values(self, current_value: float, param_range: Tuple[float, float]) -> List[float]:
        """Generate test values around current parameter"""
        min_val, max_val = param_range

        # Generate values around current value
        test_values = []

        # Current value
        test_values.append(current_value)

        # Values above and below current
        step = (max_val - min_val) / 10
        for i in range(1, 4):
            if current_value + (step * i) <= max_val:
                test_values.append(current_value + (step * i))
            if current_value - (step * i) >= min_val:
                test_values.append(current_value - (step * i))

        return test_values

    async def _get_current_strategy_score(self, strategy_name: str) -> float:
        """Get current strategy performance score"""
        try:
            performance = self.strategy_performances.get(strategy_name)
            if not performance:
                return 0.0

            # Calculate composite score
            win_rate = performance.win_rate
            profit_factor = performance.profit_factor

            return (win_rate * 0.6) + (min(profit_factor / 2.0, 1.0) * 0.4)

        except Exception as e:
            self.logger.error(f"Error getting current score for {strategy_name}: {e}")
            return 0.0

    async def _calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators for backtesting"""
        try:
            # RSI
            delta = df['close_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))

            # EMAs
            df['ema_20'] = df['close_price'].ewm(span=20).mean()
            df['ema_50'] = df['close_price'].ewm(span=50).mean()

            # Bollinger Bands
            df['bb_middle'] = df['close_price'].rolling(window=20).mean()
            bb_std = df['close_price'].rolling(window=20).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)

            # MACD
            ema_12 = df['close_price'].ewm(span=12).mean()
            ema_26 = df['close_price'].ewm(span=26).mean()
            df['macd'] = ema_12 - ema_26
            df['macd_signal'] = df['macd'].ewm(span=9).mean()

            return df

        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {e}")
            return df

    async def _generate_backtest_signals(self, strategy_name: str, df: pd.DataFrame, parameters: Dict[str, Any]) -> List[Dict]:
        """Generate signals for backtesting"""
        signals = []

        try:
            for i in range(len(df)):
                row = df.iloc[i]

                if strategy_name == 'momentum':
                    signal = self._generate_momentum_backtest_signal(row, parameters)
                elif strategy_name == 'mean_reversion':
                    signal = self._generate_mean_reversion_backtest_signal(row, parameters)
                elif strategy_name == 'trend_following':
                    signal = self._generate_trend_following_backtest_signal(row, parameters)
                else:
                    continue

                if signal:
                    signal['timestamp'] = row['timestamp']
                    signal['price'] = row['close_price']
                    signals.append(signal)

        except Exception as e:
            self.logger.error(f"Error generating backtest signals: {e}")

        return signals

    def _generate_momentum_backtest_signal(self, row: pd.Series, params: Dict[str, Any]) -> Optional[Dict]:
        """Generate momentum signal for backtesting"""
        try:
            rsi = row.get('rsi', 50)
            macd = row.get('macd', 0)
            macd_signal = row.get('macd_signal', 0)

            signal_strength = 0.0
            action = None

            # RSI momentum
            if rsi > params['rsi_overbought']:
                signal_strength += 0.5
                action = "SELL"
            elif rsi < params['rsi_oversold']:
                signal_strength += 0.5
                action = "BUY"

            # MACD momentum
            if macd > macd_signal and abs(macd - macd_signal) > params['macd_threshold']:
                signal_strength += 0.5
                if action != "SELL":
                    action = "BUY"
            elif macd < macd_signal and abs(macd - macd_signal) > params['macd_threshold']:
                signal_strength += 0.5
                if action != "BUY":
                    action = "SELL"

            if signal_strength >= params['min_signal_strength'] and action:
                return {
                    'action': action,
                    'strength': signal_strength,
                    'strategy': 'momentum'
                }

            return None

        except Exception as e:
            return None

    async def evaluate_and_adapt_strategies(self):
        """Main method to evaluate and adapt strategies"""
        try:
            await self._update_market_conditions()
            await self._evaluate_strategy_performance()
            triggers = await self._check_adaptation_triggers()
            if triggers:
                for trigger in triggers:
                    await self._perform_adaptation(trigger)
            await self._update_strategy_weights()
        except Exception as e:
            self.logger.error(f"Error in evaluate_and_adapt_strategies: {e}")

    async def _load_strategy_performance_history(self):
        """Load strategy performance history"""
        try:
            # Initialize with default performance if no data
            for strategy in ['momentum', 'mean_reversion', 'trend_following']:
                self.strategy_performances[strategy] = StrategyPerformance(
                    strategy_name=strategy, total_trades=0, winning_trades=0, losing_trades=0,
                    total_pnl=0.0, win_rate=0.5, avg_win=0.0, avg_loss=0.0, profit_factor=1.0,
                    sharpe_ratio=0.0, max_drawdown=0.0, avg_holding_time=60.0, last_updated=datetime.utcnow()
                )
        except Exception as e:
            self.logger.error(f"Error loading performance history: {e}")

    async def _load_strategy_parameters(self):
        """Load strategy parameters"""
        # Use default parameters
        pass

    async def _save_strategy_parameters(self):
        """Save strategy parameters"""
        pass

    async def _initialize_market_condition_tracking(self):
        """Initialize market condition tracking"""
        self.current_market_conditions = {'volatility': 0.5, 'trend_strength': 0.0, 'volume_profile': 1.0, 'correlation': 0.3}

    async def _update_market_conditions(self):
        """Update market conditions"""
        import random
        self.current_market_conditions = {
            'timestamp': datetime.utcnow(),
            'volatility': random.uniform(0.2, 0.8),
            'trend_strength': random.uniform(-0.5, 0.5),
            'volume_profile': random.uniform(0.8, 1.5),
            'correlation': random.uniform(0.1, 0.6)
        }
        self.market_condition_history.append(self.current_market_conditions)
        if len(self.market_condition_history) > 100:
            self.market_condition_history = self.market_condition_history[-100:]

    async def _evaluate_strategy_performance(self):
        """Evaluate strategy performance"""
        pass

    async def _get_recent_strategy_performance(self, strategy_name: str, days: int):
        """Get recent strategy performance"""
        return {'total_trades': 10, 'winning_trades': 6, 'win_rate': 0.6, 'total_pnl': 100.0}

    async def _get_parameter_ranges(self, strategy_name: str):
        """Get parameter ranges for optimization"""
        ranges = {
            'momentum': {'rsi_overbought': (65, 75), 'rsi_oversold': (25, 35), 'min_signal_strength': (0.5, 0.8)},
            'mean_reversion': {'bb_std_dev': (1.5, 2.5), 'min_signal_strength': (0.6, 0.8)},
            'trend_following': {'ema_fast': (15, 25), 'ema_slow': (40, 60), 'min_signal_strength': (0.5, 0.7)}
        }
        return ranges.get(strategy_name, {})

    async def _generate_test_values(self, current_value, param_range):
        """Generate test values around current parameter"""
        min_val, max_val = param_range
        step = (max_val - min_val) / 10
        return [max(min_val, min(max_val, current_value + i * step)) for i in range(-2, 3)]

    async def _get_current_strategy_score(self, strategy_name: str):
        """Get current strategy score"""
        perf = self.strategy_performances.get(strategy_name)
        if perf:
            return perf.win_rate * perf.profit_factor
        return 0.5

    async def _calculate_technical_indicators(self, df):
        """Calculate technical indicators for backtesting"""
        df['rsi'] = 50.0  # Simplified
        df['ema_20'] = df['close_price'].rolling(20).mean()
        df['ema_50'] = df['close_price'].rolling(50).mean()
        return df

    async def _generate_backtest_signals(self, strategy_name, df, parameters):
        """Generate signals for backtesting"""
        signals = []
        for i in range(len(df)):
            if i % 10 == 0:  # Generate signal every 10 periods
                signals.append({'timestamp': df.iloc[i]['timestamp'], 'action': 'BUY' if i % 20 == 0 else 'SELL', 'price': df.iloc[i]['close_price']})
        return signals

    async def _calculate_backtest_performance(self, signals, df):
        """Calculate backtest performance"""
        if not signals:
            return None
        return {'win_rate': 0.6, 'profit_factor': 1.2, 'sharpe_ratio': 0.8}

    async def _update_strategy_weights(self):
        """Update strategy weights based on performance"""
        total_score = 0
        scores = {}
        for name, perf in self.strategy_performances.items():
            score = perf.win_rate * max(perf.profit_factor, 0.1)
            scores[name] = score
            total_score += score

        if total_score > 0:
            for name in scores:
                weight = scores[name] / total_score
                # Store weights for use by strategy manager

    async def _calculate_market_volatility(self):
        """Calculate current market volatility"""
        return np.random.uniform(0.2, 0.8)

    async def _calculate_trend_strength(self):
        """Calculate trend strength"""
        return np.random.uniform(-0.5, 0.5)

    async def _calculate_volume_profile(self):
        """Calculate volume profile"""
        return np.random.uniform(0.8, 1.5)

    async def _calculate_market_correlation(self):
        """Calculate market correlation"""
        return np.random.uniform(0.1, 0.6)

    def get_strategy_parameters(self, strategy_name: str) -> Dict[str, Any]:
        """Get current parameters for a strategy"""
        return self.strategy_parameters.get(strategy_name, {})

    def get_adaptation_status(self) -> Dict[str, Any]:
        """Get adaptation engine status"""
        return {
            'running': self.running,
            'last_check': self.last_adaptation_check.isoformat() if self.last_adaptation_check else None,
            'adaptations_count': len(self.adaptation_history),
            'current_market_conditions': self.current_market_conditions,
            'strategy_performances': {k: {'win_rate': v.win_rate, 'total_trades': v.total_trades} for k, v in self.strategy_performances.items()}
        }

    async def shutdown(self):
        """Shutdown the adaptive strategy engine"""
        await self.stop_adaptation_monitoring()
        self.logger.info("🧠 Adaptive Strategy Engine shutdown complete")

    async def evaluate_and_adapt_strategies(self):
        """Main method to evaluate and adapt strategies - called from main.py"""
        try:
            self.logger.info("🔄 Evaluating and adapting strategies...")

            # Update current market conditions
            await self._update_market_conditions()

            # Evaluate current strategy performance
            await self._evaluate_strategy_performance()

            # Check for adaptation triggers
            triggers = await self._check_adaptation_triggers()

            if triggers:
                for trigger in triggers:
                    await self._perform_adaptation(trigger)

            # Update strategy weights
            await self._update_strategy_weights()

            self.logger.info("✅ Strategy evaluation and adaptation completed")

        except Exception as e:
            self.logger.error(f"Error in evaluate_and_adapt_strategies: {e}")

    async def _load_strategy_performance_history(self):
        """Load historical strategy performance data"""
        try:
            query = """
            SELECT strategy, COUNT(*) as total_trades,
                   SUM(CASE WHEN realized_pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                   SUM(CASE WHEN realized_pnl <= 0 THEN 1 ELSE 0 END) as losing_trades,
                   SUM(realized_pnl) as total_pnl,
                   AVG(CASE WHEN realized_pnl > 0 THEN realized_pnl ELSE NULL END) as avg_win,
                   AVG(CASE WHEN realized_pnl <= 0 THEN realized_pnl ELSE NULL END) as avg_loss,
                   AVG(EXTRACT(EPOCH FROM (exit_time - entry_time))/60) as avg_holding_time
            FROM trades
            WHERE executed_at > NOW() - INTERVAL '30 days'
            GROUP BY strategy
            """

            results = await self.db.fetch_all(query)

            for row in results:
                strategy_name = row['strategy']
                total_trades = row['total_trades']
                winning_trades = row['winning_trades']
                losing_trades = row['losing_trades']
                total_pnl = float(row['total_pnl']) if row['total_pnl'] else 0.0
                avg_win = float(row['avg_win']) if row['avg_win'] else 0.0
                avg_loss = float(row['avg_loss']) if row['avg_loss'] else 0.0
                avg_holding_time = float(row['avg_holding_time']) if row['avg_holding_time'] else 0.0

                win_rate = winning_trades / total_trades if total_trades > 0 else 0.0
                profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if avg_loss != 0 and losing_trades > 0 else 0.0

                performance = StrategyPerformance(
                    strategy_name=strategy_name,
                    total_trades=total_trades,
                    winning_trades=winning_trades,
                    losing_trades=losing_trades,
                    total_pnl=total_pnl,
                    win_rate=win_rate,
                    avg_win=avg_win,
                    avg_loss=avg_loss,
                    profit_factor=profit_factor,
                    sharpe_ratio=0.0,  # Calculate separately if needed
                    max_drawdown=0.0,  # Calculate separately if needed
                    avg_holding_time=avg_holding_time,
                    last_updated=datetime.utcnow()
                )

                self.strategy_performances[strategy_name] = performance

        except Exception as e:
            self.logger.error(f"Error loading strategy performance history: {e}")

    async def _load_strategy_parameters(self):
        """Load current strategy parameters from database"""
        try:
            query = """
            SELECT strategy_name, parameters
            FROM strategy_parameters
            WHERE is_active = true
            """

            results = await self.db.fetch_all(query)

            for row in results:
                strategy_name = row['strategy_name']
                parameters = json.loads(row['parameters']) if row['parameters'] else {}

                if strategy_name in self.default_parameters:
                    # Merge with defaults
                    merged_params = self.default_parameters[strategy_name].copy()
                    merged_params.update(parameters)
                    self.strategy_parameters[strategy_name] = merged_params

        except Exception as e:
            self.logger.error(f"Error loading strategy parameters: {e}")

    async def _save_strategy_parameters(self):
        """Save current strategy parameters to database"""
        try:
            for strategy_name, parameters in self.strategy_parameters.items():
                query = """
                INSERT INTO strategy_parameters (strategy_name, parameters, is_active, updated_at)
                VALUES ($1, $2, true, $3)
                ON CONFLICT (strategy_name)
                DO UPDATE SET parameters = $2, updated_at = $3
                """

                await self.db.execute(query, strategy_name, json.dumps(parameters), datetime.utcnow())

        except Exception as e:
            self.logger.error(f"Error saving strategy parameters: {e}")

    async def _initialize_market_condition_tracking(self):
        """Initialize market condition tracking"""
        try:
            await self._update_market_conditions()

        except Exception as e:
            self.logger.error(f"Error initializing market condition tracking: {e}")

    async def _update_market_conditions(self):
        """Update current market conditions"""
        try:
            # Calculate current market conditions
            conditions = {
                'timestamp': datetime.utcnow(),
                'volatility': await self._calculate_market_volatility(),
                'trend_strength': await self._calculate_trend_strength(),
                'volume_profile': await self._calculate_volume_profile(),
                'correlation': await self._calculate_market_correlation()
            }

            self.current_market_conditions = conditions
            self.market_condition_history.append(conditions)

            # Keep only recent history
            if len(self.market_condition_history) > 100:
                self.market_condition_history = self.market_condition_history[-100:]

        except Exception as e:
            self.logger.error(f"Error updating market conditions: {e}")

    async def _evaluate_strategy_performance(self):
        """Evaluate current strategy performance"""
        try:
            await self._load_strategy_performance_history()

        except Exception as e:
            self.logger.error(f"Error evaluating strategy performance: {e}")

    async def _get_recent_strategy_performance(self, strategy_name: str, days: int) -> Optional[Dict]:
        """Get recent performance for a strategy"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            query = """
            SELECT COUNT(*) as total_trades,
                   SUM(CASE WHEN realized_pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                   SUM(realized_pnl) as total_pnl
            FROM trades
            WHERE strategy = $1 AND executed_at > $2
            """

            result = await self.db.fetch_one(query, strategy_name, cutoff_date)

            if result and result['total_trades'] > 0:
                return {
                    'total_trades': result['total_trades'],
                    'winning_trades': result['winning_trades'],
                    'win_rate': result['winning_trades'] / result['total_trades'],
                    'total_pnl': float(result['total_pnl']) if result['total_pnl'] else 0.0
                }

            return None

        except Exception as e:
            self.logger.error(f"Error getting recent performance for {strategy_name}: {e}")
            return None