"""
Deployment Script for Super-GPT Trading Bot
Handles deployment, setup, and production configuration
"""
import os
import sys
import json
import yaml
import shutil
import subprocess
import argparse
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import asyncio
import time
import requests
import tempfile
import zipfile
import hashlib

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("Deployment")


class DeploymentManager:
    """
    Deployment Manager for Super-GPT Trading Bot
    
    Features:
    - Environment setup and configuration
    - Dependency installation
    - Database initialization
    - Service deployment
    - Health checks and monitoring
    - Backup and rollback
    - Security hardening
    - Docker containerization
    - CI/CD integration
    """
    
    def __init__(self, environment: str = "development"):
        self.environment = environment
        self.project_root = Path(__file__).parent
        self.deployment_config = self._load_deployment_config()
        
        # Deployment paths
        self.config_dir = self.project_root / "config"
        self.logs_dir = self.project_root / "logs"
        self.data_dir = self.project_root / "data"
        self.models_dir = self.project_root / "models"
        self.backup_dir = self.project_root / "backups"
        
        # Create directories
        self._create_directories()
        
        # Deployment state
        self.deployment_id = f"deploy_{int(time.time())}"
        self.deployment_log = []
        
        logger.info(f"DeploymentManager initialized for environment: {environment}")
    
    def _load_deployment_config(self) -> Dict[str, Any]:
        """Load deployment configuration"""
        config_file = self.project_root / "deployment.yaml"
        
        if config_file.exists():
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        else:
            return self._create_default_deployment_config()
    
    def _create_default_deployment_config(self) -> Dict[str, Any]:
        """Create default deployment configuration"""
        return {
            'environments': {
                'development': {
                    'python_version': '3.9',
                    'install_gpu_support': False,
                    'database_setup': True,
                    'redis_setup': True,
                    'monitoring_enabled': True,
                    'backup_enabled': True,
                    'security_hardening': False,
                    'docker_enabled': False
                },
                'staging': {
                    'python_version': '3.9',
                    'install_gpu_support': True,
                    'database_setup': True,
                    'redis_setup': True,
                    'monitoring_enabled': True,
                    'backup_enabled': True,
                    'security_hardening': True,
                    'docker_enabled': True
                },
                'production': {
                    'python_version': '3.9',
                    'install_gpu_support': True,
                    'database_setup': True,
                    'redis_setup': True,
                    'monitoring_enabled': True,
                    'backup_enabled': True,
                    'security_hardening': True,
                    'docker_enabled': True
                }
            },
            'services': {
                'database': {
                    'type': 'postgresql',
                    'version': '13',
                    'port': 5432,
                    'max_connections': 100,
                    'shared_preload_libraries': 'pg_stat_statements'
                },
                'redis': {
                    'version': '6',
                    'port': 6379,
                    'max_memory': '512mb',
                    'max_memory_policy': 'allkeys-lru'
                },
                'nginx': {
                    'version': '1.20',
                    'port': 80,
                    'ssl_port': 443,
                    'client_max_body_size': '100m'
                }
            },
            'monitoring': {
                'prometheus': {
                    'enabled': True,
                    'port': 9090,
                    'retention': '30d'
                },
                'grafana': {
                    'enabled': True,
                    'port': 3000,
                    'admin_password': 'admin123'
                },
                'alerts': {
                    'enabled': True,
                    'webhook_url': None,
                    'email_notifications': True
                }
            },
            'backup': {
                'schedule': '0 2 * * *',
                'retention_days': 30,
                'compression': True,
                'encryption': True,
                'remote_storage': {
                    'enabled': False,
                    'provider': 'aws_s3',
                    'bucket': 'trading-bot-backups',
                    'region': 'us-east-1'
                }
            }
        }
    
    def _create_directories(self):
        """Create necessary directories"""
        directories = [
            self.config_dir,
            self.logs_dir,
            self.data_dir,
            self.models_dir,
            self.backup_dir,
            self.project_root / "workspace" / "input",
            self.project_root / "workspace" / "output",
            self.project_root / "static",
            self.project_root / "temp"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {directory}")
    
    def deploy(self, components: List[str] = None):
        """Deploy the trading bot"""
        try:
            logger.info(f"Starting deployment {self.deployment_id}")
            
            # Default components
            if components is None:
                components = ['system', 'dependencies', 'database', 'services', 'configuration', 'application']
            
            # Pre-deployment checks
            self._pre_deployment_checks()
            
            # Deploy components
            for component in components:
                self._deploy_component(component)
            
            # Post-deployment tasks
            self._post_deployment_tasks()
            
            # Health checks
            self._run_health_checks()
            
            logger.info(f"Deployment {self.deployment_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Deployment failed: {e}")
            self._rollback()
            raise
    
    def _deploy_component(self, component: str):
        """Deploy a specific component"""
        logger.info(f"Deploying component: {component}")
        
        start_time = time.time()
        
        try:
            if component == 'system':
                self._setup_system()
            elif component == 'dependencies':
                self._install_dependencies()
            elif component == 'database':
                self._setup_database()
            elif component == 'services':
                self._setup_services()
            elif component == 'configuration':
                self._setup_configuration()
            elif component == 'application':
                self._deploy_application()
            elif component == 'monitoring':
                self._setup_monitoring()
            elif component == 'backup':
                self._setup_backup()
            elif component == 'security':
                self._setup_security()
            else:
                logger.warning(f"Unknown component: {component}")
                return
            
            execution_time = time.time() - start_time
            
            self.deployment_log.append({
                'component': component,
                'status': 'success',
                'execution_time': execution_time,
                'timestamp': datetime.now().isoformat()
            })
            
            logger.info(f"Component {component} deployed successfully in {execution_time:.2f}s")
            
        except Exception as e:
            self.deployment_log.append({
                'component': component,
                'status': 'failed',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            raise
    
    def _pre_deployment_checks(self):
        """Pre-deployment checks"""
        logger.info("Running pre-deployment checks")
        
        # Check Python version
        python_version = sys.version_info
        required_version = self.deployment_config['environments'][self.environment]['python_version']
        
        if python_version < tuple(map(int, required_version.split('.'))):
            raise RuntimeError(f"Python {required_version} or higher is required")
        
        # Check disk space
        self._check_disk_space()
        
        # Check network connectivity
        self._check_network_connectivity()
        
        # Check permissions
        self._check_permissions()
        
        logger.info("Pre-deployment checks passed")
    
    def _setup_system(self):
        """Setup system environment"""
        logger.info("Setting up system environment")
        
        # Update system packages
        if sys.platform.startswith('linux'):
            self._run_command(['sudo', 'apt-get', 'update'])
            self._run_command(['sudo', 'apt-get', 'upgrade', '-y'])
            
            # Install system dependencies
            system_packages = [
                'python3-dev', 'python3-pip', 'python3-venv',
                'build-essential', 'libssl-dev', 'libffi-dev',
                'postgresql-client', 'redis-tools',
                'git', 'curl', 'wget', 'unzip'
            ]
            
            self._run_command(['sudo', 'apt-get', 'install', '-y'] + system_packages)
        
        # Set up Python virtual environment
        venv_path = self.project_root / 'venv'
        if not venv_path.exists():
            self._run_command([sys.executable, '-m', 'venv', str(venv_path)])
        
        # Activate virtual environment
        self._activate_venv()
        
        logger.info("System environment setup completed")
    
    def _install_dependencies(self):
        """Install Python dependencies"""
        logger.info("Installing Python dependencies")
        
        # Upgrade pip
        self._run_command(['pip', 'install', '--upgrade', 'pip'])
        
        # Install requirements
        requirements_file = self.project_root / 'requirements.txt'
        if requirements_file.exists():
            self._run_command(['pip', 'install', '-r', str(requirements_file)])
        
        # Install GPU support if enabled
        if self.deployment_config['environments'][self.environment].get('install_gpu_support'):
            self._install_gpu_support()
        
        logger.info("Dependencies installed successfully")
    
    def _install_gpu_support(self):
        """Install GPU support packages"""
        logger.info("Installing GPU support packages")
        
        try:
            # Install CUDA support
            self._run_command(['pip', 'install', 'torch', 'torchvision', 'torchaudio', '--index-url', 'https://download.pytorch.org/whl/cu118'])
            
            # Install TensorFlow GPU
            self._run_command(['pip', 'install', 'tensorflow[and-cuda]'])
            
            # Install other GPU packages
            self._run_command(['pip', 'install', 'cupy-cuda11x', 'cudf-cu11', 'cuml-cu11'])
            
            logger.info("GPU support installed successfully")
            
        except Exception as e:
            logger.warning(f"GPU support installation failed: {e}")
    
    def _setup_database(self):
        """Setup database"""
        logger.info("Setting up database")
        
        if self.deployment_config['environments'][self.environment].get('database_setup'):
            # Install PostgreSQL
            if sys.platform.startswith('linux'):
                self._run_command(['sudo', 'apt-get', 'install', '-y', 'postgresql', 'postgresql-contrib'])
            
            # Initialize database
            self._run_command([sys.executable, 'database_init.py'])
            
            logger.info("Database setup completed")
    
    def _setup_services(self):
        """Setup services"""
        logger.info("Setting up services")
        
        # Setup Redis
        if self.deployment_config['environments'][self.environment].get('redis_setup'):
            self._setup_redis()
        
        # Setup Nginx
        if self.deployment_config['services'].get('nginx'):
            self._setup_nginx()
        
        logger.info("Services setup completed")
    
    def _setup_redis(self):
        """Setup Redis"""
        logger.info("Setting up Redis")
        
        if sys.platform.startswith('linux'):
            self._run_command(['sudo', 'apt-get', 'install', '-y', 'redis-server'])
            
            # Configure Redis
            redis_config = self.deployment_config['services']['redis']
            self._write_redis_config(redis_config)
            
            # Start Redis service
            self._run_command(['sudo', 'systemctl', 'start', 'redis-server'])
            self._run_command(['sudo', 'systemctl', 'enable', 'redis-server'])
    
    def _setup_nginx(self):
        """Setup Nginx"""
        logger.info("Setting up Nginx")
        
        if sys.platform.startswith('linux'):
            self._run_command(['sudo', 'apt-get', 'install', '-y', 'nginx'])
            
            # Configure Nginx
            nginx_config = self._generate_nginx_config()
            nginx_config_path = Path('/etc/nginx/sites-available/trading-bot')
            
            with open(nginx_config_path, 'w') as f:
                f.write(nginx_config)
            
            # Enable site
            self._run_command(['sudo', 'ln', '-sf', str(nginx_config_path), '/etc/nginx/sites-enabled/'])
            
            # Start Nginx
            self._run_command(['sudo', 'systemctl', 'start', 'nginx'])
            self._run_command(['sudo', 'systemctl', 'enable', 'nginx'])
    
    def _setup_configuration(self):
        """Setup configuration"""
        logger.info("Setting up configuration")
        
        # Initialize configuration manager
        from config_manager import initialize_config, Environment
        
        env_map = {
            'development': Environment.DEVELOPMENT,
            'staging': Environment.STAGING,
            'production': Environment.PRODUCTION
        }
        
        config_manager = initialize_config(env_map[self.environment])
        
        # Create configuration template
        config_manager.create_template()
        
        # Set environment-specific configuration
        self._set_environment_config(config_manager)
        
        logger.info("Configuration setup completed")
    
    def _deploy_application(self):
        """Deploy application"""
        logger.info("Deploying application")
        
        # Create systemd service file
        service_content = self._generate_systemd_service()
        service_path = Path('/etc/systemd/system/trading-bot.service')
        
        with open(service_path, 'w') as f:
            f.write(service_content)
        
        # Reload systemd
        self._run_command(['sudo', 'systemctl', 'daemon-reload'])
        
        # Enable and start service
        self._run_command(['sudo', 'systemctl', 'enable', 'trading-bot'])
        self._run_command(['sudo', 'systemctl', 'start', 'trading-bot'])
        
        logger.info("Application deployed successfully")
    
    def _setup_monitoring(self):
        """Setup monitoring"""
        logger.info("Setting up monitoring")
        
        monitoring_config = self.deployment_config.get('monitoring', {})
        
        # Setup Prometheus
        if monitoring_config.get('prometheus', {}).get('enabled'):
            self._setup_prometheus()
        
        # Setup Grafana
        if monitoring_config.get('grafana', {}).get('enabled'):
            self._setup_grafana()
        
        logger.info("Monitoring setup completed")
    
    def _setup_backup(self):
        """Setup backup system"""
        logger.info("Setting up backup system")
        
        backup_config = self.deployment_config.get('backup', {})
        
        # Create backup script
        backup_script = self._generate_backup_script()
        backup_script_path = self.project_root / 'backup.sh'
        
        with open(backup_script_path, 'w') as f:
            f.write(backup_script)
        
        # Make executable
        os.chmod(backup_script_path, 0o755)
        
        # Setup cron job
        if backup_config.get('schedule'):
            self._setup_backup_cron(backup_config['schedule'])
        
        logger.info("Backup system setup completed")
    
    def _setup_security(self):
        """Setup security hardening"""
        logger.info("Setting up security hardening")
        
        if self.deployment_config['environments'][self.environment].get('security_hardening'):
            # Setup firewall
            self._setup_firewall()
            
            # Setup SSL certificates
            self._setup_ssl()
            
            # Setup fail2ban
            self._setup_fail2ban()
            
            # Set file permissions
            self._set_file_permissions()
        
        logger.info("Security setup completed")
    
    def _post_deployment_tasks(self):
        """Post-deployment tasks"""
        logger.info("Running post-deployment tasks")
        
        # Create deployment log
        self._create_deployment_log()
        
        # Send deployment notification
        self._send_deployment_notification()
        
        # Clean up temporary files
        self._cleanup_temp_files()
        
        logger.info("Post-deployment tasks completed")
    
    def _run_health_checks(self):
        """Run health checks"""
        logger.info("Running health checks")
        
        checks = [
            self._check_application_health,
            self._check_database_health,
            self._check_redis_health,
            self._check_service_health
        ]
        
        for check in checks:
            try:
                check()
            except Exception as e:
                logger.error(f"Health check failed: {e}")
                raise
        
        logger.info("All health checks passed")
    
    def _rollback(self):
        """Rollback deployment"""
        logger.error("Rolling back deployment")
        
        try:
            # Stop services
            self._run_command(['sudo', 'systemctl', 'stop', 'trading-bot'], check=False)
            
            # Restore from backup
            self._restore_from_backup()
            
            # Restart services
            self._run_command(['sudo', 'systemctl', 'start', 'trading-bot'], check=False)
            
            logger.info("Rollback completed")
            
        except Exception as e:
            logger.error(f"Rollback failed: {e}")
    
    def _run_command(self, command: List[str], check: bool = True, capture_output: bool = False):
        """Run shell command"""
        logger.debug(f"Running command: {' '.join(command)}")
        
        try:
            result = subprocess.run(
                command,
                check=check,
                capture_output=capture_output,
                text=True,
                timeout=300
            )
            
            if capture_output:
                return result.stdout.strip()
            
            return result
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Command failed: {e}")
            if check:
                raise
            return None
        except subprocess.TimeoutExpired:
            logger.error(f"Command timed out: {' '.join(command)}")
            raise
    
    def _activate_venv(self):
        """Activate virtual environment"""
        venv_path = self.project_root / 'venv'
        
        if sys.platform.startswith('win'):
            activate_script = venv_path / 'Scripts' / 'activate.bat'
        else:
            activate_script = venv_path / 'bin' / 'activate'
        
        if activate_script.exists():
            # Update PATH for subprocess calls
            if sys.platform.startswith('win'):
                scripts_dir = venv_path / 'Scripts'
            else:
                scripts_dir = venv_path / 'bin'
            
            os.environ['PATH'] = str(scripts_dir) + os.pathsep + os.environ['PATH']
            os.environ['VIRTUAL_ENV'] = str(venv_path)
    
    # Placeholder methods for complex operations
    def _check_disk_space(self): pass
    def _check_network_connectivity(self): pass
    def _check_permissions(self): pass
    def _write_redis_config(self, config): pass
    def _generate_nginx_config(self): return ""
    def _set_environment_config(self, config_manager): pass
    def _generate_systemd_service(self): return ""
    def _setup_prometheus(self): pass
    def _setup_grafana(self): pass
    def _generate_backup_script(self): return ""
    def _setup_backup_cron(self, schedule): pass
    def _setup_firewall(self): pass
    def _setup_ssl(self): pass
    def _setup_fail2ban(self): pass
    def _set_file_permissions(self): pass
    def _create_deployment_log(self): pass
    def _send_deployment_notification(self): pass
    def _cleanup_temp_files(self): pass
    def _check_application_health(self): pass
    def _check_database_health(self): pass
    def _check_redis_health(self): pass
    def _check_service_health(self): pass
    def _restore_from_backup(self): pass


def main():
    """Main deployment function"""
    parser = argparse.ArgumentParser(description='Deploy Super-GPT Trading Bot')
    parser.add_argument('--environment', '-e', choices=['development', 'staging', 'production'], 
                       default='development', help='Deployment environment')
    parser.add_argument('--components', '-c', nargs='+', 
                       choices=['system', 'dependencies', 'database', 'services', 'configuration', 
                               'application', 'monitoring', 'backup', 'security'],
                       help='Components to deploy')
    parser.add_argument('--rollback', action='store_true', help='Rollback to previous deployment')
    parser.add_argument('--health-check', action='store_true', help='Run health checks only')
    
    args = parser.parse_args()
    
    # Initialize deployment manager
    deployment_manager = DeploymentManager(args.environment)
    
    try:
        if args.rollback:
            deployment_manager._rollback()
        elif args.health_check:
            deployment_manager._run_health_checks()
        else:
            deployment_manager.deploy(args.components)
            
    except Exception as e:
        logger.error(f"Deployment operation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
