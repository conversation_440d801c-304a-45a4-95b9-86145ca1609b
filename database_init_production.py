"""
PRODUCTION Database Setup - REAL TRADING DATA ONLY
NO SIMULATIONS - REAL MONEY OPERATIONS
"""
import sys
import logging
import yaml
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from pathlib import Path
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("PRODUCTION_DB_INIT")

def create_production_database():
    """Create production database for REAL TRADING"""
    try:
        logger.info("🚀 INITIALIZING PRODUCTION DATABASE FOR REAL TRADING")
        
        # Connect with postgres/postgres credentials
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres',
            database='postgres'
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create trading database
        db_name = 'bybit_trading_bot'
        try:
            cursor.execute(f"CREATE DATABASE {db_name}")
            logger.info(f"✅ PRODUCTION DATABASE {db_name} CREATED")
        except psycopg2.errors.DuplicateDatabase:
            logger.info(f"✅ PRODUCTION DATABASE {db_name} ALREADY EXISTS")
        
        cursor.close()
        conn.close()
        
        # Connect to trading database
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres',
            database=db_name
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create PRODUCTION tables for REAL TRADING
        production_sql = """
        -- REAL TRADES TABLE (LIVE MONEY)
        CREATE TABLE IF NOT EXISTS trades (
            id SERIAL PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            side VARCHAR(10) NOT NULL,
            size DECIMAL(18,8) NOT NULL,
            price DECIMAL(18,8) NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            strategy VARCHAR(50),
            profit_loss DECIMAL(18,8) NOT NULL,  -- REAL P&L
            fees DECIMAL(18,8) NOT NULL,         -- REAL FEES
            order_id VARCHAR(100),               -- REAL ORDER ID
            execution_type VARCHAR(20) DEFAULT 'LIVE', -- ALWAYS LIVE
            account_balance DECIMAL(18,8),       -- REAL BALANCE
            position_size DECIMAL(18,8),         -- REAL POSITION
            leverage INTEGER,                    -- REAL LEVERAGE
            margin_used DECIMAL(18,8)            -- REAL MARGIN
        );
        
        -- REAL POSITIONS TABLE (LIVE POSITIONS)
        CREATE TABLE IF NOT EXISTS positions (
            id SERIAL PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            side VARCHAR(10) NOT NULL,
            size DECIMAL(18,8) NOT NULL,
            entry_price DECIMAL(18,8) NOT NULL,
            current_price DECIMAL(18,8),
            unrealized_pnl DECIMAL(18,8) DEFAULT 0,  -- REAL UNREALIZED P&L
            realized_pnl DECIMAL(18,8) DEFAULT 0,    -- REAL REALIZED P&L
            strategy VARCHAR(50),
            stop_loss DECIMAL(18,8),
            take_profit DECIMAL(18,8),
            opened_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            closed_at TIMESTAMP,
            status VARCHAR(20) DEFAULT 'open',
            leverage INTEGER DEFAULT 1,              -- REAL LEVERAGE
            margin_required DECIMAL(18,8),           -- REAL MARGIN
            funding_fee DECIMAL(18,8) DEFAULT 0,     -- REAL FUNDING
            metadata JSONB
        );
        
        -- REAL ACCOUNT DATA
        CREATE TABLE IF NOT EXISTS account_data (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_balance DECIMAL(18,8) NOT NULL,    -- REAL BALANCE
            available_balance DECIMAL(18,8) NOT NULL, -- REAL AVAILABLE
            unrealized_pnl DECIMAL(18,8) DEFAULT 0,  -- REAL UNREALIZED
            total_pnl DECIMAL(18,8) DEFAULT 0,       -- REAL TOTAL P&L
            margin_used DECIMAL(18,8) DEFAULT 0,     -- REAL MARGIN USED
            margin_ratio DECIMAL(5,4) DEFAULT 0,     -- REAL MARGIN RATIO
            equity DECIMAL(18,8) NOT NULL,           -- REAL EQUITY
            account_type VARCHAR(20) DEFAULT 'UNIFIED' -- REAL ACCOUNT
        );
        
        -- REAL MARKET DATA
        CREATE TABLE IF NOT EXISTS market_data (
            id SERIAL PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            open_price DECIMAL(18,8) NOT NULL,       -- REAL PRICES
            high_price DECIMAL(18,8) NOT NULL,
            low_price DECIMAL(18,8) NOT NULL,
            close_price DECIMAL(18,8) NOT NULL,
            volume DECIMAL(18,8) NOT NULL,           -- REAL VOLUME
            quote_volume DECIMAL(18,8) NOT NULL,
            trade_count INTEGER DEFAULT 0,          -- REAL TRADE COUNT
            taker_buy_volume DECIMAL(18,8) DEFAULT 0,
            taker_buy_quote_volume DECIMAL(18,8) DEFAULT 0
        );
        
        -- PRODUCTION SYSTEM HEALTH
        CREATE TABLE IF NOT EXISTS system_health (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            cpu_usage DECIMAL(5,2),
            memory_usage DECIMAL(5,2),
            disk_usage DECIMAL(5,2),
            error_rate DECIMAL(5,2),
            api_latency DECIMAL(10,2),               -- REAL API LATENCY
            trading_status VARCHAR(20) DEFAULT 'ACTIVE', -- PRODUCTION STATUS
            uptime_seconds INTEGER DEFAULT 0,       -- REAL UPTIME
            component_status JSONB
        );
        
        -- REAL ERROR TRACKING
        CREATE TABLE IF NOT EXISTS error_events (
            id SERIAL PRIMARY KEY,
            event_id VARCHAR(100) UNIQUE NOT NULL,
            component VARCHAR(50) NOT NULL,
            error_type VARCHAR(100) NOT NULL,
            error_message TEXT,
            severity VARCHAR(20) NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            stack_trace TEXT,
            system_state JSONB,
            recovery_attempts INTEGER DEFAULT 0,
            resolved BOOLEAN DEFAULT FALSE,
            impact_on_trading BOOLEAN DEFAULT FALSE,  -- TRADING IMPACT
            financial_impact DECIMAL(18,8) DEFAULT 0 -- REAL $ IMPACT
        );
        
        -- PRODUCTION CONFIGURATION
        CREATE TABLE IF NOT EXISTS bot_config (
            id SERIAL PRIMARY KEY,
            config_key VARCHAR(100) UNIQUE NOT NULL,
            config_value JSONB,
            environment VARCHAR(20) DEFAULT 'PRODUCTION',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        );
        
        -- REAL PERFORMANCE METRICS
        CREATE TABLE IF NOT EXISTS performance_metrics (
            id SERIAL PRIMARY KEY,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_trades INTEGER DEFAULT 0,
            winning_trades INTEGER DEFAULT 0,
            losing_trades INTEGER DEFAULT 0,
            total_profit_loss DECIMAL(18,8) DEFAULT 0,  -- REAL P&L
            gross_profit DECIMAL(18,8) DEFAULT 0,       -- REAL GROSS
            gross_loss DECIMAL(18,8) DEFAULT 0,         -- REAL LOSS
            win_rate DECIMAL(5,2) DEFAULT 0,
            profit_factor DECIMAL(10,4) DEFAULT 0,
            sharpe_ratio DECIMAL(10,4) DEFAULT 0,
            max_drawdown DECIMAL(5,2) DEFAULT 0,        -- REAL DRAWDOWN
            max_profit DECIMAL(18,8) DEFAULT 0,         -- REAL MAX PROFIT
            average_win DECIMAL(18,8) DEFAULT 0,        -- REAL AVG WIN
            average_loss DECIMAL(18,8) DEFAULT 0,       -- REAL AVG LOSS
            trading_days INTEGER DEFAULT 0,
            roi_percentage DECIMAL(10,4) DEFAULT 0,     -- REAL ROI
            strategy_performance JSONB
        );
        
        -- REAL ORDER HISTORY
        CREATE TABLE IF NOT EXISTS order_history (
            id SERIAL PRIMARY KEY,
            order_id VARCHAR(100) UNIQUE NOT NULL,
            symbol VARCHAR(20) NOT NULL,
            side VARCHAR(10) NOT NULL,
            order_type VARCHAR(20) NOT NULL,
            quantity DECIMAL(18,8) NOT NULL,
            price DECIMAL(18,8),
            stop_price DECIMAL(18,8),
            filled_quantity DECIMAL(18,8) DEFAULT 0,
            average_price DECIMAL(18,8) DEFAULT 0,
            status VARCHAR(20) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            filled_at TIMESTAMP,
            commission DECIMAL(18,8) DEFAULT 0,         -- REAL COMMISSION
            commission_asset VARCHAR(10),
            is_live BOOLEAN DEFAULT TRUE,               -- ALWAYS LIVE
            execution_reports JSONB
        );
        """
        
        # Execute production schema
        cursor.execute(production_sql)
        
        # Create indexes for REAL DATA
        indexes_sql = """
        CREATE INDEX IF NOT EXISTS idx_trades_symbol_timestamp ON trades(symbol, timestamp);
        CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp DESC);
        CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(symbol);
        CREATE INDEX IF NOT EXISTS idx_positions_status ON positions(status);
        CREATE INDEX IF NOT EXISTS idx_market_data_symbol_timestamp ON market_data(symbol, timestamp);
        CREATE INDEX IF NOT EXISTS idx_error_events_timestamp ON error_events(timestamp DESC);
        CREATE INDEX IF NOT EXISTS idx_performance_timestamp ON performance_metrics(timestamp DESC);
        CREATE INDEX IF NOT EXISTS idx_order_history_status ON order_history(status);
        CREATE INDEX IF NOT EXISTS idx_account_data_timestamp ON account_data(timestamp DESC);
        """
        cursor.execute(indexes_sql)
        
        # Insert PRODUCTION configuration
        production_config_sql = """
        INSERT INTO bot_config (config_key, config_value, environment) VALUES
        ('system_initialized', '{"value": true, "mode": "PRODUCTION", "timestamp": "' || CURRENT_TIMESTAMP || '"}', 'PRODUCTION'),
        ('trading_enabled', '{"value": true, "mode": "LIVE_TRADING"}', 'PRODUCTION'),
        ('real_money_trading', '{"value": true, "confirmed": true}', 'PRODUCTION'),
        ('simulation_mode', '{"value": false, "disabled": true}', 'PRODUCTION'),
        ('paper_trading', '{"value": false, "disabled": true}', 'PRODUCTION'),
        ('auto_healing_enabled', '{"value": true}', 'PRODUCTION'),
        ('risk_limits', '{"max_position_size": 0.25, "max_daily_loss": 0.10, "stop_loss": 0.05}', 'PRODUCTION'),
        ('api_environment', '{"value": "PRODUCTION", "testnet": false}', 'PRODUCTION')
        ON CONFLICT (config_key) DO UPDATE SET 
            config_value = EXCLUDED.config_value,
            environment = 'PRODUCTION',
            updated_at = CURRENT_TIMESTAMP;
        """
        cursor.execute(production_config_sql)
        
        cursor.close()
        conn.close()
        
        logger.info("🔥 PRODUCTION DATABASE INITIALIZED FOR REAL TRADING!")
        logger.info("💰 READY FOR LIVE MONEY OPERATIONS!")
        logger.info("🚫 NO SIMULATIONS - REAL DATA ONLY!")
        return True
        
    except Exception as e:
        logger.error(f"❌ PRODUCTION DATABASE INITIALIZATION FAILED: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 STARTING PRODUCTION DATABASE SETUP...")
    logger.info("💰 REAL MONEY TRADING DATABASE")
    logger.info("🚫 NO SIMULATIONS ALLOWED")
    
    success = create_production_database()
    if success:
        logger.info("✅ PRODUCTION DATABASE READY FOR REAL TRADING!")
        sys.exit(0)
    else:
        logger.error("❌ PRODUCTION DATABASE SETUP FAILED!")
        sys.exit(1)
